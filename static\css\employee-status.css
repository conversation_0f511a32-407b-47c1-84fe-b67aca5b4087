/* تنسيقات خاصة بصفحات تغيير حالة الموظف */

/* بطاقات اختيار الحالة */
.status-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--bs-primary);
}

.status-card.selected {
    border-color: var(--bs-primary);
    background-color: rgba(13, 110, 253, 0.1);
}

/* أيقونات الحالات */
.status-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

/* ألوان الحالات */
.status-active { color: #28a745; }
.status-suspended { color: #ffc107; }
.status-deceased { color: #dc3545; }
.status-leave { color: #17a2b8; }
.status-assigned { color: #6f42c1; }
.status-resigned { color: #6c757d; }
.status-transferred { color: #fd7e14; }
.status-retired { color: #343a40; }

/* تأثيرات الانتقال */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيق النماذج */
.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* تنسيق التنبيهات */
.alert-custom {
    border-left: 4px solid;
    border-radius: 0 0.375rem 0.375rem 0;
}

.alert-custom.alert-danger {
    border-left-color: #dc3545;
}

.alert-custom.alert-warning {
    border-left-color: #ffc107;
}

.alert-custom.alert-info {
    border-left-color: #0dcaf0;
}

.alert-custom.alert-success {
    border-left-color: #198754;
}

/* تنسيق معاينة البيانات */
.preview-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.preview-box h6 {
    color: rgba(255,255,255,0.9);
    margin-bottom: 0.5rem;
}

.preview-box .value {
    font-size: 1.1rem;
    font-weight: 600;
}

/* تنسيق الأزرار */
.btn-status {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-status:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* تنسيق الجداول */
.table-status {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table-status thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.table-status tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

/* تنسيق البطاقات المعلوماتية */
.info-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.info-card .card-header {
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

/* تنسيق المؤشرات */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تنسيق responsive */
@media (max-width: 768px) {
    .status-card {
        margin-bottom: 1rem;
    }
    
    .btn-status {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .preview-box {
        margin: 0.5rem 0;
        padding: 1rem;
    }
}

/* تنسيق الحالات في القوائم */
.status-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.status-badge.active { background-color: #d4edda; color: #155724; }
.status-badge.suspended { background-color: #fff3cd; color: #856404; }
.status-badge.deceased { background-color: #f8d7da; color: #721c24; }
.status-badge.leave { background-color: #d1ecf1; color: #0c5460; }
.status-badge.assigned { background-color: #e2e3f1; color: #383d41; }
.status-badge.resigned { background-color: #e2e3e5; color: #383d41; }
.status-badge.transferred { background-color: #ffeaa7; color: #856404; }
.status-badge.retired { background-color: #d6d8db; color: #383d41; }

/* تأثيرات خاصة للمودالات */
.modal-status .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-status .modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-status .modal-body {
    padding: 1.5rem;
}

.modal-status .modal-footer {
    border-top: none;
    padding: 1.5rem;
    border-radius: 0 0 15px 15px;
}

/* تنسيق حقول التاريخ */
.date-input-group {
    position: relative;
}

.date-input-group .form-control {
    padding-right: 2.5rem;
}

.date-input-group .date-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
}

/* تنسيق معاينة المدة */
.duration-preview {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    margin: 1rem 0;
}

.duration-preview .duration-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.duration-preview .duration-text {
    font-size: 0.9rem;
    opacity: 0.9;
}