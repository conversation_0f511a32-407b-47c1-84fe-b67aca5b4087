#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام إدارة موظفي الجمارك الجزائرية
Quick Start for Algerian Customs Employee Management System
"""

import os
import sys

def main():
    """تشغيل سريع للنظام"""
    print("🚀 تشغيل سريع - نظام إدارة موظفي الجمارك الجزائرية")
    print("=" * 55)
    
    # التأكد من وجود قاعدة البيانات
    if not os.path.exists('customs_employees.db'):
        print("🔧 إنشاء قاعدة البيانات...")
        try:
            import init_database
            print("✅ تم إنشاء قاعدة البيانات")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return
    
    # تشغيل التطبيق
    try:
        print("🌐 تشغيل الخادم على http://localhost:5000")
        print("👋 اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 55)
        
        import app
        app.app.run(debug=False, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بنجاح")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()