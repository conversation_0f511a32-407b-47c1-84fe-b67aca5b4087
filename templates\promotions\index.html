{% extends "base.html" %}

{% block title %}الترقيات - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة الترقيات{% endblock %}

{% block content %}
<!-- إحصائيات -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <i class="fas fa-arrow-up fa-2x mb-2"></i>
            <h3>{{ rank_promotions|length }}</h3>
            <p>ترقيات الرتب</p>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stats-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <i class="fas fa-level-up-alt fa-2x mb-2"></i>
            <h3>{{ grade_promotions|length }}</h3>
            <p>ترقيات الدرجات</p>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>إضافة ترقية جديدة
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="addPromotion('rank')">
                    <i class="fas fa-arrow-up me-2"></i>ترقية في الرتبة
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="addPromotion('grade')">
                    <i class="fas fa-level-up-alt me-2"></i>ترقية في الدرجة
                </a></li>
            </ul>
        </div>
        <button class="btn btn-success" onclick="exportData()">
            <i class="fas fa-file-excel me-2"></i>تصدير البيانات
        </button>
    </div>
</div>

<!-- ترقيات الرتب -->
<div class="card mb-4">
    <div class="card-header">
        <h4><i class="fas fa-arrow-up me-2"></i>ترقيات الرتب</h4>
    </div>
    <div class="card-body">
        {% if rank_promotions %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>من الرتبة</th>
                        <th>إلى الرتبة</th>
                        <th>نوع الترقية</th>
                        <th>تاريخ المقرر</th>
                        <th>تاريخ السريان</th>
                        <th>رقم المقرر</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for promotion in rank_promotions %}
                    <tr>
                        <td>
                            <strong>{{ promotion.first_name }} {{ promotion.last_name }}</strong>
                            <br><small class="text-muted">{{ promotion.registration_number }}</small>
                        </td>
                        <td>{{ promotion.from_rank_name or 'غير محدد' }}</td>
                        <td>{{ promotion.to_rank_name or 'غير محدد' }}</td>
                        <td>{{ promotion.promotion_type_name or 'غير محدد' }}</td>
                        <td>{{ promotion.decision_date or 'غير محدد' }}</td>
                        <td>{{ promotion.effective_date or 'غير محدد' }}</td>
                        <td>{{ promotion.decision_number or 'غير محدد' }}</td>
                        <td>
                            {% if promotion.status == 'نشط' %}
                                <span class="badge bg-success">{{ promotion.status }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ promotion.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-arrow-up fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد ترقيات رتب مسجلة</h5>
            <p class="text-muted">ابدأ بإضافة ترقية رتبة جديدة لعرض البيانات هنا</p>
            <button class="btn btn-primary" onclick="addPromotion('rank')">
                <i class="fas fa-plus me-2"></i>إضافة ترقية رتبة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- ترقيات الدرجات -->
<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-level-up-alt me-2"></i>ترقيات الدرجات</h4>
    </div>
    <div class="card-body">
        {% if grade_promotions %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>من الدرجة</th>
                        <th>إلى الدرجة</th>
                        <th>تاريخ المقرر</th>
                        <th>تاريخ السريان</th>
                        <th>رقم المقرر</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for promotion in grade_promotions %}
                    <tr>
                        <td>
                            <strong>{{ promotion.first_name }} {{ promotion.last_name }}</strong>
                            <br><small class="text-muted">{{ promotion.registration_number }}</small>
                        </td>
                        <td>{{ promotion.from_grade }}</td>
                        <td>{{ promotion.to_grade }}</td>
                        <td>{{ promotion.decision_date or 'غير محدد' }}</td>
                        <td>{{ promotion.effective_date or 'غير محدد' }}</td>
                        <td>{{ promotion.decision_number or 'غير محدد' }}</td>
                        <td>
                            {% if promotion.status == 'نشط' %}
                                <span class="badge bg-success">{{ promotion.status }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ promotion.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-level-up-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد ترقيات درجات مسجلة</h5>
            <p class="text-muted">ابدأ بإضافة ترقية درجة جديدة لعرض البيانات هنا</p>
            <button class="btn btn-info" onclick="addPromotion('grade')">
                <i class="fas fa-plus me-2"></i>إضافة ترقية درجة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal إضافة ترقية -->
<div class="modal fade" id="promotionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promotionModalTitle">
                    <i class="fas fa-arrow-up me-2"></i>إضافة ترقية جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="promotionForm">
                <div class="modal-body" id="promotionModalBody">
                    <!-- سيتم ملء المحتوى ديناميكياً حسب نوع الترقية -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="promotionSubmitBtn">حفظ الترقية</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addPromotion(type) {
    const modal = new bootstrap.Modal(document.getElementById('promotionModal'));
    const modalTitle = document.getElementById('promotionModalTitle');
    const modalBody = document.getElementById('promotionModalBody');
    const submitBtn = document.getElementById('promotionSubmitBtn');
    
    let title = '';
    let content = '';
    let btnText = 'حفظ الترقية';
    let btnClass = 'btn-primary';
    
    if (type === 'rank') {
        title = '<i class="fas fa-arrow-up me-2"></i>إضافة ترقية في الرتبة';
        btnText = 'حفظ ترقية الرتبة';
        btnClass = 'btn-success';
        content = `
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الموظف <span class="required">*</span></label>
                        <select name="employee_id" class="form-select" required>
                            <option value="">اختر الموظف</option>
                            <!-- سيتم ملؤها ديناميكياً -->
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">نوع الترقية <span class="required">*</span></label>
                        <select name="promotion_type_id" class="form-select" required>
                            <option value="">اختر النوع</option>
                            <option value="1">ترقية عادية</option>
                            <option value="2">ترقية استثنائية</option>
                            <option value="3">ترقية بالاختيار</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">من الرتبة</label>
                        <select name="from_rank_id" class="form-select">
                            <option value="">اختر الرتبة الحالية</option>
                            <!-- سيتم ملؤها من قاعدة البيانات -->
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">إلى الرتبة <span class="required">*</span></label>
                        <select name="to_rank_id" class="form-select" required>
                            <option value="">اختر الرتبة الجديدة</option>
                            <!-- سيتم ملؤها من قاعدة البيانات -->
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ المقرر <span class="required">*</span></label>
                        <input type="date" name="decision_date" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ السريان <span class="required">*</span></label>
                        <input type="date" name="effective_date" class="form-control" required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم المقرر</label>
                        <input type="text" name="decision_number" class="form-control" placeholder="رقم مقرر الترقية">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="نشط" selected>نشط</option>
                            <option value="ملغي">ملغي</option>
                            <option value="مؤجل">مؤجل</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="2" placeholder="ملاحظات حول الترقية"></textarea>
            </div>
        `;
    } else if (type === 'grade') {
        title = '<i class="fas fa-level-up-alt me-2"></i>إضافة ترقية في الدرجة';
        btnText = 'حفظ ترقية الدرجة';
        btnClass = 'btn-info';
        content = `
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الموظف <span class="required">*</span></label>
                        <select name="employee_id" class="form-select" required>
                            <option value="">اختر الموظف</option>
                            <!-- سيتم ملؤها ديناميكياً -->
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الدرجة الحالية</label>
                        <input type="number" name="from_grade" class="form-control" min="1" max="15" placeholder="الدرجة الحالية">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الدرجة الجديدة <span class="required">*</span></label>
                        <input type="number" name="to_grade" class="form-control" min="1" max="15" required placeholder="الدرجة الجديدة">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ المقرر <span class="required">*</span></label>
                        <input type="date" name="decision_date" class="form-control" required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ السريان <span class="required">*</span></label>
                        <input type="date" name="effective_date" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم المقرر</label>
                        <input type="text" name="decision_number" class="form-control" placeholder="رقم مقرر الترقية">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="نشط" selected>نشط</option>
                            <option value="ملغي">ملغي</option>
                            <option value="مؤجل">مؤجل</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="2" placeholder="ملاحظات حول الترقية"></textarea>
            </div>
        `;
    }
    
    modalTitle.innerHTML = title;
    modalBody.innerHTML = content;
    submitBtn.textContent = btnText;
    submitBtn.className = `btn ${btnClass}`;
    
    modal.show();
}

function exportData() {
    alert('سيتم تصدير بيانات الترقيات إلى Excel');
}

// معالجة النموذج
document.getElementById('promotionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ الترقية بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('promotionModal')).hide();
});
</script>
{% endblock %}
