{% extends "base.html" %}

{% block title %}تقارير حالات الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        تقارير حالات الموظفين
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_dashboard') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- أنواع التقارير -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-calendar-alt"></i>
                                        التقارير الشهرية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>تقارير مفصلة للحالات خلال شهر معين</p>
                                    <form method="GET" action="{{ url_for('generate_monthly_report') }}">
                                        <div class="form-group">
                                            <label for="report_year">السنة</label>
                                            <select class="form-control" id="report_year" name="year" required>
                                                {% for year in range(2020, 2030) %}
                                                <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="report_month">الشهر</label>
                                            <select class="form-control" id="report_month" name="month" required>
                                                <option value="1" {% if current_month == 1 %}selected{% endif %}>يناير</option>
                                                <option value="2" {% if current_month == 2 %}selected{% endif %}>فبراير</option>
                                                <option value="3" {% if current_month == 3 %}selected{% endif %}>مارس</option>
                                                <option value="4" {% if current_month == 4 %}selected{% endif %}>أبريل</option>
                                                <option value="5" {% if current_month == 5 %}selected{% endif %}>مايو</option>
                                                <option value="6" {% if current_month == 6 %}selected{% endif %}>يونيو</option>
                                                <option value="7" {% if current_month == 7 %}selected{% endif %}>يوليو</option>
                                                <option value="8" {% if current_month == 8 %}selected{% endif %}>أغسطس</option>
                                                <option value="9" {% if current_month == 9 %}selected{% endif %}>سبتمبر</option>
                                                <option value="10" {% if current_month == 10 %}selected{% endif %}>أكتوبر</option>
                                                <option value="11" {% if current_month == 11 %}selected{% endif %}>نوفمبر</option>
                                                <option value="12" {% if current_month == 12 %}selected{% endif %}>ديسمبر</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary btn-block">
                                            <i class="fas fa-file-alt"></i> إنشاء التقرير
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-calendar"></i>
                                        التقارير السنوية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>تقارير شاملة للحالات خلال سنة كاملة</p>
                                    <form method="GET" action="{{ url_for('generate_annual_report') }}">
                                        <div class="form-group">
                                            <label for="annual_year">السنة</label>
                                            <select class="form-control" id="annual_year" name="year" required>
                                                {% for year in range(2020, 2030) %}
                                                <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-success btn-block">
                                            <i class="fas fa-chart-line"></i> إنشاء التقرير
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-building"></i>
                                        تقارير الأقسام
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>تقارير حسب القسم أو الإدارة</p>
                                    <form method="GET" action="{{ url_for('generate_department_report') }}">
                                        <div class="form-group">
                                            <label for="department">القسم</label>
                                            <select class="form-control" id="department" name="department">
                                                <option value="">جميع الأقسام</option>
                                                {% for dept in departments %}
                                                <option value="{{ dept }}">{{ dept }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-users"></i> إنشاء التقرير
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقارير سريعة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <i class="fas fa-tachometer-alt"></i>
                                        تقارير سريعة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="{{ url_for('quick_report', type='current_month') }}" class="btn btn-outline-primary btn-block">
                                                <i class="fas fa-calendar-day"></i><br>
                                                تقرير الشهر الحالي
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ url_for('quick_report', type='last_30_days') }}" class="btn btn-outline-success btn-block">
                                                <i class="fas fa-clock"></i><br>
                                                آخر 30 يوم
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ url_for('quick_report', type='pending_actions') }}" class="btn btn-outline-warning btn-block">
                                                <i class="fas fa-hourglass-half"></i><br>
                                                الإجراءات المعلقة
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{{ url_for('quick_report', type='upcoming_events') }}" class="btn btn-outline-info btn-block">
                                                <i class="fas fa-calendar-plus"></i><br>
                                                الأحداث القادمة
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تصدير البيانات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <i class="fas fa-download"></i>
                                        تصدير البيانات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>نوع البيانات</label>
                                                <select class="form-control" id="export_type">
                                                    <option value="long_term_leaves">العطل طويلة الأمد</option>
                                                    <option value="resignations">الاستقالات</option>
                                                    <option value="retirements">التقاعدات</option>
                                                    <option value="external_transfers">التحويلات الخارجية</option>
                                                    <option value="suspensions">التوقيفات</option>
                                                    <option value="assignments">الانتدابات</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>تنسيق التصدير</label>
                                                <select class="form-control" id="export_format">
                                                    <option value="csv">CSV</option>
                                                    <option value="json">JSON</option>
                                                    <option value="excel">Excel</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>&nbsp;</label>
                                                <button type="button" class="btn btn-success btn-block" onclick="exportData()">
                                                    <i class="fas fa-file-export"></i> تصدير البيانات
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-calendar-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">العطل النشطة</span>
                                    <span class="info-box-number">{{ quick_stats.active_leaves or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-user-minus"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">الاستقالات المعلقة</span>
                                    <span class="info-box-number">{{ quick_stats.pending_resignations or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-ban"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">التوقيفات النشطة</span>
                                    <span class="info-box-number">{{ quick_stats.active_suspensions or 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-briefcase"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">الانتدابات النشطة</span>
                                    <span class="info-box-number">{{ quick_stats.active_assignments or 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportData() {
    const exportType = document.getElementById('export_type').value;
    const exportFormat = document.getElementById('export_format').value;
    
    // إنشاء رابط التصدير
    const url = `/api/export_status_data?type=${exportType}&format=${exportFormat}`;
    
    // فتح الرابط في نافذة جديدة
    window.open(url, '_blank');
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
    fetch('/api/employee_status/quick_stats')
        .then(response => response.json())
        .then(data => {
            // تحديث الأرقام
            document.querySelector('.info-box.bg-info .info-box-number').textContent = data.active_leaves || 0;
            document.querySelector('.info-box.bg-warning .info-box-number').textContent = data.pending_resignations || 0;
            document.querySelector('.info-box.bg-danger .info-box-number').textContent = data.active_suspensions || 0;
            document.querySelector('.info-box.bg-success .info-box-number').textContent = data.active_assignments || 0;
        })
        .catch(error => console.error('خطأ في تحديث الإحصائيات:', error));
}, 30000);

// تعيين التاريخ الحالي كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    
    // تعيين السنة والشهر الحاليين
    document.getElementById('report_year').value = currentYear;
    document.getElementById('report_month').value = currentMonth;
    document.getElementById('annual_year').value = currentYear;
});
</script>
{% endblock %}