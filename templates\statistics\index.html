{% extends "base.html" %}

{% block title %}الإحصائيات والتقارير - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}الإحصائيات والتقارير{% endblock %}

{% block content %}
<!-- إحصائيات عامة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);">
            <i class="fas fa-users fa-2x mb-2"></i>
            <h3>{{ stats.total_employees }}</h3>
            <p>إجمالي الموظفين</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);">
            <i class="fas fa-user-check fa-2x mb-2"></i>
            <h3>{{ stats.active_employees }}</h3>
            <p>الموظفين النشطين</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
            <h3>{{ stats.total_leaves }}</h3>
            <p>إجمالي العطل</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
            <i class="fas fa-arrow-up fa-2x mb-2"></i>
            <h3>{{ stats.total_promotions }}</h3>
            <p>إجمالي الترقيات</p>
        </div>
    </div>
</div>

<!-- أزرار التصدير والطباعة -->
<div class="card mb-4">
    <div class="card-body">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download me-2"></i>تصدير التقارير
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportReport('employees')">
                    <i class="fas fa-users me-2"></i>تقرير الموظفين
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportReport('leaves')">
                    <i class="fas fa-calendar-alt me-2"></i>تقرير العطل
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportReport('promotions')">
                    <i class="fas fa-arrow-up me-2"></i>تقرير الترقيات
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportReport('comprehensive')">
                    <i class="fas fa-file-alt me-2"></i>تقرير شامل
                </a></li>
            </ul>
        </div>
        <button class="btn btn-primary" onclick="printReport()">
            <i class="fas fa-print me-2"></i>طباعة التقرير
        </button>
        <button class="btn btn-info" onclick="refreshStats()">
            <i class="fas fa-sync-alt me-2"></i>تحديث الإحصائيات
        </button>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row mb-4">
    <!-- توزيع الموظفين حسب الجنس -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>توزيع الموظفين حسب الجنس</h5>
            </div>
            <div class="card-body">
                <div style="position: relative; height: 300px;">
                    <canvas id="genderChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- توزيع الموظفين حسب الأسلاك -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>توزيع الموظفين حسب الأسلاك</h5>
            </div>
            <div class="card-body">
                <div style="position: relative; height: 300px;">
                    <canvas id="corpsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات العطل -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>إحصائيات العطل الشهرية</h5>
            </div>
            <div class="card-body">
                <div style="position: relative; height: 300px;">
                    <canvas id="leavesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-percentage me-2"></i>نسب أنواع العطل</h5>
            </div>
            <div class="card-body">
                <div style="position: relative; height: 250px;">
                    <canvas id="leaveTypesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جداول الإحصائيات التفصيلية -->
<div class="row">
    <!-- إحصائيات الموظفين حسب المديريات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-building me-2"></i>توزيع الموظفين حسب المديريات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>المديرية</th>
                                <th>عدد الموظفين</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in stats.employees_by_directorate %}
                            <tr>
                                <td>{{ item.directorate_name or 'غير محدد' }}</td>
                                <td>{{ item.count }}</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: {{ (item.count / stats.total_employees * 100)|round(1) }}%">
                                            {{ (item.count / stats.total_employees * 100)|round(1) }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات الترقيات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-arrow-up me-2"></i>إحصائيات الترقيات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>نوع الترقية</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in stats.promotions_by_type %}
                            <tr>
                                <td>{{ item.promotion_type or 'غير محدد' }}</td>
                                <td>{{ item.count }}</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ (item.count / stats.total_promotions * 100)|round(1) if stats.total_promotions > 0 else 0 }}%">
                                            {{ (item.count / stats.total_promotions * 100)|round(1) if stats.total_promotions > 0 else 0 }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات العمر والخبرة -->
<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-birthday-cake me-2"></i>توزيع الأعمار</h5>
            </div>
            <div class="card-body">
                <div style="position: relative; height: 250px;">
                    <canvas id="ageChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock me-2"></i>سنوات الخبرة</h5>
            </div>
            <div class="card-body">
                <div style="position: relative; height: 250px;">
                    <canvas id="experienceChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-heart me-2"></i>الحالة العائلية</h5>
            </div>
            <div class="card-body">
                <div style="position: relative; height: 250px;">
                    <canvas id="maritalChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-tachometer-alt me-2"></i>إحصائيات سريعة</h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-2">
                <div class="border-end">
                    <h4 class="text-primary">{{ stats.avg_age|round(1) }}</h4>
                    <small class="text-muted">متوسط العمر</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="border-end">
                    <h4 class="text-success">{{ stats.avg_experience|round(1) }}</h4>
                    <small class="text-muted">متوسط الخبرة</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="border-end">
                    <h4 class="text-warning">{{ stats.total_certificates }}</h4>
                    <small class="text-muted">إجمالي الشهادات</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="border-end">
                    <h4 class="text-info">{{ stats.total_training }}</h4>
                    <small class="text-muted">إجمالي التكوين</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="border-end">
                    <h4 class="text-danger">{{ stats.total_sanctions }}</h4>
                    <small class="text-muted">إجمالي العقوبات</small>
                </div>
            </div>
            <div class="col-md-2">
                <h4 class="text-success">{{ stats.total_rewards }}</h4>
                <small class="text-muted">إجمالي المكافآت</small>
            </div>
        </div>
    </div>
</div>

<!-- قسم التقارير -->
<div class="card mt-5">
    <div class="card-header">
        <h4><i class="fas fa-file-alt me-2"></i>التقارير والطباعة</h4>
    </div>
    <div class="card-body">
        <!-- أنواع التقارير -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card" onclick="generateReport('employees')">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                        <h6>تقرير الموظفين</h6>
                        <p class="text-muted small">تقرير شامل لجميع الموظفين</p>
                        <span class="badge bg-primary">{{ stats.total_employees }} موظف</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card" onclick="generateReport('leaves')">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
                        <h6>تقرير العطل</h6>
                        <p class="text-muted small">العطل السنوية والمرضية</p>
                        <span class="badge bg-warning">{{ stats.total_leaves }} عطلة</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card" onclick="generateReport('promotions')">
                    <div class="card-body text-center">
                        <i class="fas fa-arrow-up fa-3x text-success mb-3"></i>
                        <h6>تقرير الترقيات</h6>
                        <p class="text-muted small">ترقيات الرتب والدرجات</p>
                        <span class="badge bg-success">{{ stats.total_promotions }} ترقية</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card" onclick="generateReport('comprehensive')">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                        <h6>التقرير الشامل</h6>
                        <p class="text-muted small">جميع بيانات النظام</p>
                        <span class="badge bg-info">شامل</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- خيارات التقرير -->
        <div class="card bg-light">
            <div class="card-header">
                <h6><i class="fas fa-filter me-2"></i>خيارات التقرير</h6>
            </div>
            <div class="card-body">
                <form id="reportOptionsForm">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">نوع التقرير</label>
                                <select name="report_type" class="form-select" id="reportType">
                                    <option value="employees">تقرير الموظفين</option>
                                    <option value="leaves">تقرير العطل</option>
                                    <option value="promotions">تقرير الترقيات</option>
                                    <option value="sanctions">تقرير العقوبات والمكافآت</option>
                                    <option value="transfers">تقرير التنقلات</option>
                                    <option value="comprehensive">التقرير الشامل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="start_date" class="form-control" id="startDate">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="end_date" class="form-control" id="endDate">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">تنسيق التصدير</label>
                                <select name="export_format" class="form-select">
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="word">Word</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-primary me-2" onclick="generateCustomReport()">
                            <i class="fas fa-file-alt me-2"></i>إنشاء التقرير
                        </button>
                        <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                            <i class="fas fa-download me-2"></i>تصدير
                        </button>
                        <button type="button" class="btn btn-info" onclick="printReport()">
                            <i class="fas fa-print me-2"></i>طباعة
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- تقارير سريعة -->
        <div class="mt-4">
            <h6><i class="fas fa-bolt me-2"></i>تقارير سريعة</h6>
            <div class="row">
                <div class="col-md-4 mb-2">
                    <button class="btn btn-outline-primary btn-sm w-100" onclick="quickReport('active_employees')">
                        <i class="fas fa-users me-2"></i>الموظفين النشطين
                    </button>
                </div>
                <div class="col-md-4 mb-2">
                    <button class="btn btn-outline-warning btn-sm w-100" onclick="quickReport('current_leaves')">
                        <i class="fas fa-calendar-times me-2"></i>العطل الحالية
                    </button>
                </div>
                <div class="col-md-4 mb-2">
                    <button class="btn btn-outline-success btn-sm w-100" onclick="quickReport('recent_promotions')">
                        <i class="fas fa-arrow-up me-2"></i>الترقيات الأخيرة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معاينة التقرير -->
<div class="card mt-4" id="reportPreview" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6><i class="fas fa-eye me-2"></i>معاينة التقرير</h6>
        <div>
            <button class="btn btn-sm btn-outline-primary" onclick="refreshPreview()">
                <i class="fas fa-sync-alt me-2"></i>تحديث
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="closePreview()">
                <i class="fas fa-times me-2"></i>إغلاق
            </button>
        </div>
    </div>
    <div class="card-body" id="reportContent">
        <!-- سيتم ملء محتوى التقرير هنا -->
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* تحسين عرض الرسوم البيانية */
.card-body canvas {
    max-height: 300px !important;
}

.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    color: white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card i {
    opacity: 0.9;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
}

.stats-card p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
}

/* تحسين الجداول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.progress {
    height: 20px;
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-header h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

/* تحسين الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* تحسين الإحصائيات السريعة */
.border-end {
    border-right: 2px solid #dee2e6 !important;
}

.border-end:last-child {
    border-right: none !important;
}

/* تجاوب الرسوم البيانية */
@media (max-width: 768px) {
    .card-body canvas {
        max-height: 250px !important;
    }

    .stats-card h3 {
        font-size: 2rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .card-body canvas {
        max-height: 200px !important;
    }

    .stats-card h3 {
        font-size: 1.8rem;
    }
}

/* تحسين بطاقات التقارير */
.report-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    height: 100%;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.report-card:hover .card-body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.report-card .card-body {
    padding: 1.5rem 1rem;
}

.report-card h6 {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.report-card .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

#reportPreview {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media print {
    .card-header, .btn, .navbar, .sidebar, #reportOptionsForm, .report-card {
        display: none !important;
    }

    #reportContent {
        padding: 0 !important;
        margin: 0 !important;
    }
}
</style>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// بيانات الرسوم البيانية من الخادم
const chartData = {
    gender: {{ stats.gender_distribution|tojson }},
    corps: {{ stats.corps_distribution|tojson }},
    leaves: {{ stats.monthly_leaves|tojson }},
    leaveTypes: {{ stats.leave_types_distribution|tojson }},
    age: {{ stats.age_distribution|tojson }},
    experience: {{ stats.experience_distribution|tojson }},
    marital: {{ stats.marital_distribution|tojson }}
};

// رسم بياني للجنس
const genderCtx = document.getElementById('genderChart').getContext('2d');
new Chart(genderCtx, {
    type: 'doughnut',
    data: {
        labels: chartData.gender.map(item => item.gender),
        datasets: [{
            data: chartData.gender.map(item => item.count),
            backgroundColor: ['#007bff', '#dc3545'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true,
                    font: {
                        size: 12
                    }
                }
            }
        }
    }
});

// رسم بياني للأسلاك
const corpsCtx = document.getElementById('corpsChart').getContext('2d');
new Chart(corpsCtx, {
    type: 'bar',
    data: {
        labels: chartData.corps.map(item => item.corps_name),
        datasets: [{
            label: 'عدد الموظفين',
            data: chartData.corps.map(item => item.count),
            backgroundColor: '#28a745',
            borderColor: '#1e7e34',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    font: {
                        size: 11
                    }
                }
            },
            x: {
                ticks: {
                    font: {
                        size: 11
                    },
                    maxRotation: 45
                }
            }
        }
    }
});

// رسم بياني للعطل الشهرية
const leavesCtx = document.getElementById('leavesChart').getContext('2d');
new Chart(leavesCtx, {
    type: 'line',
    data: {
        labels: chartData.leaves.map(item => item.month),
        datasets: [{
            label: 'العطل السنوية',
            data: chartData.leaves.map(item => item.annual),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }, {
            label: 'العطل المرضية',
            data: chartData.leaves.map(item => item.sick),
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.4
        }, {
            label: 'العطل الأخرى',
            data: chartData.leaves.map(item => item.other),
            borderColor: '#ffc107',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    padding: 10,
                    usePointStyle: true,
                    font: {
                        size: 11
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    font: {
                        size: 10
                    }
                }
            },
            x: {
                ticks: {
                    font: {
                        size: 10
                    }
                }
            }
        }
    }
});

// رسم بياني لأنواع العطل
const leaveTypesCtx = document.getElementById('leaveTypesChart').getContext('2d');
new Chart(leaveTypesCtx, {
    type: 'pie',
    data: {
        labels: chartData.leaveTypes.map(item => item.type),
        datasets: [{
            data: chartData.leaveTypes.map(item => item.count),
            backgroundColor: ['#007bff', '#dc3545', '#ffc107', '#28a745'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 10,
                    usePointStyle: true,
                    font: {
                        size: 10
                    }
                }
            }
        }
    }
});

// رسم بياني للأعمار
const ageCtx = document.getElementById('ageChart').getContext('2d');
new Chart(ageCtx, {
    type: 'bar',
    data: {
        labels: chartData.age.map(item => item.range),
        datasets: [{
            label: 'عدد الموظفين',
            data: chartData.age.map(item => item.count),
            backgroundColor: '#17a2b8',
            borderColor: '#138496',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    font: {
                        size: 11
                    }
                }
            },
            x: {
                ticks: {
                    font: {
                        size: 11
                    }
                }
            }
        }
    }
});

// رسم بياني للخبرة
const experienceCtx = document.getElementById('experienceChart').getContext('2d');
new Chart(experienceCtx, {
    type: 'bar',
    data: {
        labels: chartData.experience.map(item => item.range),
        datasets: [{
            label: 'عدد الموظفين',
            data: chartData.experience.map(item => item.count),
            backgroundColor: '#6f42c1',
            borderColor: '#5a2d91',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    font: {
                        size: 11
                    }
                }
            },
            x: {
                ticks: {
                    font: {
                        size: 11
                    }
                }
            }
        }
    }
});

// رسم بياني للحالة العائلية
const maritalCtx = document.getElementById('maritalChart').getContext('2d');
new Chart(maritalCtx, {
    type: 'doughnut',
    data: {
        labels: chartData.marital.map(item => item.status),
        datasets: [{
            data: chartData.marital.map(item => item.count),
            backgroundColor: ['#28a745', '#007bff', '#ffc107', '#dc3545'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 10,
                    usePointStyle: true,
                    font: {
                        size: 10
                    }
                }
            }
        }
    }
});

// وظائف التصدير والطباعة
function exportReport(type) {
    alert(`سيتم تصدير تقرير ${type} إلى Excel`);
    // يمكن إضافة وظيفة التصدير الفعلية
}

function printReport() {
    window.print();
}

function refreshStats() {
    location.reload();
}

// وظائف التقارير
function generateReport(type) {
    document.getElementById('reportType').value = type;
    generateCustomReport();
}

function generateCustomReport() {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    // إظهار معاينة التقرير
    const preview = document.getElementById('reportPreview');
    const content = document.getElementById('reportContent');

    preview.style.display = 'block';
    content.innerHTML = '<div class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i><p>جاري إنشاء التقرير...</p></div>';

    // التمرير إلى معاينة التقرير
    preview.scrollIntoView({ behavior: 'smooth' });

    // محاكاة تحميل التقرير
    setTimeout(() => {
        let reportContent = generateReportContent(reportType, startDate, endDate);
        content.innerHTML = reportContent;
    }, 1500);
}

function generateReportContent(type, startDate, endDate) {
    const currentDate = new Date().toLocaleDateString('ar-DZ');

    let content = `
        <div class="report-header text-center mb-4">
            <h2>الجمهورية الجزائرية الديمقراطية الشعبية</h2>
            <h3>وزارة المالية - مديرية الجمارك</h3>
            <hr>
            <h4>${getReportTitle(type)}</h4>
            <p class="text-muted">تاريخ الإنشاء: ${currentDate}</p>
            ${startDate && endDate ? `<p class="text-muted">الفترة: من ${startDate} إلى ${endDate}</p>` : ''}
        </div>
    `;

    switch(type) {
        case 'employees':
            content += generateEmployeesReport();
            break;
        case 'leaves':
            content += generateLeavesReport();
            break;
        case 'promotions':
            content += generatePromotionsReport();
            break;
        case 'comprehensive':
            content += generateComprehensiveReport();
            break;
        default:
            content += '<p class="text-center">نوع التقرير غير مدعوم</p>';
    }

    content += `
        <div class="report-footer mt-5 pt-3 border-top">
            <div class="row">
                <div class="col-6">
                    <p><strong>المدير:</strong> ________________</p>
                    <p><strong>التوقيع:</strong></p>
                </div>
                <div class="col-6 text-end">
                    <p><strong>تاريخ الطباعة:</strong> ${currentDate}</p>
                    <p><strong>رقم الصفحة:</strong> 1</p>
                </div>
            </div>
        </div>
    `;

    return content;
}

function getReportTitle(type) {
    const titles = {
        'employees': 'تقرير الموظفين',
        'leaves': 'تقرير العطل والإجازات',
        'promotions': 'تقرير الترقيات',
        'sanctions': 'تقرير العقوبات والمكافآت',
        'transfers': 'تقرير التنقلات والحركات',
        'comprehensive': 'التقرير الشامل'
    };
    return titles[type] || 'تقرير عام';
}

function generateEmployeesReport() {
    return `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>إجمالي الموظفين</h5>
                        <h3 class="text-primary">{{ stats.total_employees }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>الموظفين النشطين</h5>
                        <h3 class="text-success">{{ stats.active_employees }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>متوسط العمر</h5>
                        <h3 class="text-info">{{ stats.avg_age|round(1) }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>رقم التسجيل</th>
                        <th>الاسم الكامل</th>
                        <th>السلك</th>
                        <th>الرتبة</th>
                        <th>المديرية</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center text-muted">سيتم ملء البيانات من قاعدة البيانات</td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

function generateLeavesReport() {
    return `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>العطل السنوية</h5>
                        <h3 class="text-primary">{{ stats.annual_leaves or 0 }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>العطل المرضية</h5>
                        <h3 class="text-warning">{{ stats.sick_leaves or 0 }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>العطل الأخرى</h5>
                        <h3 class="text-info">{{ stats.other_leaves or 0 }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع العطلة</th>
                        <th>تاريخ البداية</th>
                        <th>عدد الأيام</th>
                        <th>تاريخ النهاية</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5" class="text-center text-muted">سيتم ملء البيانات من قاعدة البيانات</td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

function generatePromotionsReport() {
    return `
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع الترقية</th>
                        <th>من</th>
                        <th>إلى</th>
                        <th>تاريخ السريان</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5" class="text-center text-muted">سيتم ملء البيانات من قاعدة البيانات</td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

function generateComprehensiveReport() {
    return `
        <div class="row mb-4">
            <div class="col-12">
                <h5>ملخص عام</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>الموظفين</h6>
                                <h4 class="text-primary">{{ stats.total_employees }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>العطل</h6>
                                <h4 class="text-warning">{{ stats.total_leaves }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>الترقيات</h6>
                                <h4 class="text-success">{{ stats.total_promotions }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>الشهادات</h6>
                                <h4 class="text-info">{{ stats.total_certificates }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function quickReport(type) {
    const reportTypes = {
        'active_employees': 'employees',
        'current_leaves': 'leaves',
        'recent_promotions': 'promotions'
    };

    generateReport(reportTypes[type] || 'employees');
}

function exportReport() {
    const format = document.querySelector('select[name="export_format"]').value;
    alert(`سيتم تصدير التقرير بصيغة ${format.toUpperCase()}`);
}

function printReport() {
    window.print();
}

function refreshPreview() {
    generateCustomReport();
}

function closePreview() {
    document.getElementById('reportPreview').style.display = 'none';
}

// تحديد تاريخ اليوم كقيمة افتراضية
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('endDate').value = today;

    // تحديد بداية الشهر
    const firstDay = new Date();
    firstDay.setDate(1);
    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
});
</script>
{% endblock %}
