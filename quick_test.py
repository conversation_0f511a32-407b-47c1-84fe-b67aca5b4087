#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام
"""

import sys
import os

def test_flask():
    """اختبار Flask"""
    try:
        import flask
        print(f"✅ Flask متوفر - الإصدار: {flask.__version__}")
        return True
    except ImportError:
        print("❌ Flask غير مثبت")
        return False

def test_files():
    """اختبار الملفات"""
    files = [
        'simple_main.py',
        'templates/base.html',
        'templates/index.html',
        'templates/employee_statuses/index.html'
    ]
    
    missing = []
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing.append(file)
    
    return missing

def test_database():
    """اختبار قاعدة البيانات"""
    if os.path.exists('customs_employees.db'):
        print("✅ قاعدة البيانات موجودة")
        return True
    else:
        print("⚠️  قاعدة البيانات غير موجودة")
        return False

def main():
    print("🔍 اختبار سريع للنظام")
    print("="*40)
    
    flask_ok = test_flask()
    missing_files = test_files()
    db_ok = test_database()
    
    print("\n📋 النتيجة:")
    if flask_ok and not missing_files:
        print("✅ النظام جاهز للتشغيل")
        print("🚀 شغل: python simple_main.py")
    else:
        print("❌ توجد مشاكل:")
        if not flask_ok:
            print("   - Flask غير متوفر")
        if missing_files:
            print(f"   - ملفات مفقودة: {missing_files}")

if __name__ == "__main__":
    main()
