#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للنظام
Simple System Test
"""

import sqlite3
from simple_status_system import SimpleStatusManager
from complete_status_transfers import CompleteStatusTransfers

def test_system():
    """اختبار النظام بشكل مبسط"""
    print("🧪 اختبار النظام الكامل")
    print("=" * 50)
    
    # 1. اختبار قاعدة البيانات
    print("1️⃣  اختبار قاعدة البيانات...")
    try:
        conn = sqlite3.connect('customs_employees.db')
        
        # عدد الموظفين
        total_employees = conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0]
        print(f"   📊 إجمالي الموظفين: {total_employees}")
        
        # عدد المستودعين
        leave_absence = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'مستودع'").fetchone()[0]
        print(f"   📊 المستودعين: {leave_absence}")
        
        # عدد النشطين
        active = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'نشط'").fetchone()[0]
        print(f"   📊 النشطين: {active}")
        
        conn.close()
        print("   ✅ قاعدة البيانات تعمل بشكل صحيح")
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
    
    # 2. اختبار مدير الحالات
    print("\n2️⃣  اختبار مدير الحالات...")
    try:
        status_manager = SimpleStatusManager()
        stats = status_manager.get_statistics()
        
        print(f"   📊 النشطين: {stats.get('active', 0)}")
        print(f"   📊 المستودعين: {stats.get('leave_of_absence', 0)}")
        print(f"   📊 المتوفين: {stats.get('deceased', 0)}")
        print(f"   📊 المحولين خارجياً: {stats.get('external_transfer', 0)}")
        print(f"   📊 إجمالي النشطين: {stats.get('total_active', 0)}")
        print(f"   📊 إجمالي المحذوفين: {stats.get('total_removed', 0)}")
        
        print("   ✅ مدير الحالات يعمل بشكل صحيح")
        
    except Exception as e:
        print(f"   ❌ خطأ في مدير الحالات: {e}")
    
    # 3. اختبار مدير التحويلات
    print("\n3️⃣  اختبار مدير التحويلات...")
    try:
        transfer_manager = CompleteStatusTransfers()
        
        # عدد المحولين خارجياً
        external_transfers = len(transfer_manager.get_external_transfers())
        print(f"   📊 المحولين خارجياً: {external_transfers}")
        
        # عدد المعزولين
        dismissed = len(transfer_manager.get_dismissed_employees())
        print(f"   📊 المعزولين: {dismissed}")
        
        # عدد المتقاعدين
        retired = len(transfer_manager.get_retired_employees())
        print(f"   📊 المتقاعدين: {retired}")
        
        print("   ✅ مدير التحويلات يعمل بشكل صحيح")
        
    except Exception as e:
        print(f"   ❌ خطأ في مدير التحويلات: {e}")
    
    # 4. اختبار رصيد الاستيداع
    print("\n4️⃣  اختبار رصيد الاستيداع...")
    try:
        conn = sqlite3.connect('customs_employees.db')
        employee = conn.execute('SELECT id, first_name, last_name FROM employees LIMIT 1').fetchone()
        conn.close()
        
        if employee:
            employee_id, first_name, last_name = employee
            balance = status_manager.get_leave_balance(employee_id)
            
            if 'error' not in balance:
                print(f"   👤 الموظف: {first_name} {last_name}")
                print(f"   📊 الرصيد المتبقي: {balance['remaining_months']} شهر")
                print(f"   📊 المستخدم: {balance['used_months']} شهر")
                print("   ✅ حساب رصيد الاستيداع يعمل")
            else:
                print(f"   ❌ خطأ في حساب الرصيد: {balance['error']}")
        else:
            print("   ⚠️  لا يوجد موظفين لاختبار الرصيد")
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الرصيد: {e}")
    
    print(f"\n🎉 انتهى الاختبار!")
    
    # عرض الملخص النهائي
    print(f"\n🌟 ملخص النظام:")
    print(f"   ✅ الحالات النهائية: تُحذف من جدول الموظفين")
    print(f"   ✅ الحالات المؤقتة: تبقى في جدول الموظفين")
    print(f"   ✅ حفظ البيانات: جميع البيانات محفوظة في JSON")
    print(f"   ✅ عدم الاحتساب: المحذوفين لا يُحسبون في العدد")
    print(f"   ✅ الوصول للمعلومات: من جداول الحالات")
    
    print(f"\n🚀 النظام جاهز للاستخدام!")
    print(f"   python app.py")
    print(f"   ثم افتح: http://localhost:5000/")

if __name__ == "__main__":
    test_system()