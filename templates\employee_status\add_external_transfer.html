{% extends "base.html" %}

{% block title %}إضافة تحويل خارجي - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        تسجيل تحويل خارجي
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user"></i> معلومات الموظف</h5>
                        <strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}<br>
                        <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                        <strong>الحالة الحالية:</strong> <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير مهم:</strong> التحويل الخارجي يعني خروج الموظف من تعداد المؤسسة نهائياً. 
                        الموظف لن يظهر في الإحصائيات العادية وسيتم نقله إلى قائمة التحويلات الخارجية المنفصلة.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="transfer_date">تاريخ التحويل <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="transfer_date" name="transfer_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ التحويل
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">تاريخ القرار <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="decision_date" name="decision_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ القرار
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="destination_organization">الجهة المحول إليها <span class="text-danger">*</span></label>
                                    <select class="form-control" id="destination_org_select" onchange="handleOrgChange()">
                                        <option value="">اختر الجهة</option>
                                        <option value="وزارة المالية">وزارة المالية</option>
                                        <option value="وزارة الداخلية">وزارة الداخلية</option>
                                        <option value="وزارة العدل">وزارة العدل</option>
                                        <option value="وزارة التربية الوطنية">وزارة التربية الوطنية</option>
                                        <option value="وزارة الصحة">وزارة الصحة</option>
                                        <option value="وزارة النقل">وزارة النقل</option>
                                        <option value="وزارة الطاقة">وزارة الطاقة</option>
                                        <option value="وزارة التجارة">وزارة التجارة</option>
                                        <option value="وزارة الصناعة">وزارة الصناعة</option>
                                        <option value="ولاية">ولاية</option>
                                        <option value="بلدية">بلدية</option>
                                        <option value="مؤسسة عمومية">مؤسسة عمومية</option>
                                        <option value="أخرى">أخرى (حدد)</option>
                                    </select>
                                    <input type="text" class="form-control mt-2" id="destination_organization" name="destination_organization" 
                                           required placeholder="اسم الجهة المحول إليها">
                                    <div class="invalid-feedback">
                                        يرجى إدخال الجهة المحول إليها
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="decision_number">رقم القرار <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                           required placeholder="مثال: 2024/TR/001">
                                    <div class="invalid-feedback">
                                        يرجى إدخال رقم القرار
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="destination_department">القسم أو المصلحة المحول إليها</label>
                            <input type="text" class="form-control" id="destination_department" name="destination_department" 
                                   placeholder="مثال: مديرية الموارد البشرية">
                            <small class="form-text text-muted">اختياري - يمكن تحديده لاحقاً</small>
                        </div>

                        <div class="form-group">
                            <label for="reason">سبب التحويل <span class="text-danger">*</span></label>
                            <select class="form-control" id="reason_select" onchange="handleReasonChange()">
                                <option value="">اختر سبب التحويل</option>
                                <option value="إعادة هيكلة إدارية">إعادة هيكلة إدارية</option>
                                <option value="طلب شخصي من الموظف">طلب شخصي من الموظف</option>
                                <option value="ضرورة الخدمة">ضرورة الخدمة</option>
                                <option value="تخصص مطلوب في الجهة الأخرى">تخصص مطلوب في الجهة الأخرى</option>
                                <option value="قرار إداري">قرار إداري</option>
                                <option value="ظروف عائلية">ظروف عائلية</option>
                                <option value="أخرى">أخرى (حدد)</option>
                            </select>
                            <textarea class="form-control mt-2" id="reason" name="reason" rows="3" required 
                                      placeholder="اذكر سبب التحويل بالتفصيل"></textarea>
                            <div class="invalid-feedback">
                                يرجى إدخال سبب التحويل
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                      placeholder="أي ملاحظات إضافية حول التحويل (اختياري)"></textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_external" required>
                                <label class="custom-control-label" for="confirm_external">
                                    <strong>أؤكد أن هذا تحويل خارجي وأن الموظف سيخرج من تعداد المؤسسة نهائياً</strong>
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد طبيعة التحويل الخارجي
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_data" required>
                                <label class="custom-control-label" for="confirm_data">
                                    أؤكد صحة جميع البيانات المدخلة
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد صحة البيانات
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-exchange-alt"></i> تسجيل التحويل الخارجي
                            </button>
                            <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// معالجة تغيير الجهة
function handleOrgChange() {
    var select = document.getElementById('destination_org_select');
    var input = document.getElementById('destination_organization');
    
    if (select.value && select.value !== 'أخرى') {
        input.value = select.value;
    } else if (select.value === 'أخرى') {
        input.value = '';
        input.focus();
    }
}

// معالجة تغيير سبب التحويل
function handleReasonChange() {
    var select = document.getElementById('reason_select');
    var textarea = document.getElementById('reason');
    
    if (select.value && select.value !== 'أخرى') {
        textarea.value = select.value;
    } else if (select.value === 'أخرى') {
        textarea.value = '';
        textarea.focus();
    }
}

// التحقق من التواريخ
document.getElementById('decision_date').addEventListener('change', function() {
    var decisionDate = new Date(this.value);
    var transferDate = document.getElementById('transfer_date').value;
    
    if (transferDate && decisionDate > new Date(transferDate)) {
        alert('تاريخ القرار لا يمكن أن يكون بعد تاريخ التحويل');
        this.value = '';
    }
});

document.getElementById('transfer_date').addEventListener('change', function() {
    var transferDate = new Date(this.value);
    var decisionDate = document.getElementById('decision_date').value;
    
    if (decisionDate && new Date(decisionDate) > transferDate) {
        alert('تاريخ التحويل لا يمكن أن يكون قبل تاريخ القرار');
        this.value = '';
    }
});

// تأكيد إضافي قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد من تسجيل هذا التحويل الخارجي؟ هذا الإجراء سينقل الموظف خارج تعداد المؤسسة نهائياً.')) {
        e.preventDefault();
    }
});

// تعيين تاريخ اليوم كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    var today = new Date().toISOString().split('T')[0];
    document.getElementById('transfer_date').value = today;
    document.getElementById('decision_date').value = today;
});
</script>
{% endblock %}