{% extends "base.html" %}

{% block title %}قائمة الموظفين المتقاعدين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-check"></i>
                        قائمة الموظفين المتقاعدين
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> معلومات:</h6>
                                <p class="mb-0">إجمالي الموظفين المتقاعدين: <strong>{{ retired|length }}</strong></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> ملاحظة:</h6>
                                <p class="mb-0">هؤلاء الموظفون لا يُحسبون في إجمالي عدد الموظفين النشطين</p>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ url_for('simple_status.dashboard') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button class="btn btn-success" onclick="window.print()">
                                <i class="fas fa-print"></i>
                                طباعة القائمة
                            </button>
                        </div>
                    </div>

                    {% if retired %}
                    <!-- جدول المتقاعدين -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-success">
                                <tr>
                                    <th>#</th>
                                    <th>رقم التسجيل</th>
                                    <th>الاسم الكامل</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>تاريخ التقاعد</th>
                                    <th>سنوات الخدمة</th>
                                    <th>رقم مقرر التقاعد</th>
                                    <th>رقم بطاقة التقاعد</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for person in retired %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <span class="badge badge-secondary">
                                            {{ person.registration_number }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ person.first_name }} {{ person.last_name }}</strong>
                                    </td>
                                    <td>
                                        {% if person.hire_date %}
                                            {{ person.hire_date }}
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-success">
                                            {{ person.retirement_date }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if person.hire_date and person.retirement_date %}
                                            <strong>{{ (person.retirement_date|strptime('%Y-%m-%d')).year - (person.hire_date|strptime('%Y-%m-%d')).year }} سنة</strong>
                                        {% else %}
                                            <span class="text-muted">غير محسوب</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ person.retirement_decision_number }}</code>
                                    </td>
                                    <td>
                                        {% if person.retirement_card_number %}
                                            <span class="badge badge-info">{{ person.retirement_card_number }}</span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info" 
                                                onclick="viewEmployeeData({{ person.id }}, '{{ person.first_name }} {{ person.last_name }}')">
                                            <i class="fas fa-eye"></i>
                                            عرض البيانات
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <!-- رسالة عدم وجود بيانات -->
                    <div class="text-center py-5">
                        <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد موظفون متقاعدون</h5>
                        <p class="text-muted">لم يتم تسجيل أي حالة تقاعد حتى الآن</p>
                        <a href="{{ url_for('employees') }}" class="btn btn-primary">
                            <i class="fas fa-users"></i>
                            عرض قائمة الموظفين
                        </a>
                    </div>
                    {% endif %}

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض بيانات الموظف -->
<div class="modal fade" id="employeeDataModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user"></i>
                    بيانات الموظف المتقاعد
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="employeeDataContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>جاري تحميل البيانات...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function viewEmployeeData(retiredId, employeeName) {
    $('#employeeDataModal').modal('show');
    
    $('#employeeDataModal .modal-title').html(`
        <i class="fas fa-user"></i>
        بيانات الموظف المتقاعد: ${employeeName}
    `);
    
    fetch(`/final_status/api/employee_data/retired_employees/${retiredId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                $('#employeeDataContent').html(`
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> خطأ:</h6>
                        <p class="mb-0">${data.error}</p>
                    </div>
                `);
            } else {
                let content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">المعلومات الشخصية:</h6>
                            <p><strong>الاسم:</strong> ${data.first_name} ${data.last_name}</p>
                            <p><strong>رقم التسجيل:</strong> ${data.registration_number}</p>
                            <p><strong>تاريخ الميلاد:</strong> ${data.birth_date || 'غير محدد'}</p>
                            <p><strong>الجنس:</strong> ${data.gender || 'غير محدد'}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">المعلومات الوظيفية:</h6>
                            <p><strong>تاريخ التوظيف:</strong> ${data.hire_date || 'غير محدد'}</p>
                            <p><strong>الرتبة:</strong> ${data.rank_id || 'غير محدد'}</p>
                            <p><strong>السلك:</strong> ${data.corps_id || 'غير محدد'}</p>
                            <p><strong>المصلحة:</strong> ${data.service_id || 'غير محدد'}</p>
                        </div>
                    </div>
                `;
                
                $('#employeeDataContent').html(content);
            }
        })
        .catch(error => {
            $('#employeeDataContent').html(`
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> خطأ في الشبكة:</h6>
                    <p class="mb-0">لا يمكن تحميل البيانات حالياً</p>
                </div>
            `);
        });
}
</script>
{% endblock %}