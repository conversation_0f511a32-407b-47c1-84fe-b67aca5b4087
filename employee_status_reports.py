#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة التقارير لنظام إدارة حالات الموظفين
Reports module for Employee Status Management System
"""

import sqlite3
import json
import csv
import io
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from employee_status_helpers import EmployeeStatusHelpers, format_arabic_date

class EmployeeStatusReports:
    """كلاس إنشاء التقارير"""
    
    def __init__(self, db_path: str = 'employees.db'):
        self.db_path = db_path
        self.helpers = EmployeeStatusHelpers(db_path)
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def generate_monthly_report(self, year: int, month: int) -> Dict[str, Any]:
        """تقرير شهري للحالات"""
        start_date = f"{year}-{month:02d}-01"
        
        # حساب آخر يوم في الشهر
        if month == 12:
            next_month = f"{year + 1}-01-01"
        else:
            next_month = f"{year}-{month + 1:02d}-01"
        
        end_date = (datetime.strptime(next_month, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')
        
        conn = self.get_db_connection()
        
        try:
            report = {
                'period': {
                    'year': year,
                    'month': month,
                    'start_date': start_date,
                    'end_date': end_date,
                    'month_name': self._get_arabic_month_name(month)
                },
                'summary': {},
                'details': {},
                'comparisons': {}
            }
            
            # إحصائيات العطل الجديدة
            new_leaves = conn.execute('''
                SELECT COUNT(*) as count, leave_type
                FROM employee_long_term_leaves 
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY leave_type
            ''', (start_date, end_date)).fetchall()
            
            report['details']['new_long_term_leaves'] = {
                'total': sum(row['count'] for row in new_leaves),
                'by_type': [{'type': row['leave_type'], 'count': row['count']} for row in new_leaves]
            }
            
            # العطل المنتهية
            ended_leaves = conn.execute('''
                SELECT COUNT(*) as count, leave_type
                FROM employee_long_term_leaves 
                WHERE DATE(end_date) BETWEEN ? AND ?
                GROUP BY leave_type
            ''', (start_date, end_date)).fetchall()
            
            report['details']['ended_long_term_leaves'] = {
                'total': sum(row['count'] for row in ended_leaves),
                'by_type': [{'type': row['leave_type'], 'count': row['count']} for row in ended_leaves]
            }
            
            # الاستقالات الجديدة
            new_resignations = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_resignations 
                WHERE DATE(created_at) BETWEEN ? AND ?
            ''', (start_date, end_date)).fetchone()
            
            report['summary']['new_resignations'] = new_resignations['count'] if new_resignations else 0
            
            # التقاعدات الجديدة
            new_retirements = conn.execute('''
                SELECT COUNT(*) as count, retirement_type
                FROM employee_retirements 
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY retirement_type
            ''', (start_date, end_date)).fetchall()
            
            report['details']['new_retirements'] = {
                'total': sum(row['count'] for row in new_retirements),
                'by_type': [{'type': row['retirement_type'], 'count': row['count']} for row in new_retirements]
            }
            
            # التحويلات الخارجية
            new_transfers = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_external_transfers 
                WHERE DATE(created_at) BETWEEN ? AND ?
            ''', (start_date, end_date)).fetchone()
            
            report['summary']['new_external_transfers'] = new_transfers['count'] if new_transfers else 0
            
            # الوفيات
            new_deaths = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_deaths 
                WHERE DATE(created_at) BETWEEN ? AND ?
            ''', (start_date, end_date)).fetchone()
            
            report['summary']['new_deaths'] = new_deaths['count'] if new_deaths else 0
            
            # مقارنة مع الشهر السابق
            prev_month = month - 1 if month > 1 else 12
            prev_year = year if month > 1 else year - 1
            prev_report = self.generate_monthly_report(prev_year, prev_month)
            
            report['comparisons'] = {
                'resignations_change': report['summary']['new_resignations'] - prev_report['summary']['new_resignations'],
                'transfers_change': report['summary']['new_external_transfers'] - prev_report['summary']['new_external_transfers'],
                'retirements_change': report['details']['new_retirements']['total'] - prev_report['details']['new_retirements']['total']
            }
            
            return report
            
        finally:
            conn.close()
    
    def generate_annual_report(self, year: int) -> Dict[str, Any]:
        """تقرير سنوي للحالات"""
        start_date = f"{year}-01-01"
        end_date = f"{year}-12-31"
        
        conn = self.get_db_connection()
        
        try:
            report = {
                'year': year,
                'period': {'start_date': start_date, 'end_date': end_date},
                'summary': {},
                'monthly_breakdown': {},
                'trends': {},
                'top_statistics': {}
            }
            
            # الإحصائيات السنوية الإجمالية
            annual_stats = {}
            
            # العطل طويلة الأمد
            leaves_count = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_long_term_leaves 
                WHERE strftime('%Y', created_at) = ?
            ''', (str(year),)).fetchone()
            annual_stats['long_term_leaves'] = leaves_count['count'] if leaves_count else 0
            
            # الاستقالات
            resignations_count = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_resignations 
                WHERE strftime('%Y', created_at) = ?
            ''', (str(year),)).fetchone()
            annual_stats['resignations'] = resignations_count['count'] if resignations_count else 0
            
            # التقاعدات
            retirements_count = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_retirements 
                WHERE strftime('%Y', created_at) = ?
            ''', (str(year),)).fetchone()
            annual_stats['retirements'] = retirements_count['count'] if retirements_count else 0
            
            # التحويلات الخارجية
            transfers_count = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_external_transfers 
                WHERE strftime('%Y', created_at) = ?
            ''', (str(year),)).fetchone()
            annual_stats['external_transfers'] = transfers_count['count'] if transfers_count else 0
            
            report['summary'] = annual_stats
            
            # التفصيل الشهري
            monthly_data = {}
            for month in range(1, 13):
                monthly_report = self.generate_monthly_report(year, month)
                monthly_data[month] = {
                    'month_name': monthly_report['period']['month_name'],
                    'resignations': monthly_report['summary']['new_resignations'],
                    'transfers': monthly_report['summary']['new_external_transfers'],
                    'retirements': monthly_report['details']['new_retirements']['total'],
                    'leaves': monthly_report['details']['new_long_term_leaves']['total']
                }
            
            report['monthly_breakdown'] = monthly_data
            
            # أكثر أنواع العطل شيوعاً
            top_leave_types = conn.execute('''
                SELECT leave_type, COUNT(*) as count
                FROM employee_long_term_leaves 
                WHERE strftime('%Y', created_at) = ?
                GROUP BY leave_type
                ORDER BY count DESC
                LIMIT 5
            ''', (str(year),)).fetchall()
            
            report['top_statistics']['top_leave_types'] = [
                {'type': row['leave_type'], 'count': row['count']} 
                for row in top_leave_types
            ]
            
            # أكثر أنواع التقاعد شيوعاً
            top_retirement_types = conn.execute('''
                SELECT retirement_type, COUNT(*) as count
                FROM employee_retirements 
                WHERE strftime('%Y', created_at) = ?
                GROUP BY retirement_type
                ORDER BY count DESC
            ''', (str(year),)).fetchall()
            
            report['top_statistics']['top_retirement_types'] = [
                {'type': row['retirement_type'], 'count': row['count']} 
                for row in top_retirement_types
            ]
            
            return report
            
        finally:
            conn.close()
    
    def generate_department_report(self, department: str = None) -> Dict[str, Any]:
        """تقرير حسب القسم"""
        conn = self.get_db_connection()
        
        try:
            report = {
                'department': department or 'جميع الأقسام',
                'employee_count': {},
                'status_breakdown': {},
                'recent_changes': {}
            }
            
            # بناء شرط القسم
            dept_condition = "WHERE e.department = ?" if department else ""
            dept_params = [department] if department else []
            
            # عدد الموظفين حسب الحالة
            status_counts = conn.execute(f'''
                SELECT status, COUNT(*) as count
                FROM employees e
                {dept_condition}
                GROUP BY status
            ''', dept_params).fetchall()
            
            report['status_breakdown'] = {
                row['status']: row['count'] for row in status_counts
            }
            
            # إجمالي الموظفين
            total_employees = sum(report['status_breakdown'].values())
            report['employee_count']['total'] = total_employees
            report['employee_count']['active'] = report['status_breakdown'].get('نشط', 0)
            
            # التغييرات الأخيرة (آخر 30 يوم)
            recent_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            # العطل الجديدة
            recent_leaves = conn.execute(f'''
                SELECT COUNT(*) as count
                FROM employee_long_term_leaves l
                JOIN employees e ON l.employee_id = e.id
                {dept_condition.replace('e.', 'e.') if dept_condition else ''}
                {'AND' if dept_condition else 'WHERE'} l.created_at >= ?
            ''', dept_params + [recent_date]).fetchone()
            
            report['recent_changes']['new_leaves'] = recent_leaves['count'] if recent_leaves else 0
            
            # الاستقالات الجديدة
            recent_resignations = conn.execute(f'''
                SELECT COUNT(*) as count
                FROM employee_resignations r
                JOIN employees e ON r.employee_id = e.id
                {dept_condition.replace('e.', 'e.') if dept_condition else ''}
                {'AND' if dept_condition else 'WHERE'} r.created_at >= ?
            ''', dept_params + [recent_date]).fetchone()
            
            report['recent_changes']['new_resignations'] = recent_resignations['count'] if recent_resignations else 0
            
            return report
            
        finally:
            conn.close()
    
    def generate_employee_status_timeline(self, employee_id: int) -> Dict[str, Any]:
        """تقرير تسلسل زمني لحالات موظف"""
        conn = self.get_db_connection()
        
        try:
            # معلومات الموظف
            employee = conn.execute('''
                SELECT * FROM employees WHERE id = ?
            ''', (employee_id,)).fetchone()
            
            if not employee:
                return {'error': 'الموظف غير موجود'}
            
            timeline = []
            
            # العطل طويلة الأمد
            leaves = conn.execute('''
                SELECT 'عطلة طويلة الأمد' as event_type, leave_type as details,
                       start_date as event_date, created_at, end_date,
                       CASE WHEN end_date IS NULL THEN 'نشطة' ELSE 'منتهية' END as status
                FROM employee_long_term_leaves 
                WHERE employee_id = ?
                ORDER BY created_at
            ''', (employee_id,)).fetchall()
            
            for leave in leaves:
                timeline.append(dict(leave))
            
            # الاستقالات
            resignations = conn.execute('''
                SELECT 'استقالة' as event_type, reason as details,
                       resignation_date as event_date, created_at, 
                       last_working_day as end_date, status
                FROM employee_resignations 
                WHERE employee_id = ?
                ORDER BY created_at
            ''', (employee_id,)).fetchall()
            
            for resignation in resignations:
                timeline.append(dict(resignation))
            
            # التقاعدات
            retirements = conn.execute('''
                SELECT 'تقاعد' as event_type, retirement_type as details,
                       retirement_date as event_date, created_at,
                       NULL as end_date, 'نهائي' as status
                FROM employee_retirements 
                WHERE employee_id = ?
                ORDER BY created_at
            ''', (employee_id,)).fetchall()
            
            for retirement in retirements:
                timeline.append(dict(retirement))
            
            # التحويلات الخارجية
            transfers = conn.execute('''
                SELECT 'تحويل خارجي' as event_type, destination_organization as details,
                       transfer_date as event_date, created_at,
                       NULL as end_date, 'نهائي' as status
                FROM employee_external_transfers 
                WHERE employee_id = ?
                ORDER BY created_at
            ''', (employee_id,)).fetchall()
            
            for transfer in transfers:
                timeline.append(dict(transfer))
            
            # ترتيب التسلسل الزمني
            timeline.sort(key=lambda x: x['created_at'])
            
            return {
                'employee': dict(employee),
                'timeline': timeline,
                'summary': {
                    'total_events': len(timeline),
                    'active_statuses': len([t for t in timeline if t['status'] not in ['منتهية', 'نهائي']])
                }
            }
            
        finally:
            conn.close()
    
    def export_report_to_csv(self, report_data: Dict[str, Any], report_type: str) -> str:
        """تصدير التقرير إلى CSV"""
        output = io.StringIO()
        
        if report_type == 'monthly':
            # تصدير التقرير الشهري
            writer = csv.writer(output)
            
            # العنوان
            writer.writerow([f"تقرير شهري - {report_data['period']['month_name']} {report_data['period']['year']}"])
            writer.writerow([])
            
            # الملخص
            writer.writerow(['الملخص'])
            writer.writerow(['الاستقالات الجديدة', report_data['summary']['new_resignations']])
            writer.writerow(['التحويلات الخارجية', report_data['summary']['new_external_transfers']])
            writer.writerow(['الوفيات', report_data['summary']['new_deaths']])
            writer.writerow([])
            
            # العطل الجديدة
            writer.writerow(['العطل طويلة الأمد الجديدة'])
            writer.writerow(['نوع العطلة', 'العدد'])
            for leave_type in report_data['details']['new_long_term_leaves']['by_type']:
                writer.writerow([leave_type['type'], leave_type['count']])
            
        elif report_type == 'annual':
            # تصدير التقرير السنوي
            writer = csv.writer(output)
            
            writer.writerow([f"تقرير سنوي - {report_data['year']}"])
            writer.writerow([])
            
            # الملخص السنوي
            writer.writerow(['الإحصائيات السنوية'])
            writer.writerow(['العطل طويلة الأمد', report_data['summary']['long_term_leaves']])
            writer.writerow(['الاستقالات', report_data['summary']['resignations']])
            writer.writerow(['التقاعدات', report_data['summary']['retirements']])
            writer.writerow(['التحويلات الخارجية', report_data['summary']['external_transfers']])
            writer.writerow([])
            
            # التفصيل الشهري
            writer.writerow(['التفصيل الشهري'])
            writer.writerow(['الشهر', 'الاستقالات', 'التحويلات', 'التقاعدات', 'العطل'])
            for month, data in report_data['monthly_breakdown'].items():
                writer.writerow([
                    data['month_name'],
                    data['resignations'],
                    data['transfers'],
                    data['retirements'],
                    data['leaves']
                ])
        
        return output.getvalue()
    
    def _get_arabic_month_name(self, month: int) -> str:
        """الحصول على اسم الشهر بالعربية"""
        months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        return months[month - 1] if 1 <= month <= 12 else 'غير محدد'

def create_reports_manager(db_path: str = 'employees.db') -> EmployeeStatusReports:
    """إنشاء مدير التقارير"""
    return EmployeeStatusReports(db_path)

if __name__ == "__main__":
    # اختبار التقارير
    reports = create_reports_manager()
    
    print("📊 اختبار وحدة التقارير...")
    
    # تقرير شهري
    current_date = datetime.now()
    monthly_report = reports.generate_monthly_report(current_date.year, current_date.month)
    print(f"✅ تقرير شهري: {monthly_report['period']['month_name']} {monthly_report['period']['year']}")
    
    # تقرير سنوي
    annual_report = reports.generate_annual_report(current_date.year)
    print(f"✅ تقرير سنوي: {annual_report['year']}")
    
    # تقرير قسم
    dept_report = reports.generate_department_report()
    print(f"✅ تقرير الأقسام: {dept_report['employee_count']['total']} موظف")
    
    print("📈 انتهى اختبار التقارير")