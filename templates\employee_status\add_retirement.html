{% extends "base.html" %}

{% block title %}إضافة تقاعد - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-clock"></i>
                        تسجيل تقاعد
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user"></i> معلومات الموظف</h5>
                        <strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}<br>
                        <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                        <strong>تاريخ الميلاد:</strong> {{ employee.birth_date|format_arabic_date if employee.birth_date }}<br>
                        <strong>تاريخ التوظيف:</strong> {{ employee.hire_date|format_arabic_date if employee.hire_date }}<br>
                        <strong>الحالة الحالية:</strong> <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> تسجيل التقاعد سيغير حالة الموظف إلى "متقاعد" نهائياً.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="retirement_date">تاريخ التقاعد <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="retirement_date" name="retirement_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ التقاعد
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="retirement_type">نوع التقاعد <span class="text-danger">*</span></label>
                                    <select class="form-control" id="retirement_type" name="retirement_type" required>
                                        <option value="">اختر نوع التقاعد</option>
                                        <option value="تقاعد عادي">تقاعد عادي (بلوغ السن القانونية)</option>
                                        <option value="تقاعد مبكر">تقاعد مبكر</option>
                                        <option value="تقاعد بدون شرط السن">تقاعد بدون شرط السن</option>
                                        <option value="تقاعد لأسباب صحية">تقاعد لأسباب صحية</option>
                                        <option value="تقاعد نسبي">تقاعد نسبي</option>
                                        <option value="تقاعد استثنائي">تقاعد استثنائي</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار نوع التقاعد
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="years_of_service">سنوات الخدمة <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="years_of_service" name="years_of_service" 
                                           min="1" max="50" required>
                                    <small class="form-text text-muted">سيتم حسابها تلقائياً إذا تركت فارغة</small>
                                    <div class="invalid-feedback">
                                        يرجى إدخال سنوات الخدمة
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="pension_amount">مبلغ المعاش (دج)</label>
                                    <input type="number" class="form-control" id="pension_amount" name="pension_amount" 
                                           min="0" step="0.01" placeholder="0.00">
                                    <small class="form-text text-muted">اختياري - يمكن تحديده لاحقاً</small>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="decision_date">تاريخ القرار</label>
                                    <input type="date" class="form-control" id="decision_date" name="decision_date">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">رقم القرار</label>
                                    <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                           placeholder="مثال: 2024/RET/001">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="أي ملاحظات حول التقاعد (اختياري)"></textarea>
                        </div>

                        <!-- معلومات محسوبة -->
                        <div class="card bg-light mb-3" id="calculated_info" style="display: none;">
                            <div class="card-header">
                                <h6 class="card-title">معلومات محسوبة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>العمر عند التقاعد:</strong>
                                        <span id="retirement_age">-</span> سنة
                                    </div>
                                    <div class="col-md-4">
                                        <strong>سنوات الخدمة المحسوبة:</strong>
                                        <span id="calculated_service">-</span> سنة
                                    </div>
                                    <div class="col-md-4">
                                        <strong>نوع التقاعد المقترح:</strong>
                                        <span id="suggested_type">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm" required>
                                <label class="custom-control-label" for="confirm">
                                    أؤكد صحة البيانات المدخلة وأن هذا تقاعد رسمي
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد صحة البيانات
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> تسجيل التقاعد
                            </button>
                            <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات الموظف من الخادم
const employeeData = {
    birthDate: '{{ employee.birth_date }}',
    hireDate: '{{ employee.hire_date }}'
};

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// حساب المعلومات عند تغيير تاريخ التقاعد
document.getElementById('retirement_date').addEventListener('change', calculateRetirementInfo);

function calculateRetirementInfo() {
    const retirementDate = document.getElementById('retirement_date').value;
    
    if (!retirementDate || !employeeData.birthDate || !employeeData.hireDate) {
        return;
    }
    
    const retirement = new Date(retirementDate);
    const birth = new Date(employeeData.birthDate);
    const hire = new Date(employeeData.hireDate);
    
    // حساب العمر عند التقاعد
    const ageAtRetirement = Math.floor((retirement - birth) / (365.25 * 24 * 60 * 60 * 1000));
    
    // حساب سنوات الخدمة
    const serviceYears = Math.floor((retirement - hire) / (365.25 * 24 * 60 * 60 * 1000));
    
    // اقتراح نوع التقاعد
    let suggestedType = '';
    if (ageAtRetirement >= 60) {
        suggestedType = 'تقاعد عادي';
    } else if (serviceYears >= 32) {
        suggestedType = 'تقاعد بدون شرط السن';
    } else if (ageAtRetirement >= 55 && serviceYears >= 15) {
        suggestedType = 'تقاعد مبكر';
    } else {
        suggestedType = 'تقاعد استثنائي';
    }
    
    // عرض المعلومات
    document.getElementById('retirement_age').textContent = ageAtRetirement;
    document.getElementById('calculated_service').textContent = serviceYears;
    document.getElementById('suggested_type').textContent = suggestedType;
    
    // ملء سنوات الخدمة تلقائياً إذا كانت فارغة
    const serviceInput = document.getElementById('years_of_service');
    if (!serviceInput.value) {
        serviceInput.value = serviceYears;
    }
    
    // اقتراح نوع التقاعد
    const typeSelect = document.getElementById('retirement_type');
    if (!typeSelect.value) {
        for (let option of typeSelect.options) {
            if (option.value === suggestedType) {
                option.selected = true;
                break;
            }
        }
    }
    
    document.getElementById('calculated_info').style.display = 'block';
}

// التحقق من صحة التواريخ
document.getElementById('retirement_date').addEventListener('change', function() {
    const retirementDate = new Date(this.value);
    const today = new Date();
    
    if (retirementDate < today) {
        if (!confirm('تاريخ التقاعد في الماضي. هل تريد المتابعة؟')) {
            this.value = '';
            return;
        }
    }
    
    // التحقق من العمر الأدنى للتقاعد
    if (employeeData.birthDate) {
        const birth = new Date(employeeData.birthDate);
        const ageAtRetirement = Math.floor((retirementDate - birth) / (365.25 * 24 * 60 * 60 * 1000));
        
        if (ageAtRetirement < 50) {
            alert('تحذير: العمر عند التقاعد أقل من 50 سنة. تأكد من صحة التاريخ.');
        }
    }
});

// التحقق من سنوات الخدمة
document.getElementById('years_of_service').addEventListener('change', function() {
    const years = parseInt(this.value);
    
    if (years < 1) {
        alert('سنوات الخدمة يجب أن تكون سنة واحدة على الأقل');
        this.value = 1;
    } else if (years > 50) {
        alert('سنوات الخدمة تبدو مرتفعة جداً. تأكد من صحة الرقم.');
    }
});

// تنسيق مبلغ المعاش
document.getElementById('pension_amount').addEventListener('input', function() {
    let value = this.value.replace(/[^\d.]/g, '');
    if (value) {
        this.value = parseFloat(value).toFixed(2);
    }
});

// تعيين تاريخ اليوم كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('retirement_date').value = today;
    document.getElementById('decision_date').value = today;
    
    // حساب المعلومات إذا كانت البيانات متوفرة
    if (employeeData.birthDate && employeeData.hireDate) {
        calculateRetirementInfo();
    }
});
</script>
{% endblock %}