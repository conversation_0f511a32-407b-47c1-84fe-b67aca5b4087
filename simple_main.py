#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية - نسخة مبسطة
"""

from flask import Flask, render_template, request, redirect, url_for, flash
import sqlite3
import os

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'customs_secret_key_2024'

# إعداد قاعدة البيانات
DATABASE = 'customs_employees.db'

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def init_database():
    """إنشاء قاعدة البيانات والجداول الأساسية"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        # جدول الموظفين الأساسي
        conn.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                registration_number TEXT UNIQUE NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                status TEXT DEFAULT 'نشط',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"خطأ في إنشاء قاعدة البيانات: {e}")
        return False

# الصفحة الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    try:
        conn = get_db_connection()
        if conn:
            # إحصائيات بسيطة
            total_employees = conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0]
            active_employees = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'نشط'").fetchone()[0]
            conn.close()
        else:
            total_employees = 0
            active_employees = 0
        
        stats = {
            'total_employees': total_employees,
            'active_employees': active_employees,
            'total_leaves': 0,
            'active_leaves': 0
        }
        
        return render_template('index.html', stats=stats)
    except Exception as e:
        print(f"خطأ في الصفحة الرئيسية: {e}")
        return f"خطأ: {e}"

# قائمة الموظفين
@app.route('/employees')
def employees():
    """قائمة الموظفين"""
    try:
        conn = get_db_connection()
        if conn:
            employees = conn.execute('SELECT * FROM employees ORDER BY id DESC').fetchall()
            conn.close()
            return render_template('employees/index.html', employees=employees)
        else:
            return "خطأ في الاتصال بقاعدة البيانات"
    except Exception as e:
        print(f"خطأ في قائمة الموظفين: {e}")
        return f"خطأ: {e}"

# إضافة موظف
@app.route('/employees/add', methods=['GET', 'POST'])
def add_employee():
    """إضافة موظف جديد"""
    if request.method == 'POST':
        try:
            registration_number = request.form.get('registration_number', '').strip()
            first_name = request.form.get('first_name', '').strip()
            last_name = request.form.get('last_name', '').strip()
            
            if not registration_number or not first_name or not last_name:
                flash('جميع الحقول مطلوبة', 'error')
                return render_template('employees/add.html')
            
            conn = get_db_connection()
            if conn:
                conn.execute('''
                    INSERT INTO employees (registration_number, first_name, last_name)
                    VALUES (?, ?, ?)
                ''', (registration_number, first_name, last_name))
                
                conn.commit()
                conn.close()
                flash('تم إضافة الموظف بنجاح', 'success')
                return redirect(url_for('employees'))
            else:
                flash('خطأ في الاتصال بقاعدة البيانات', 'error')
                
        except Exception as e:
            flash(f'خطأ في إضافة الموظف: {e}', 'error')
    
    return render_template('employees/add.html')

# صفحة حالات الموظفين
@app.route('/employee_statuses')
def employee_statuses():
    """صفحة حالات الموظفين"""
    try:
        conn = get_db_connection()
        if conn:
            # حساب عدد الموظفين لكل حالة
            active_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'نشط'").fetchone()[0]
            assignment_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'منتدب'").fetchone()[0]
            study_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'في دراسة'").fetchone()[0]
            leave_of_absence_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'مستودع'").fetchone()[0]
            suspension_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'موقوف'").fetchone()[0]
            retirement_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'متقاعد'").fetchone()[0]
            resignation_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'مستقيل'").fetchone()[0]
            external_transfer_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'محول خارجياً'").fetchone()[0]
            death_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'متوفى'").fetchone()[0]
            long_term_leave_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'في عطلة طويلة'").fetchone()[0]
            
            conn.close()
        else:
            # قيم افتراضية
            active_count = 0
            assignment_count = 0
            study_count = 0
            leave_of_absence_count = 0
            suspension_count = 0
            retirement_count = 0
            resignation_count = 0
            external_transfer_count = 0
            death_count = 0
            long_term_leave_count = 0
        
        return render_template('employee_statuses/index.html',
                             active_count=active_count,
                             assignment_count=assignment_count,
                             study_count=study_count,
                             leave_of_absence_count=leave_of_absence_count,
                             suspension_count=suspension_count,
                             retirement_count=retirement_count,
                             resignation_count=resignation_count,
                             external_transfer_count=external_transfer_count,
                             death_count=death_count,
                             long_term_leave_count=long_term_leave_count)
    except Exception as e:
        print(f"خطأ في صفحة حالات الموظفين: {e}")
        return f"خطأ: {e}"

# صفحات أخرى بسيطة
@app.route('/leaves')
def leaves():
    """صفحة العطل والإجازات"""
    return render_template('leaves/index.html')

@app.route('/certificates')
def certificates():
    """صفحة الشهادات والتكوين"""
    return render_template('certificates/index.html')

@app.route('/sanctions')
def sanctions():
    """صفحة العقوبات والمكافآت"""
    return render_template('sanctions/index.html')

@app.route('/transfers')
def transfers():
    """صفحة التنقلات والحركات"""
    return render_template('transfers/index.html')

@app.route('/promotions')
def promotions():
    """صفحة مختلف الترقيات"""
    return render_template('promotions/index.html')

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    return render_template('settings/index.html')

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports/index.html')

# معالج الأخطاء
@app.errorhandler(404)
def not_found(error):
    return "الصفحة غير موجودة", 404

@app.errorhandler(500)
def internal_error(error):
    return "خطأ داخلي في الخادم", 500

if __name__ == '__main__':
    print("\n" + "="*60)
    print("🏛️ نظام إدارة موظفي الجمارك الجزائرية")
    print("="*60)
    
    # إنشاء قاعدة البيانات
    if init_database():
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
    else:
        print("❌ خطأ في إنشاء قاعدة البيانات")
    
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("📊 صفحة حالات الموظفين: http://localhost:5000/employee_statuses")
    print("👥 إدارة الموظفين: http://localhost:5000/employees")
    print("="*60)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("="*60 + "\n")
    
    try:
        app.run(debug=False, host='127.0.0.1', port=5000)
    except Exception as e:
        print(f"خطأ في تشغيل الخادم: {e}")
