#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جداول إدارة حالات الموظفين
Create Employee Status Management Tables
"""

import sqlite3
import os

def create_status_tables():
    """إنشاء جداول إدارة الحالات"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    print("🔄 إنشاء جداول إدارة حالات الموظفين...")
    
    # جدول الوفيات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_deaths (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            death_date DATE NOT NULL,
            death_cause TEXT,
            death_location TEXT,
            medical_report TEXT,
            burial_location TEXT,
            family_contact TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            <PERSON>OREIG<PERSON> KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    print("✅ جدول employee_deaths")
    
    # جدول الاستيداع
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_leave_of_absence (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE,
            reason_id INTEGER,
            reason_details TEXT,
            approval_date DATE,
            approved_by TEXT,
            status TEXT DEFAULT 'نشط',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id),
            FOREIGN KEY (reason_id) REFERENCES leave_of_absence_reasons (id)
        )
    ''')
    print("✅ جدول employee_leave_of_absence")
    
    # جدول التوقيف
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_suspensions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE,
            suspension_type TEXT NOT NULL,
            reason TEXT NOT NULL,
            decision_number TEXT,
            decision_date DATE,
            approved_by TEXT,
            status TEXT DEFAULT 'نشط',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    print("✅ جدول employee_suspensions")
    
    # جدول الانتداب
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_assignments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            assignment_type TEXT NOT NULL,
            destination TEXT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE,
            assignment_details TEXT,
            decision_number TEXT,
            decision_date DATE,
            approved_by TEXT,
            status TEXT DEFAULT 'نشط',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    print("✅ جدول employee_assignments")
    
    # جدول الاستقالات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_resignations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            resignation_date DATE NOT NULL,
            effective_date DATE,
            resignation_type TEXT NOT NULL,
            reason TEXT,
            notice_period INTEGER,
            approval_status TEXT DEFAULT 'معلق',
            approved_by TEXT,
            approval_date DATE,
            final_settlement REAL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    print("✅ جدول employee_resignations")
    
    # جدول التحويلات الخارجية
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_external_transfers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            transfer_date DATE NOT NULL,
            destination_organization TEXT NOT NULL,
            destination_department TEXT,
            transfer_type TEXT NOT NULL,
            reason TEXT,
            decision_number TEXT,
            decision_date DATE,
            approved_by TEXT,
            status TEXT DEFAULT 'مكتمل',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    print("✅ جدول employee_external_transfers")
    
    # جدول العطل طويلة الأمد
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_long_term_leaves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            leave_type TEXT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE,
            medical_condition TEXT,
            doctor_name TEXT,
            hospital_name TEXT,
            review_date DATE,
            extension_count INTEGER DEFAULT 0,
            status TEXT DEFAULT 'نشط',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    print("✅ جدول employee_long_term_leaves")
    
    # جدول التقاعد
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employee_retirements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            retirement_date DATE NOT NULL,
            retirement_type TEXT NOT NULL,
            service_years INTEGER,
            pension_amount REAL,
            pension_percentage REAL,
            final_settlement REAL,
            decision_number TEXT,
            decision_date DATE,
            approved_by TEXT,
            status TEXT DEFAULT 'مكتمل',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    print("✅ جدول employee_retirements")
    
    # جدول أسباب الاستيداع
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reason_name TEXT NOT NULL UNIQUE,
            description TEXT,
            max_duration_months INTEGER,
            renewable BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    print("✅ جدول leave_of_absence_reasons")
    
    # إدراج أسباب الاستيداع الافتراضية
    reasons = [
        ('أسباب صحية', 'استيداع لأسباب صحية', 12, 1),
        ('أسباب عائلية', 'استيداع لأسباب عائلية', 6, 0),
        ('الدراسة', 'استيداع للدراسة', 24, 1),
        ('أسباب شخصية', 'استيداع لأسباب شخصية', 6, 0),
        ('رعاية الأطفال', 'استيداع لرعاية الأطفال', 12, 1)
    ]
    
    cursor.executemany('''
        INSERT OR IGNORE INTO leave_of_absence_reasons 
        (reason_name, description, max_duration_months, renewable)
        VALUES (?, ?, ?, ?)
    ''', reasons)
    print("✅ تم إدراج أسباب الاستيداع الافتراضية")
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("🎉 تم إنشاء جميع جداول إدارة الحالات بنجاح!")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏗️  إنشاء جداول إدارة حالات الموظفين")
    print("=" * 60)
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('customs_employees.db'):
        print("❌ قاعدة البيانات غير موجودة")
        print("💡 يرجى تشغيل init_database.py أولاً")
        return
    
    try:
        create_status_tables()
        print("\n✅ تم إكمال العملية بنجاح!")
        print("🚀 يمكنك الآن تشغيل النظام")
        
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء الجداول: {e}")

if __name__ == "__main__":
    main()