#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض جميع حقول الموظف في قاعدة البيانات
"""

import sqlite3

def show_employee_fields():
    """عرض جميع حقول جدول الموظفين"""
    print("📋 جميع حقول الموظف في قاعدة البيانات")
    print("=" * 80)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    # الحصول على معلومات الأعمدة
    cursor.execute("PRAGMA table_info(employees)")
    columns = cursor.fetchall()
    
    print(f"🗃️  جدول: employees")
    print(f"📊 عدد الحقول: {len(columns)}")
    print("-" * 80)
    
    # عرض الحقول مع التفاصيل
    for i, col in enumerate(columns, 1):
        col_id = col[0]
        col_name = col[1]
        col_type = col[2]
        not_null = "NOT NULL" if col[3] == 1 else "NULL"
        default_val = f"DEFAULT {col[4]}" if col[4] else ""
        is_pk = "PRIMARY KEY" if col[5] == 1 else ""
        
        # تحديد الوصف بالعربية
        arabic_descriptions = {
            'id': 'معرف الموظف',
            'registration_number': 'رقم التسجيل',
            'first_name': 'الاسم الأول (عربي)',
            'last_name': 'اللقب (عربي)',
            'first_name_fr': 'الاسم الأول (فرنسي)',
            'last_name_fr': 'اللقب (فرنسي)',
            'birth_date': 'تاريخ الميلاد',
            'birth_wilaya_id': 'معرف ولاية الميلاد',
            'birth_commune_id': 'معرف بلدية الميلاد',
            'gender': 'الجنس',
            'social_security_number': 'رقم الضمان الاجتماعي',
            'hire_date': 'تاريخ التوظيف',
            'current_rank_id': 'معرف الرتبة الحالية',
            'corps_id': 'معرف السلك',
            'current_service_id': 'معرف المصلحة الحالية',
            'postal_account': 'رقم الحساب الجاري البريدي',
            'phone': 'رقم الهاتف (قديم)',
            'phone1': 'رقم الهاتف الأول',
            'phone2': 'رقم الهاتف الثاني',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'photo': 'الصورة (Base64)',
            'status': 'الحالة الوظيفية',
            'marital_status': 'الحالة العائلية',
            'children_count': 'عدد الأبناء',
            'dependents_count': 'عدد المتكفل بهم',
            'blood_type': 'زمرة الدم',
            'sport_practiced': 'الرياضة الممارسة',
            'created_at': 'تاريخ الإنشاء',
            'updated_at': 'تاريخ آخر تحديث'
        }
        
        arabic_desc = arabic_descriptions.get(col_name, 'غير معرف')
        
        print(f"{i:2d}. {col_name:<25} | {col_type:<12} | {not_null:<8} | {default_val:<15} | {is_pk}")
        print(f"    📝 {arabic_desc}")
        print()
    
    # الحصول على بيانات موظف تجريبي إذا وجد
    cursor.execute("SELECT * FROM employees LIMIT 1")
    sample_employee = cursor.fetchone()
    
    if sample_employee:
        print("-" * 80)
        print("📄 مثال على بيانات موظف:")
        print("-" * 80)
        
        for i, col in enumerate(columns):
            col_name = col[1]
            value = sample_employee[i] if i < len(sample_employee) else None
            arabic_desc = arabic_descriptions.get(col_name, 'غير معرف')
            
            if value is not None:
                # تقصير القيم الطويلة
                if isinstance(value, str) and len(str(value)) > 50:
                    display_value = str(value)[:47] + "..."
                else:
                    display_value = value
                status = "✅"
            else:
                display_value = "NULL"
                status = "❌"
            
            print(f"{status} {col_name:<25} = {display_value}")
    
    conn.close()
    
    print("\n" + "=" * 80)
    print("📊 ملخص الحقول:")
    
    # تصنيف الحقول
    categories = {
        'البيانات الأساسية': ['id', 'registration_number', 'first_name', 'last_name', 'first_name_fr', 'last_name_fr'],
        'البيانات الشخصية': ['birth_date', 'birth_wilaya_id', 'birth_commune_id', 'gender', 'social_security_number', 'marital_status', 'children_count', 'dependents_count', 'blood_type', 'sport_practiced'],
        'البيانات المهنية': ['hire_date', 'current_rank_id', 'corps_id', 'current_service_id', 'status'],
        'بيانات الاتصال': ['phone', 'phone1', 'phone2', 'email', 'address', 'postal_account'],
        'البيانات التقنية': ['photo', 'created_at', 'updated_at']
    }
    
    for category, fields in categories.items():
        print(f"\n🏷️  {category}:")
        for field in fields:
            if any(col[1] == field for col in columns):
                arabic_desc = arabic_descriptions.get(field, 'غير معرف')
                print(f"   • {field} - {arabic_desc}")

if __name__ == "__main__":
    show_employee_fields()