{% extends "base.html" %}

{% block title %}قائمة الموظفين{% endblock %}

{% block page_title %}قائمة الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><i class="fas fa-users me-2"></i>قائمة الموظفين</h4>
                            <p class="text-muted mb-0">إدارة بيانات الموظفين</p>
                        </div>
                        <div>
                            <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if employees %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم التسجيل</th>
                                    <th>الاسم الكامل</th>
                                    <th>المنصب</th>
                                    <th>القسم</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td><strong>{{ employee.registration_number }}</strong></td>
                                    <td>{{ employee.first_name }} {{ employee.last_name }}</td>
                                    <td>{{ employee.position or '-' }}</td>
                                    <td>{{ employee.department or '-' }}</td>
                                    <td>
                                        {% if employee.status == 'نشط' %}
                                            <span class="badge bg-success">{{ employee.status }}</span>
                                        {% elif employee.status == 'منتدب' %}
                                            <span class="badge bg-info">{{ employee.status }}</span>
                                        {% elif employee.status == 'موقوف' %}
                                            <span class="badge bg-danger">{{ employee.status }}</span>
                                        {% elif employee.status == 'متقاعد' %}
                                            <span class="badge bg-secondary">{{ employee.status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ employee.status or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ employee.hire_date or '-' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات موظفين</h5>
                        <p class="text-muted">ابدأ بإضافة موظف جديد</p>
                        <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    background-color: #343a40;
    color: white;
    border: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,.075);
}

.badge {
    font-size: 0.8rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-body {
    padding: 1.5rem;
}
</style>
{% endblock %}
