{% extends "base.html" %}

{% block title %}إدارة الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-users"></i>
                    إدارة الموظفين
                </h1>
                <p class="page-subtitle">إدارة شاملة لبيانات موظفي الجمارك الجزائرية</p>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i> قائمة الموظفين
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                                <i class="fas fa-plus"></i> إضافة موظف جديد
                            </button>
                            <button type="button" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-info">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث برقم التسجيل</label>
                            <input type="text" class="form-control" placeholder="رقم التسجيل">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">البحث بالاسم</label>
                            <input type="text" class="form-control" placeholder="الاسم الأول أو العائلة">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">القسم</label>
                            <select class="form-select">
                                <option value="">جميع الأقسام</option>
                                <option value="customs">الجمارك</option>
                                <option value="admin">الإدارة</option>
                                <option value="finance">المالية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">المنصب</label>
                            <select class="form-select">
                                <option value="">جميع المناصب</option>
                                <option value="officer">ضابط</option>
                                <option value="inspector">مفتش</option>
                                <option value="clerk">كاتب</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="row">
        <div class="col-12">
            <div class="table-container">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم التسجيل</th>
                            <th>الصورة</th>
                            <th>الاسم الكامل</th>
                            <th>العمر</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>تاريخ التوظيف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if employees %}
                            {% for employee in employees %}
                            <tr>
                                <td><strong>{{ employee.registration_number }}</strong></td>
                                <td>
                                    {% if employee.photo_path %}
                                        <img src="{{ url_for('static', filename='uploads/' + employee.photo_path) }}" 
                                             alt="صورة الموظف" class="employee-photo">
                                    {% else %}
                                        <div class="employee-photo-placeholder">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ employee.first_name_ar }} {{ employee.last_name_ar }}</strong>
                                    {% if employee.first_name_fr %}
                                        <br><small class="text-muted">{{ employee.first_name_fr }} {{ employee.last_name_fr }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ employee.age or '-' }}</td>
                                <td>{{ employee.department or '-' }}</td>
                                <td>{{ employee.position or '-' }}</td>
                                <td>{{ employee.hire_date or '-' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">لا توجد بيانات موظفين</h5>
                                        <p class="text-muted">ابدأ بإضافة موظف جديد للنظام</p>
                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                                            <i class="fas fa-plus"></i> إضافة موظف جديد
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Employee Modal -->
<div class="modal fade" id="addEmployeeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> إضافة موظف جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addEmployeeForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم التسجيل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="registration_number" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأول (عربي) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="first_name_ar" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم العائلة (عربي) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="last_name_ar" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأول (فرنسي)</label>
                            <input type="text" class="form-control" name="first_name_fr">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم العائلة (فرنسي)</label>
                            <input type="text" class="form-control" name="last_name_fr">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" name="birth_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">القسم</label>
                            <select class="form-select" name="department">
                                <option value="">اختر القسم</option>
                                <option value="customs">الجمارك</option>
                                <option value="admin">الإدارة</option>
                                <option value="finance">المالية</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المنصب</label>
                            <select class="form-select" name="position">
                                <option value="">اختر المنصب</option>
                                <option value="officer">ضابط</option>
                                <option value="inspector">مفتش</option>
                                <option value="clerk">كاتب</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ التوظيف</label>
                            <input type="date" class="form-control" name="hire_date">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="2"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.employee-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.employee-photo-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.empty-state {
    padding: 40px 20px;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}
</style>
{% endblock %}
