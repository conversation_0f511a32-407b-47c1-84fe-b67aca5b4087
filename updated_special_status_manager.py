#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الحالات الخاصة المحدث للموظفين
Updated Special Employee Status Manager
"""

import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional

class UpdatedSpecialStatusManager:
    """مدير الحالات الخاصة المحدث للموظفين"""
    
    def __init__(self, db_path: str = 'customs_employees.db'):
        self.db_path = db_path
        self.MAX_LEAVE_OF_ABSENCE_YEARS = 5  # الحد الأقصى للاستيداع 5 سنوات
        self.ensure_tables_exist()
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def ensure_tables_exist(self):
        """التأكد من وجود الجداول المطلوبة"""
        try:
            from create_updated_special_status_tables import create_updated_special_status_tables
            create_updated_special_status_tables()
        except Exception as e:
            print(f"تحذير: لم يتم إنشاء الجداول: {e}")
    
    def calculate_end_date(self, start_date: str, duration_months: int) -> str:
        """حساب تاريخ النهاية بإضافة المدة إلى تاريخ البداية"""
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            # إضافة الأشهر
            end = start + timedelta(days=duration_months * 30)  # تقريبي
            return end.strftime('%Y-%m-%d')
        except:
            return start_date
    
    def get_employee_leave_balance(self, employee_id: int) -> Dict:
        """حساب رصيد الاستيداع للموظف"""
        conn = self.get_db_connection()
        
        # حساب إجمالي الأشهر المستخدمة
        total_months = conn.execute('''
            SELECT COALESCE(SUM(duration_months), 0) 
            FROM employee_leave_of_absence_updated 
            WHERE employee_id = ?
        ''', (employee_id,)).fetchone()[0]
        
        max_months = self.MAX_LEAVE_OF_ABSENCE_YEARS * 12  # 60 شهر
        remaining_months = max_months - total_months
        
        conn.close()
        
        return {
            'total_used_months': total_months,
            'total_used_years': total_months / 12,
            'remaining_months': remaining_months,
            'remaining_years': remaining_months / 12,
            'max_allowed_years': self.MAX_LEAVE_OF_ABSENCE_YEARS
        }
    
    # ================================
    # إدارة أسباب الاستيداع
    # ================================
    
    def get_leave_reasons(self) -> List:
        """الحصول على قائمة أسباب الاستيداع"""
        conn = self.get_db_connection()
        reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
        conn.close()
        return reasons
    
    def add_leave_reason(self, reason: str) -> bool:
        """إضافة سبب استيداع جديد"""
        try:
            conn = self.get_db_connection()
            conn.execute('''
                INSERT INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
            conn.commit()
            conn.close()
            return True
        except:
            return False
    
    def update_leave_reason(self, reason_id: int, new_reason: str) -> bool:
        """تحديث سبب الاستيداع"""
        try:
            conn = self.get_db_connection()
            conn.execute('''
                UPDATE leave_of_absence_reasons 
                SET reason = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_reason, reason_id))
            conn.commit()
            conn.close()
            return True
        except:
            return False
    
    def deactivate_leave_reason(self, reason_id: int) -> bool:
        """إلغاء تفعيل سبب الاستيداع"""
        try:
            conn = self.get_db_connection()
            conn.execute('''
                UPDATE leave_of_absence_reasons 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (reason_id,))
            conn.commit()
            conn.close()
            return True
        except:
            return False
    
    # ================================
    # إدارة الاستيداع المحدث
    # ================================
    
    def add_leave_of_absence(self, data: Dict) -> bool:
        """إضافة استيداع جديد مع التحقق من الحد الأقصى"""
        try:
            # التحقق من الرصيد المتاح
            balance = self.get_employee_leave_balance(data['employee_id'])
            if balance['remaining_months'] < data['duration_months']:
                return False, f"تجاوز الحد الأقصى للاستيداع. المتاح: {balance['remaining_months']} شهر"
            
            # حساب تاريخ النهاية
            end_date = self.calculate_end_date(data['start_date'], data['duration_months'])
            
            # حساب رقم الفترة
            conn = self.get_db_connection()
            period_count = conn.execute('''
                SELECT COUNT(*) FROM employee_leave_of_absence_updated 
                WHERE employee_id = ?
            ''', (data['employee_id'],)).fetchone()[0]
            
            period_number = period_count + 1
            
            conn.execute('''
                INSERT INTO employee_leave_of_absence_updated 
                (employee_id, period_number, duration_months, reason, start_date, end_date,
                 decision_number, decision_date, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['employee_id'],
                period_number,
                data['duration_months'],
                data['reason'],
                data['start_date'],
                end_date,
                data.get('decision_number', ''),
                data.get('decision_date', ''),
                data.get('status', 'active')
            ))
            
            conn.commit()
            conn.close()
            return True, "تم إضافة الاستيداع بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة الاستيداع: {str(e)}"
    
    # ================================
    # إدارة الاستقالات المحدثة
    # ================================
    
    def add_resignation(self, data: Dict) -> bool:
        """إضافة استقالة جديدة مع نظام الموافقة"""
        try:
            conn = self.get_db_connection()
            
            conn.execute('''
                INSERT INTO employee_resignations_updated 
                (employee_id, request_date, reason, is_approved, 
                 approval_decision_number, approval_decision_date, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['employee_id'],
                data['request_date'],
                data.get('reason', ''),
                data.get('is_approved'),
                data.get('approval_decision_number', ''),
                data.get('approval_decision_date', ''),
                data.get('status', 'pending')
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"خطأ في إضافة الاستقالة: {e}")
            return False
    
    # ================================
    # إدارة الحالات النهائية (نقل الموظفين)
    # ================================
    
    def move_employee_to_final_status(self, employee_id: int, status_type: str, status_data: Dict) -> bool:
        """نقل الموظف إلى حالة نهائية وحذفه من جدول الموظفين"""
        try:
            conn = self.get_db_connection()
            
            # الحصول على بيانات الموظف أولاً
            employee = conn.execute('''
                SELECT * FROM employees WHERE id = ?
            ''', (employee_id,)).fetchone()
            
            if not employee:
                return False, "الموظف غير موجود"
            
            # إضافة السجل إلى جدول الحالة النهائية
            if status_type == 'death':
                conn.execute('''
                    INSERT INTO employee_deaths_updated 
                    (employee_id, death_date, cause_type, certificate_number)
                    VALUES (?, ?, ?, ?)
                ''', (employee_id, status_data['death_date'], 
                      status_data.get('cause_type', 'عادية'),
                      status_data.get('certificate_number', '')))
                      
            elif status_type == 'external_transfer':
                conn.execute('''
                    INSERT INTO employee_external_transfers_updated 
                    (employee_id, end_of_duties_date, destination_directorate, 
                     decision_number, decision_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (employee_id, status_data['end_of_duties_date'],
                      status_data['destination_directorate'],
                      status_data.get('decision_number', ''),
                      status_data.get('decision_date', '')))
                      
            elif status_type == 'dismissal':
                conn.execute('''
                    INSERT INTO employee_dismissals 
                    (employee_id, dismissal_date, reason, decision_number, decision_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (employee_id, status_data['dismissal_date'],
                      status_data['reason'],
                      status_data.get('decision_number', ''),
                      status_data.get('decision_date', '')))
                      
            elif status_type == 'retirement':
                conn.execute('''
                    INSERT INTO employee_retirements_updated 
                    (employee_id, retirement_date, decision_number, decision_date,
                     retirement_card_number, card_issue_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (employee_id, status_data['retirement_date'],
                      status_data.get('decision_number', ''),
                      status_data.get('decision_date', ''),
                      status_data.get('retirement_card_number', ''),
                      status_data.get('card_issue_date', '')))
            
            # حذف الموظف من جدول الموظفين الرئيسي
            conn.execute('DELETE FROM employees WHERE id = ?', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم نقل الموظف إلى الحالة النهائية بنجاح"
            
        except Exception as e:
            return False, f"خطأ في نقل الموظف: {str(e)}"
    
    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات شاملة"""
        conn = self.get_db_connection()
        stats = {}
        
        try:
            # إحصائيات الحالات المؤقتة (لا تزال في جدول الموظفين)
            stats['active_leave_of_absence'] = conn.execute(
                'SELECT COUNT(*) FROM employee_leave_of_absence_updated WHERE status = "active"'
            ).fetchone()[0]
            
            stats['active_suspensions'] = conn.execute(
                'SELECT COUNT(*) FROM employee_suspensions_updated WHERE status = "active"'
            ).fetchone()[0]
            
            stats['pending_resignations'] = conn.execute(
                'SELECT COUNT(*) FROM employee_resignations_updated WHERE status = "pending"'
            ).fetchone()[0]
            
            # إحصائيات الحالات النهائية (تم نقلها من جدول الموظفين)
            stats['total_deaths'] = conn.execute(
                'SELECT COUNT(*) FROM employee_deaths_updated'
            ).fetchone()[0]
            
            stats['total_external_transfers'] = conn.execute(
                'SELECT COUNT(*) FROM employee_external_transfers_updated'
            ).fetchone()[0]
            
            stats['total_dismissals'] = conn.execute(
                'SELECT COUNT(*) FROM employee_dismissals'
            ).fetchone()[0]
            
            stats['total_retirements'] = conn.execute(
                'SELECT COUNT(*) FROM employee_retirements_updated'
            ).fetchone()[0]
            
        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            # إرجاع قيم افتراضية في حالة الخطأ
            stats = {key: 0 for key in [
                'active_leave_of_absence', 'active_suspensions', 'pending_resignations',
                'total_deaths', 'total_external_transfers', 'total_dismissals', 'total_retirements'
            ]}
        finally:
            conn.close()
        
        return stats
