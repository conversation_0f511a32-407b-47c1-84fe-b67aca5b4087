#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح ودمج نظام حالات الموظفين مع النظام الموجود
Fix and Integrate Employee Status System
"""

import sqlite3
from datetime import datetime, date
import json

def check_existing_system():
    """فحص النظام الموجود"""
    print("🔍 فحص النظام الموجود...")
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    # فحص الجداول الموجودة
    cursor.execute('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name LIKE '%employee%'
        ORDER BY name
    ''')
    existing_tables = cursor.fetchall()
    
    print("📋 الجداول الموجودة:")
    for table in existing_tables:
        count = cursor.execute(f'SELECT COUNT(*) FROM {table[0]}').fetchone()[0]
        print(f"   - {table[0]}: {count} سجل")
    
    conn.close()
    return [table[0] for table in existing_tables]

def create_enhanced_status_system():
    """إنشاء نظام محسن متوافق مع الموجود"""
    print("\n🚀 إنشاء النظام المحسن...")
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    try:
        # 1. جدول أسباب الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reason_text TEXT NOT NULL UNIQUE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج الأسباب الافتراضية
        default_reasons = [
            'رعاية الأطفال',
            'الدراسة',
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        for reason in default_reasons:
            cursor.execute('''
                INSERT OR IGNORE INTO leave_of_absence_reasons (reason_text)
                VALUES (?)
            ''', (reason,))
        
        # 2. جدول الاستيداع المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_leave_of_absence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                period_number INTEGER NOT NULL,
                duration_months INTEGER NOT NULL,
                reason_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                total_previous_months INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                FOREIGN KEY (reason_id) REFERENCES leave_of_absence_reasons (id)
            )
        ''')
        
        # 3. جدول التوقيف المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_suspension (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                suspension_date DATE NOT NULL,
                suspension_reason TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                end_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 4. جدول الاستقالة المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_resignation (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                request_date DATE NOT NULL,
                resignation_reason TEXT NOT NULL,
                is_accepted INTEGER, -- NULL = قيد الدراسة، 1 = مقبولة، 0 = مرفوضة
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 5. جدول الخدمة الوطنية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_national_service (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                service_location TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 6. جدول عطلة طويلة الأمد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_long_term_leave (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                review_date DATE NOT NULL,
                document_number TEXT NOT NULL,
                document_date DATE NOT NULL,
                granting_authority TEXT NOT NULL,
                leave_reason TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 7. جدول الانتداب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_assignment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                assignment_date DATE NOT NULL,
                duration_months INTEGER NOT NULL,
                assignment_reason TEXT NOT NULL,
                assignment_location TEXT NOT NULL,
                end_date DATE NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 8. جدول الدراسة/التكوين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_study_training (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                study_type TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                institution TEXT,
                expected_end_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 9. جدول العزل
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_dismissal (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                dismissal_date DATE NOT NULL,
                dismissal_reason TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # ================================
        # جداول الحالات النهائية (المحذوفة)
        # ================================
        
        # 1. جدول الوفيات المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS archived_deceased_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                death_date DATE NOT NULL,
                death_cause TEXT NOT NULL CHECK (death_cause IN ('عادية', 'حادث عمل')),
                death_certificate_number TEXT NOT NULL,
                employee_data TEXT, -- JSON لجميع بيانات الموظف
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # 2. جدول التحويل الخارجي المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS archived_external_transfer_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                transfer_date DATE NOT NULL,
                destination_directorate TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                employee_data TEXT,
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # 3. جدول العزل المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS archived_dismissed_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                dismissal_date DATE NOT NULL,
                dismissal_reason TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                employee_data TEXT,
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # 4. جدول التقاعد المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS archived_retired_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                retirement_date DATE NOT NULL,
                retirement_decision_number TEXT NOT NULL,
                retirement_decision_date DATE NOT NULL,
                retirement_card_number TEXT,
                retirement_card_issue_date DATE,
                employee_data TEXT,
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # جدول تاريخ تغييرات الحالات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                old_status TEXT,
                new_status TEXT NOT NULL,
                change_date DATE NOT NULL,
                change_reason TEXT,
                decision_number TEXT,
                decision_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT
            )
        ''')
        
        conn.commit()
        print("✅ تم إنشاء جميع الجداول المحسنة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def create_enhanced_status_manager():
    """إنشاء مدير الحالات المحسن"""
    
    manager_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير حالات الموظفين المحسن - متوافق مع النظام الموجود
Enhanced Employee Status Manager - Compatible with Existing System
"""

import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import json

class EnhancedEmployeeStatusManager:
    """مدير حالات الموظفين المحسن"""
    
    def __init__(self, db_path: str = 'customs_employees.db'):
        self.db_path = db_path
        
        # تصنيف الحالات
        self.permanent_removal_statuses = {
            'death': 'متوفى',
            'external_transfer': 'محول خارجياً', 
            'dismissal': 'معزول',
            'retirement': 'متقاعد'
        }
        
        self.temporary_statuses = {
            'leave_of_absence': 'مستودع',
            'suspension': 'موقوف',
            'resignation': 'مستقيل',
            'national_service': 'في الخدمة الوطنية',
            'long_term_leave': 'في عطلة طويلة الأمد',
            'assignment': 'منتدب',
            'study_training': 'في دراسة/تكوين'
        }
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    # ================================
    # دوال الحالات المؤقتة
    # ================================
    
    def add_leave_of_absence(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة استيداع"""
        conn = self.get_db_connection()
        try:
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # حساب إجمالي أشهر الاستيداع السابقة
            total_previous = conn.execute('''
                SELECT COALESCE(SUM(duration_months), 0) 
                FROM enhanced_leave_of_absence 
                WHERE employee_id = ? AND is_active = 1
            ''', (employee_id,)).fetchone()[0]
            
            # التحقق من عدم تجاوز 60 شهر (5 سنوات)
            new_duration = int(data['duration_months'])
            if total_previous + new_duration > 60:
                remaining = 60 - total_previous
                return False, f"تجاوز الحد الأقصى للاستيداع. المتبقي: {remaining} شهر"
            
            # حساب تاريخ النهاية
            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            end_date = start_date + timedelta(days=new_duration * 30)  # تقريبي
            
            # حساب رقم الفترة
            period_count = conn.execute('''
                SELECT COUNT(*) FROM enhanced_leave_of_absence WHERE employee_id = ?
            ''', (employee_id,)).fetchone()[0]
            
            # إدراج البيانات
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO enhanced_leave_of_absence 
                (employee_id, period_number, duration_months, reason_id, start_date, 
                 end_date, decision_number, decision_date, total_previous_months, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, period_count + 1, new_duration, data['reason_id'],
                start_date, end_date, data.get('decision_number'),
                data.get('decision_date'), total_previous, data.get('created_by')
            ))
            
            # تحديث حالة الموظف
            conn.execute('UPDATE employees SET status = ? WHERE id = ?', 
                        ('مستودع', employee_id))
            
            # إضافة سجل في التاريخ
            self._add_status_history(conn, employee_id, employee['status'], 'مستودع',
                                   start_date, 'استيداع', data.get('decision_number'),
                                   data.get('decision_date'), data.get('created_by'))
            
            conn.commit()
            return True, "تم إضافة الاستيداع بنجاح"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    def transfer_to_death(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """نقل الموظف لجدول الوفيات وحذفه من جدول الموظفين"""
        conn = self.get_db_connection()
        try:
            # الحصول على بيانات الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # تحويل بيانات الموظف إلى JSON
            employee_data = dict(employee)
            employee_json = json.dumps(employee_data, ensure_ascii=False, default=str)
            
            cursor = conn.cursor()
            
            # إدراج في جدول الوفيات
            cursor.execute('''
                INSERT INTO archived_deceased_employees 
                (original_employee_id, registration_number, first_name, last_name,
                 birth_date, hire_date, death_date, death_cause, death_certificate_number,
                 employee_data, transferred_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, employee['registration_number'], employee['first_name'],
                employee['last_name'], employee['birth_date'], employee['hire_date'],
                data['death_date'], data['death_cause'], data['death_certificate_number'],
                employee_json, data.get('transferred_by')
            ))
            
            # إضافة سجل في التاريخ
            self._add_status_history(conn, employee_id, employee['status'], 'متوفى',
                                   data['death_date'], 'وفاة', None, None, 
                                   data.get('transferred_by'))
            
            # حذف من جدول الموظفين
            conn.execute('DELETE FROM employees WHERE id = ?', (employee_id,))
            
            conn.commit()
            return True, "تم نقل الموظف لجدول الوفيات"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    def _add_status_history(self, conn, employee_id, old_status, new_status, 
                           change_date, change_reason, decision_number, 
                           decision_date, created_by):
        """إضافة سجل في تاريخ تغييرات الحالات"""
        conn.execute('''
            INSERT INTO enhanced_status_history 
            (employee_id, old_status, new_status, change_date, change_reason,
             decision_number, decision_date, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (employee_id, old_status, new_status, change_date, change_reason,
              decision_number, decision_date, created_by))
    
    def get_employee_statistics(self) -> Dict:
        """إحصائيات الموظفين حسب الحالات"""
        conn = self.get_db_connection()
        try:
            stats = {}
            
            # الموظفين النشطين
            stats['active'] = conn.execute('''
                SELECT COUNT(*) FROM employees WHERE status = 'نشط'
            ''').fetchone()[0]
            
            # الحالات المؤقتة
            for status_key, status_name in self.temporary_statuses.items():
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = ?
                ''', (status_name,)).fetchone()[0]
            
            # الحالات النهائية
            stats['deceased'] = conn.execute('SELECT COUNT(*) FROM archived_deceased_employees').fetchone()[0]
            stats['external_transfer'] = conn.execute('SELECT COUNT(*) FROM archived_external_transfer_employees').fetchone()[0]
            stats['dismissed'] = conn.execute('SELECT COUNT(*) FROM archived_dismissed_employees').fetchone()[0]
            stats['retired'] = conn.execute('SELECT COUNT(*) FROM archived_retired_employees').fetchone()[0]
            
            # الإجمالي
            stats['total_active'] = sum([stats['active']] + [stats[key] for key in self.temporary_statuses.keys()])
            stats['total_removed'] = stats['deceased'] + stats['external_transfer'] + stats['dismissed'] + stats['retired']
            stats['grand_total'] = stats['total_active'] + stats['total_removed']
            
            return stats
            
        except Exception as e:
            print(f"خطأ في الإحصائيات: {e}")
            return {}
        finally:
            conn.close()

def create_enhanced_status_manager():
    """إنشاء مدير الحالات المحسن"""
    return EnhancedEmployeeStatusManager()
'''
    
    with open('enhanced_employee_status_manager.py', 'w', encoding='utf-8') as f:
        f.write(manager_code)
    
    print("✅ تم إنشاء مدير الحالات المحسن")

def test_enhanced_system():
    """اختبار النظام المحسن"""
    print("\n🧪 اختبار النظام المحسن...")
    
    try:
        from enhanced_employee_status_manager import create_enhanced_status_manager
        
        manager = create_enhanced_status_manager()
        stats = manager.get_employee_statistics()
        
        print("📊 إحصائيات النظام المحسن:")
        print(f"   النشطين: {stats.get('active', 0)}")
        print(f"   المستودعين: {stats.get('leave_of_absence', 0)}")
        print(f"   الموقوفين: {stats.get('suspension', 0)}")
        print(f"   المتوفين: {stats.get('deceased', 0)}")
        print(f"   الإجمالي النشط: {stats.get('total_active', 0)}")
        print(f"   الإجمالي العام: {stats.get('grand_total', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🌟 إصلاح ودمج نظام حالات الموظفين")
    print("=" * 60)
    
    # فحص النظام الموجود
    existing_tables = check_existing_system()
    
    # إنشاء النظام المحسن
    if create_enhanced_status_system():
        print("✅ تم إنشاء النظام المحسن")
        
        # إنشاء مدير الحالات
        create_enhanced_status_manager()
        
        # اختبار النظام
        if test_enhanced_system():
            print("\n🎉 النظام المحسن جاهز للاستخدام!")
            
            print(f"\n📋 الجداول الجديدة:")
            print(f"   - leave_of_absence_reasons: أسباب الاستيداع")
            print(f"   - enhanced_leave_of_absence: الاستيداع")
            print(f"   - enhanced_suspension: التوقيف")
            print(f"   - enhanced_resignation: الاستقالة")
            print(f"   - archived_deceased_employees: الوفيات")
            print(f"   - archived_external_transfer_employees: التحويل الخارجي")
            print(f"   - enhanced_status_history: تاريخ التغييرات")
            
            print(f"\n🚀 الخطوة التالية:")
            print(f"   python app.py")
            
        else:
            print("❌ فشل في اختبار النظام")
    else:
        print("❌ فشل في إنشاء النظام المحسن")

if __name__ == "__main__":
    main()