#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام النهائي - التأكد من اكتمال جميع المكونات
Final System Check - Verify All Components Are Complete
"""

import os
import sys
import sqlite3
import importlib
from datetime import datetime

def print_header():
    """طباعة رأس الفحص"""
    print("=" * 80)
    print("🔍 فحص النظام النهائي - نظام إدارة موظفي الجمارك الجزائرية")
    print("   Final System Check - Algerian Customs Employee Management System")
    print("=" * 80)
    print(f"📅 تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

def check_python_environment():
    """فحص بيئة Python"""
    print("\n🐍 فحص بيئة Python:")
    print("-" * 40)
    
    # إصدار Python
    version = sys.version_info
    print(f"📌 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✅ إصدار Python مناسب")
        python_ok = True
    else:
        print("❌ إصدار Python غير مناسب (يتطلب 3.8+)")
        python_ok = False
    
    # فحص المكتبات المطلوبة
    required_modules = [
        'flask', 'sqlite3', 'PIL', 'werkzeug', 'os', 'sys', 
        'datetime', 're', 'base64', 'io'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            if module == 'PIL':
                importlib.import_module('PIL')
            else:
                importlib.import_module(module)
            print(f"✅ {module} متوفر")
        except ImportError:
            print(f"❌ {module} مفقود")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"⚠️  المكتبات المفقودة: {', '.join(missing_modules)}")
        return False
    
    return python_ok

def check_core_files():
    """فحص الملفات الأساسية"""
    print("\n📁 فحص الملفات الأساسية:")
    print("-" * 40)
    
    core_files = {
        'app.py': 'التطبيق الرئيسي',
        'init_database.py': 'إنشاء قاعدة البيانات',
        'launch_system.py': 'تشغيل محسن',
        'start_system.bat': 'تشغيل Windows',
        'run.py': 'تشغيل تقليدي',
        'requirements.txt': 'متطلبات المشروع',
        'check_system.py': 'فحص النظام'
    }
    
    all_present = True
    for file_path, description in core_files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({description}) - {size} bytes")
        else:
            print(f"❌ {file_path} ({description}) - مفقود")
            all_present = False
    
    return all_present

def check_status_modules():
    """فحص وحدات إدارة الحالات"""
    print("\n🎛️  فحص وحدات إدارة الحالات:")
    print("-" * 40)
    
    status_modules = {
        'employee_status_manager.py': 'إدارة الحالات الرئيسية',
        'employee_status_api.py': 'واجهة برمجة التطبيقات',
        'employee_status_helpers.py': 'مساعدات ووظائف مساندة',
        'employee_status_reports.py': 'تقارير الحالات',
        'status_integration.py': 'تكامل الحالات مع النظام'
    }
    
    all_present = True
    for module_path, description in status_modules.items():
        if os.path.exists(module_path):
            size = os.path.getsize(module_path)
            print(f"✅ {module_path} ({description}) - {size} bytes")
        else:
            print(f"❌ {module_path} ({description}) - مفقود")
            all_present = False
    
    return all_present

def check_templates():
    """فحص القوالب"""
    print("\n🎨 فحص القوالب:")
    print("-" * 40)
    
    template_dirs = [
        'templates',
        'templates/employees',
        'templates/employee_status',
        'templates/employee_statuses',
        'templates/leaves',
        'templates/certificates',
        'templates/promotions',
        'templates/transfers',
        'templates/sanctions',
        'templates/reports',
        'templates/statistics',
        'templates/reference_data',
        'templates/settings'
    ]
    
    essential_templates = [
        'templates/base.html',
        'templates/index.html',
        'templates/employees/list.html',
        'templates/employees/add.html',
        'templates/employees/edit.html',
        'templates/employees/view.html'
    ]
    
    # فحص المجلدات
    dirs_ok = True
    for dir_path in template_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            count = len([f for f in os.listdir(dir_path) if f.endswith('.html')])
            print(f"✅ {dir_path} - {count} ملف HTML")
        else:
            print(f"❌ {dir_path} - مجلد مفقود")
            dirs_ok = False
    
    # فحص القوالب الأساسية
    templates_ok = True
    for template_path in essential_templates:
        if os.path.exists(template_path):
            size = os.path.getsize(template_path)
            print(f"✅ {template_path} - {size} bytes")
        else:
            print(f"❌ {template_path} - مفقود")
            templates_ok = False
    
    return dirs_ok and templates_ok

def check_static_files():
    """فحص الملفات الثابتة"""
    print("\n🎨 فحص الملفات الثابتة:")
    print("-" * 40)
    
    static_dirs = [
        'static',
        'static/css',
        'static/uploads'
    ]
    
    all_present = True
    for dir_path in static_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            files = os.listdir(dir_path)
            print(f"✅ {dir_path} - {len(files)} ملف")
        else:
            print(f"❌ {dir_path} - مجلد مفقود")
            all_present = False
    
    # فحص ملف CSS الخاص بالحالات
    css_file = 'static/css/employee-status.css'
    if os.path.exists(css_file):
        size = os.path.getsize(css_file)
        print(f"✅ {css_file} - {size} bytes")
    else:
        print(f"⚠️  {css_file} - مفقود (اختياري)")
    
    return all_present

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️  فحص قاعدة البيانات:")
    print("-" * 40)
    
    db_path = 'customs_employees.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        essential_tables = [
            'employees', 'wilayas', 'communes', 'ranks', 'corps', 'services',
            'annual_leaves', 'sick_leaves', 'other_leaves', 'certificates',
            'training', 'rank_promotions', 'grade_promotions', 'transfers',
            'sanctions'
        ]
        
        # فحص جداول إدارة الحالات
        status_tables = [
            'employee_deaths', 'employee_leave_of_absence', 'employee_suspensions',
            'employee_assignments', 'employee_resignations', 'employee_external_transfers',
            'employee_long_term_leaves', 'employee_retirements', 'leave_of_absence_reasons'
        ]
        
        all_tables = essential_tables + status_tables
        missing_tables = []
        
        for table in all_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                # حساب عدد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ جدول {table} - {count} سجل")
            else:
                print(f"❌ جدول {table} - مفقود")
                missing_tables.append(table)
        
        # إحصائيات عامة
        cursor.execute("SELECT COUNT(*) FROM employees")
        employee_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM wilayas")
        wilaya_count = cursor.fetchone()[0]
        
        print(f"\n📊 إحصائيات قاعدة البيانات:")
        print(f"   👥 الموظفين: {employee_count}")
        print(f"   🏛️  الولايات: {wilaya_count}")
        
        conn.close()
        
        if missing_tables:
            print(f"⚠️  الجداول المفقودة: {', '.join(missing_tables)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_documentation():
    """فحص التوثيق"""
    print("\n📚 فحص التوثيق:")
    print("-" * 40)
    
    doc_files = {
        'README.md': 'الدليل الأساسي',
        'README_FINAL.md': 'الدليل النهائي',
        'USER_GUIDE.md': 'دليل المستخدم',
        'PROJECT_COMPLETE.md': 'تقرير إكمال المشروع',
        'SYSTEM_STATUS.md': 'حالة النظام',
        'SYSTEM_SUMMARY.md': 'ملخص النظام',
        'EMPLOYEE_STATUS_SYSTEM.md': 'دليل نظام الحالات',
        'STATUS_SYSTEM_SUMMARY.md': 'ملخص نظام الحالات'
    }
    
    all_present = True
    total_size = 0
    
    for doc_path, description in doc_files.items():
        if os.path.exists(doc_path):
            size = os.path.getsize(doc_path)
            total_size += size
            print(f"✅ {doc_path} ({description}) - {size} bytes")
        else:
            print(f"❌ {doc_path} ({description}) - مفقود")
            all_present = False
    
    print(f"\n📊 إجمالي حجم التوثيق: {total_size:,} bytes")
    
    return all_present

def check_functionality():
    """فحص الوظائف الأساسية"""
    print("\n⚙️  فحص الوظائف الأساسية:")
    print("-" * 40)
    
    try:
        # محاولة استيراد التطبيق الرئيسي
        sys.path.insert(0, '.')
        
        # فحص استيراد الوحدات الأساسية
        try:
            import app
            print("✅ استيراد app.py نجح")
        except Exception as e:
            print(f"❌ فشل استيراد app.py: {e}")
            return False
        
        # فحص استيراد وحدات الحالات
        try:
            import employee_status_manager
            import employee_status_api
            import status_integration
            print("✅ استيراد وحدات إدارة الحالات نجح")
        except Exception as e:
            print(f"⚠️  تحذير: فشل استيراد وحدات الحالات: {e}")
        
        # فحص الدوال الأساسية
        if hasattr(app, 'get_db_connection'):
            print("✅ دالة الاتصال بقاعدة البيانات موجودة")
        else:
            print("❌ دالة الاتصال بقاعدة البيانات مفقودة")
            return False
        
        if hasattr(app, 'validate_registration_number'):
            print("✅ دالة التحقق من رقم التسجيل موجودة")
        else:
            print("❌ دالة التحقق من رقم التسجيل مفقودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الوظائف: {e}")
        return False

def generate_final_report():
    """إنشاء تقرير نهائي"""
    print("\n" + "=" * 80)
    print("📋 التقرير النهائي:")
    print("=" * 80)
    
    # تشغيل جميع الفحوصات
    checks = {
        'بيئة Python': check_python_environment(),
        'الملفات الأساسية': check_core_files(),
        'وحدات إدارة الحالات': check_status_modules(),
        'القوالب': check_templates(),
        'الملفات الثابتة': check_static_files(),
        'قاعدة البيانات': check_database(),
        'التوثيق': check_documentation(),
        'الوظائف الأساسية': check_functionality()
    }
    
    # عرض النتائج
    passed = 0
    total = len(checks)
    
    for check_name, result in checks.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print("-" * 80)
    print(f"📊 النتيجة النهائية: {passed}/{total} فحص نجح")
    
    percentage = (passed / total) * 100
    print(f"📈 نسبة الاكتمال: {percentage:.1f}%")
    
    if percentage == 100:
        print("\n🎉 تهانينا! النظام مكتمل 100% وجاهز للاستخدام!")
        print("🚀 يمكنك تشغيل النظام باستخدام: python launch_system.py")
    elif percentage >= 90:
        print("\n✅ النظام شبه مكتمل ويمكن استخدامه مع بعض التحسينات")
    elif percentage >= 70:
        print("\n⚠️  النظام يحتاج إلى بعض الإصلاحات قبل الاستخدام")
    else:
        print("\n❌ النظام يحتاج إلى إصلاحات كبيرة")
    
    return percentage

def main():
    """الدالة الرئيسية"""
    print_header()
    
    try:
        completion_percentage = generate_final_report()
        
        print("\n" + "=" * 80)
        print("🔗 روابط مفيدة:")
        print("   📖 دليل المستخدم: USER_GUIDE.md")
        print("   🚀 تشغيل النظام: python launch_system.py")
        print("   🌐 الوصول للنظام: http://localhost:5000")
        print("=" * 80)
        
        return completion_percentage >= 90
        
    except Exception as e:
        print(f"\n❌ خطأ في الفحص النهائي: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الفحص بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)