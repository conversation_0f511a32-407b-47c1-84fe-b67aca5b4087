#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية - النسخة النهائية
Final Algerian Customs Employee Management System
"""

from flask import Flask, render_template
import sqlite3
import os

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'final_customs_2025'

# إعدادات التطبيق
app.config['DATABASE'] = 'customs_employees.db'
app.config['UPLOAD_FOLDER'] = 'static/uploads'

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(app.config['DATABASE'])
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات"""
    conn = get_db_connection()
    
    # جدول الموظفين الأساسي
    conn.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            first_name_ar TEXT NOT NULL,
            last_name_ar TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()

# الصفحات الأساسية
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    conn = get_db_connection()
    stats = {
        'total_employees': conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0],
        'active_leaves': 0,
        'pending_certificates': 0,
        'recent_training': 0
    }
    conn.close()
    return render_template('dashboard.html', stats=stats)

@app.route('/employees')
def employees():
    conn = get_db_connection()
    employees = conn.execute('SELECT * FROM employees ORDER BY registration_number').fetchall()
    conn.close()
    return render_template('employees/index.html', employees=employees)

@app.route('/leaves')
def leaves():
    return render_template('leaves/index.html')

@app.route('/certificates')
def certificates():
    return render_template('certificates/index.html')

@app.route('/employee_statuses')
def employee_statuses():
    return render_template('employee_statuses/index.html')

@app.route('/settings')
def settings():
    return render_template('settings/index.html')

@app.route('/promotions')
def promotions():
    return render_template('promotions/index.html')

@app.route('/transfers')
def transfers():
    return render_template('transfers/index.html')

@app.route('/statistics')
def statistics():
    return render_template('statistics/index.html')

@app.route('/sanctions')
def sanctions():
    return render_template('sanctions/index.html')

if __name__ == '__main__':
    # إنشاء مجلدات التحميل
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # تهيئة قاعدة البيانات
    init_database()
    
    # تشغيل التطبيق
    print("🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - النسخة النهائية")
    print("🌐 http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
