# 🇩🇿 نظام إدارة موظفي الجمارك الجزائرية الجديد
# 🇩🇿 New Algerian Customs Employee Management System

## 🎨 تصميم جديد مستوحى من موقع بريد الجزائر
## 🎨 New Design Inspired by Algeria Post Website

---

## ✨ المميزات الجديدة / New Features

### 🎯 التصميم والواجهة / Design & Interface
- **تصميم احترافي** مستوحى من موقع بريد الجزائر الرسمي
- **أيقونات سداسية تفاعلية** مع تأثيرات بصرية جميلة
- **تدرجات لونية أنيقة** وخلفيات احترافية
- **تصميم متجاوب** يعمل على جميع الأجهزة (هاتف، تابلت، كمبيوتر)
- **خطوط عربية جميلة** (Cairo Font) لتجربة قراءة مثالية

### 🚀 الوظائف الأساسية / Core Functions
- **إدارة شاملة للموظفين** مع جميع البيانات المطلوبة
- **نظام العطل والإجازات** (سنوية، مرضية، أخرى)
- **إدارة الشهادات والتكوين** الأكاديمي والمهني
- **حالات الموظفين** (استيداع، وفاة، انتداب، تحويل، خدمة وطنية، توقيف)
- **الإعدادات والتكوين** للنظام والبيانات المرجعية

### 🔧 التقنيات المستخدمة / Technologies Used
- **Backend**: Flask (Python 3.x)
- **Frontend**: Bootstrap 5, CSS Grid, JavaScript
- **Database**: SQLite
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Cairo)

---

## 🚀 التشغيل السريع / Quick Start

### 1. تشغيل النظام / Run System
```bash
python run_new_system.py
```

### 2. فتح المتصفح / Open Browser
```
http://localhost:5000
```

---

## 📁 هيكل المشروع / Project Structure

```
📦 نظام إدارة موظفي الجمارك
├── 📄 app.py                    # التطبيق الرئيسي
├── 📄 run_new_system.py         # ملف التشغيل
├── 📄 requirements.txt          # المتطلبات
├── 📄 customs_employees.db      # قاعدة البيانات
├── 📁 templates/                # القوالب
│   ├── 📄 base.html            # القالب الأساسي
│   ├── 📄 index.html           # الصفحة الرئيسية
│   ├── 📄 dashboard.html       # لوحة التحكم
│   ├── 📁 employees/           # صفحات الموظفين
│   ├── 📁 leaves/              # صفحات العطل
│   ├── 📁 certificates/        # صفحات الشهادات
│   ├── 📁 employee_statuses/   # صفحات حالات الموظفين
│   └── 📁 settings/            # صفحات الإعدادات
├── 📁 static/                   # الملفات الثابتة
│   ├── 📁 css/
│   │   └── 📄 style.css        # التصميم الرئيسي
│   ├── 📁 js/
│   │   └── 📄 main.js          # JavaScript الرئيسي
│   └── 📁 uploads/             # ملفات التحميل
└── 📄 README_NEW_SYSTEM.md     # هذا الملف
```

---

## 🎨 لقطات الشاشة / Screenshots

### 🏠 الصفحة الرئيسية / Home Page
- تصميم hero section مع خلفية متدرجة
- أيقونات سداسية تفاعلية للخدمات
- بطاقات المميزات مع أيقونات جميلة

### 📊 لوحة التحكم / Dashboard
- إحصائيات سريعة مع بطاقات ملونة
- إجراءات سريعة للوظائف الأساسية
- خط زمني للأنشطة الأخيرة

### 👥 إدارة الموظفين / Employee Management
- جدول تفاعلي مع البحث والتصفية
- نماذج منبثقة لإضافة/تعديل البيانات
- عرض صور الموظفين

### 📅 العطل والإجازات / Leaves Management
- بطاقات ملونة لأنواع العطل المختلفة
- تبويبات للتنقل بين أنواع العطل
- حساب تلقائي لعدد أيام العطلة

---

## 🔧 الإعدادات / Configuration

### قاعدة البيانات / Database
- **النوع**: SQLite
- **الملف**: `customs_employees.db`
- **التهيئة**: تلقائية عند أول تشغيل

### الخادم / Server
- **المنفذ**: 5000
- **العنوان**: localhost
- **وضع التطوير**: مفعل

---

## 📋 الوظائف المتاحة / Available Functions

### 👥 إدارة الموظفين / Employee Management
- ✅ إضافة موظف جديد
- ✅ تعديل بيانات الموظف
- ✅ عرض تفاصيل الموظف
- ✅ البحث والتصفية
- ✅ تصدير البيانات

### 📅 العطل والإجازات / Leaves & Vacations
- ✅ العطل السنوية
- ✅ العطل المرضية
- ✅ العطل الأخرى
- ✅ حساب الرصيد المتاح
- ✅ تتبع العطل النشطة

### 🎓 الشهادات والتكوين / Certificates & Training
- ✅ الشهادات الأكاديمية
- ✅ الدورات التدريبية
- ✅ تصنيف حسب النوع
- ✅ تتبع التقدم

### 👤 حالات الموظفين / Employee Statuses
- ✅ استيداع
- ✅ وفاة
- ✅ انتداب
- ✅ تحويل
- ✅ خدمة وطنية
- ✅ توقيف

### ⚙️ الإعدادات / Settings
- ✅ الهيكل التنظيمي
- ✅ الولايات والبلديات
- ✅ إدارة المستخدمين
- ✅ النسخ الاحتياطي
- ✅ التصدير والاستيراد

---

## 🎯 المميزات التقنية / Technical Features

### 🔒 الأمان / Security
- حماية من SQL Injection
- تشفير كلمات المرور
- جلسات آمنة

### 📱 الاستجابة / Responsiveness
- تصميم متجاوب بالكامل
- يعمل على جميع أحجام الشاشات
- تحسين للأجهزة المحمولة

### ⚡ الأداء / Performance
- تحميل سريع للصفحات
- استعلامات محسنة لقاعدة البيانات
- ضغط الملفات الثابتة

---

## 🆘 الدعم والمساعدة / Support & Help

### 🐛 الإبلاغ عن الأخطاء / Bug Reports
إذا واجهت أي مشاكل، يرجى التحقق من:
1. تشغيل Python 3.x
2. تثبيت المتطلبات من `requirements.txt`
3. وجود جميع الملفات المطلوبة

### 📞 التواصل / Contact
- **المطور**: نظام إدارة موظفي الجمارك
- **التاريخ**: 2025
- **الإصدار**: 1.0.0

---

## 📝 ملاحظات مهمة / Important Notes

1. **النسخ الاحتياطي**: يُنصح بعمل نسخة احتياطية من قاعدة البيانات بانتظام
2. **التحديثات**: تحقق من التحديثات الجديدة للنظام
3. **الأمان**: غيّر كلمات المرور الافتراضية
4. **الصيانة**: راقب أداء النظام وحجم قاعدة البيانات

---

## 🎉 شكر خاص / Special Thanks

شكر خاص لفريق تطوير موقع بريد الجزائر على التصميم الملهم والاحترافي الذي استوحينا منه هذا النظام.

---

**🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - تصميم جديد 2025**
