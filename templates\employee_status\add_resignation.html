{% extends "base.html" %}

{% block title %}إضافة استقالة - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-minus"></i>
                        تسجيل استقالة
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user"></i> معلومات الموظف</h5>
                        <strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}<br>
                        <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                        <strong>الحالة الحالية:</strong> <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> تسجيل الاستقالة سيغير حالة الموظف إلى "استقالة معلقة" حتى يتم اتخاذ قرار بشأنها.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="resignation_date">تاريخ تقديم الاستقالة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="resignation_date" name="resignation_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ تقديم الاستقالة
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notice_period_days">فترة الإشعار (بالأيام) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="notice_period_days" name="notice_period_days" 
                                           value="30" min="1" max="90" required>
                                    <small class="form-text text-muted">عادة 30 يوم حسب القانون</small>
                                    <div class="invalid-feedback">
                                        يرجى إدخال فترة إشعار صحيحة (1-90 يوم)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_working_day">آخر يوم عمل <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="last_working_day" name="last_working_day" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال آخر يوم عمل
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">تاريخ القرار</label>
                                    <input type="date" class="form-control" id="decision_date" name="decision_date">
                                    <small class="form-text text-muted">يملأ عند اتخاذ القرار</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">رقم القرار</label>
                                    <input type="text" class="form-control" id="decision_number" name="decision_number" placeholder="مثال: 2024/RES/001">
                                    <small class="form-text text-muted">يملأ عند اتخاذ القرار</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="reason">سبب الاستقالة <span class="text-danger">*</span></label>
                            <select class="form-control" id="reason_select" onchange="handleReasonChange()">
                                <option value="">اختر سبب الاستقالة</option>
                                <option value="أسباب شخصية">أسباب شخصية</option>
                                <option value="فرصة عمل أفضل">فرصة عمل أفضل</option>
                                <option value="ظروف عائلية">ظروف عائلية</option>
                                <option value="الانتقال لمدينة أخرى">الانتقال لمدينة أخرى</option>
                                <option value="عدم الرضا عن العمل">عدم الرضا عن العمل</option>
                                <option value="أسباب صحية">أسباب صحية</option>
                                <option value="التفرغ للدراسة">التفرغ للدراسة</option>
                                <option value="أخرى">أخرى (حدد)</option>
                            </select>
                            <textarea class="form-control mt-2" id="reason" name="reason" rows="3" required 
                                      placeholder="اذكر سبب الاستقالة بالتفصيل"></textarea>
                            <div class="invalid-feedback">
                                يرجى إدخال سبب الاستقالة
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                      placeholder="أي ملاحظات إضافية حول الاستقالة (اختياري)"></textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm" required>
                                <label class="custom-control-label" for="confirm">
                                    أؤكد صحة البيانات المدخلة وأن هذه استقالة رسمية
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد صحة البيانات
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> تسجيل الاستقالة
                            </button>
                            <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// حساب آخر يوم عمل تلقائياً
document.getElementById('resignation_date').addEventListener('change', calculateLastWorkingDay);
document.getElementById('notice_period_days').addEventListener('change', calculateLastWorkingDay);

function calculateLastWorkingDay() {
    var resignationDate = document.getElementById('resignation_date').value;
    var noticePeriod = parseInt(document.getElementById('notice_period_days').value);
    
    if (resignationDate && noticePeriod) {
        var startDate = new Date(resignationDate);
        var lastWorkingDay = new Date(startDate);
        lastWorkingDay.setDate(lastWorkingDay.getDate() + noticePeriod);
        
        document.getElementById('last_working_day').value = lastWorkingDay.toISOString().split('T')[0];
    }
}

// معالجة تغيير سبب الاستقالة
function handleReasonChange() {
    var select = document.getElementById('reason_select');
    var textarea = document.getElementById('reason');
    
    if (select.value && select.value !== 'أخرى') {
        textarea.value = select.value;
    } else if (select.value === 'أخرى') {
        textarea.value = '';
        textarea.focus();
    }
}

// التحقق من التواريخ
document.getElementById('resignation_date').addEventListener('change', function() {
    var resignationDate = new Date(this.value);
    var today = new Date();
    
    if (resignationDate > today) {
        alert('تاريخ تقديم الاستقالة لا يمكن أن يكون في المستقبل');
        this.value = '';
    }
});

// تعيين تاريخ اليوم كافتراضي لتاريخ الاستقالة
document.addEventListener('DOMContentLoaded', function() {
    var today = new Date().toISOString().split('T')[0];
    document.getElementById('resignation_date').value = today;
    calculateLastWorkingDay();
});
</script>
{% endblock %}