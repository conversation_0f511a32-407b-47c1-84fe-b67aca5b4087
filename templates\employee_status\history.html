{% extends "base.html" %}

{% block title %}تاريخ حالات الموظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i>
                        تاريخ حالات الموظف: {{ employee.first_name }} {{ employee.last_name }}
                    </h3>
                    <div class="card-tools">
                        <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                            <strong>الرتبة:</strong> {{ employee.rank_name or 'غير محدد' }}<br>
                            <strong>المصلحة:</strong> {{ employee.service_name or 'غير محدد' }}
                        </div>
                        <div class="col-md-6">
                            <strong>الحالة الحالية:</strong> 
                            <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                        </div>
                    </div>
                    
                    <!-- أزرار إضافة حالات جديدة -->
                    {% if employee.status == 'نشط' %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>إضافة حالة جديدة:</h5>
                            <div class="btn-group flex-wrap" role="group">
                                <a href="{{ url_for('add_long_term_leave', employee_id=employee.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-calendar-plus"></i> عطلة طويلة الأمد
                                </a>
                                <a href="{{ url_for('add_resignation', employee_id=employee.id) }}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-user-minus"></i> استقالة
                                </a>
                                <a href="{{ url_for('add_leave_of_absence', employee_id=employee.id) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-pause"></i> استيداع
                                </a>
                                <a href="{{ url_for('add_suspension', employee_id=employee.id) }}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-ban"></i> توقيف
                                </a>
                                <a href="{{ url_for('add_retirement', employee_id=employee.id) }}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-user-clock"></i> تقاعد
                                </a>
                                <a href="{{ url_for('add_external_transfer', employee_id=employee.id) }}" class="btn btn-dark btn-sm">
                                    <i class="fas fa-exchange-alt"></i> تحويل خارجي
                                </a>
                                <a href="{{ url_for('add_assignment', employee_id=employee.id) }}" class="btn btn-success btn-sm">
                                    <i class="fas fa-briefcase"></i> انتداب
                                </a>
                                <a href="{{ url_for('add_death_record', employee_id=employee.id) }}" class="btn btn-outline-dark btn-sm">
                                    <i class="fas fa-cross"></i> وفاة
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- تاريخ الحالات -->
                    <div class="timeline">
                        <!-- العطل طويلة الأمد -->
                        {% for leave in history.long_term_leaves %}
                        <div class="time-label">
                            <span class="bg-primary">{{ leave.start_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-calendar-alt bg-primary"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ leave.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">عطلة طويلة الأمد - {{ leave.leave_type }}</h3>
                                <div class="timeline-body">
                                    <strong>السبب:</strong> {{ leave.reason }}<br>
                                    <strong>تاريخ البداية:</strong> {{ leave.start_date|format_arabic_date }}<br>
                                    <strong>تاريخ الانتهاء المتوقع:</strong> {{ leave.expected_end_date|format_arabic_date }}<br>
                                    {% if leave.actual_end_date %}
                                    <strong>تاريخ الانتهاء الفعلي:</strong> {{ leave.actual_end_date|format_arabic_date }}<br>
                                    {% endif %}
                                    <strong>الحالة:</strong> <span class="badge badge-{{ 'success' if leave.status == 'نشط' else 'secondary' }}">{{ leave.status }}</span><br>
                                    {% if leave.decision_number %}
                                    <strong>رقم القرار:</strong> {{ leave.decision_number }}<br>
                                    {% endif %}
                                    {% if leave.notes %}
                                    <strong>ملاحظات:</strong> {{ leave.notes }}
                                    {% endif %}
                                </div>
                                {% if leave.status == 'نشط' %}
                                <div class="timeline-footer">
                                    <button class="btn btn-warning btn-sm" onclick="endLeave({{ leave.id }}, 'long_term_leave')">
                                        إنهاء العطلة
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- الاستقالات -->
                        {% for resignation in history.resignations %}
                        <div class="time-label">
                            <span class="bg-warning">{{ resignation.resignation_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-user-minus bg-warning"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ resignation.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">استقالة</h3>
                                <div class="timeline-body">
                                    <strong>السبب:</strong> {{ resignation.reason }}<br>
                                    <strong>تاريخ الاستقالة:</strong> {{ resignation.resignation_date|format_arabic_date }}<br>
                                    <strong>آخر يوم عمل:</strong> {{ resignation.last_working_day|format_arabic_date }}<br>
                                    <strong>فترة الإشعار:</strong> {{ resignation.notice_period_days }} يوم<br>
                                    <strong>الحالة:</strong> <span class="badge badge-{{ 'success' if resignation.status == 'موافق عليها' else 'warning' }}">{{ resignation.status }}</span><br>
                                    {% if resignation.decision_number %}
                                    <strong>رقم القرار:</strong> {{ resignation.decision_number }}<br>
                                    {% endif %}
                                    {% if resignation.notes %}
                                    <strong>ملاحظات:</strong> {{ resignation.notes }}
                                    {% endif %}
                                </div>
                                {% if resignation.status == 'معلقة' %}
                                <div class="timeline-footer">
                                    <button class="btn btn-success btn-sm" onclick="approveResignation({{ resignation.id }})">
                                        الموافقة على الاستقالة
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- الاستيداع -->
                        {% for absence in history.leave_of_absence %}
                        <div class="time-label">
                            <span class="bg-info">{{ absence.start_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-pause bg-info"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ absence.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">استيداع - {{ absence.absence_type }}</h3>
                                <div class="timeline-body">
                                    <strong>السبب:</strong> {{ absence.reason }}<br>
                                    <strong>تاريخ البداية:</strong> {{ absence.start_date|format_arabic_date }}<br>
                                    <strong>المدة المتوقعة:</strong> {{ absence.expected_duration_months }} شهر<br>
                                    {% if absence.actual_end_date %}
                                    <strong>تاريخ الانتهاء الفعلي:</strong> {{ absence.actual_end_date|format_arabic_date }}<br>
                                    {% endif %}
                                    <strong>الحالة:</strong> <span class="badge badge-{{ 'success' if absence.status == 'نشط' else 'secondary' }}">{{ absence.status }}</span><br>
                                    {% if absence.decision_number %}
                                    <strong>رقم القرار:</strong> {{ absence.decision_number }}<br>
                                    {% endif %}
                                </div>
                                {% if absence.status == 'نشط' %}
                                <div class="timeline-footer">
                                    <button class="btn btn-warning btn-sm" onclick="endAbsence({{ absence.id }})">
                                        إنهاء الاستيداع
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- التوقيفات -->
                        {% for suspension in history.suspensions %}
                        <div class="time-label">
                            <span class="bg-danger">{{ suspension.suspension_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-ban bg-danger"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ suspension.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">توقيف - {{ suspension.suspension_type }}</h3>
                                <div class="timeline-body">
                                    <strong>السبب:</strong> {{ suspension.reason }}<br>
                                    <strong>تاريخ التوقيف:</strong> {{ suspension.suspension_date|format_arabic_date }}<br>
                                    {% if suspension.duration_days %}
                                    <strong>المدة:</strong> {{ suspension.duration_days }} يوم<br>
                                    {% endif %}
                                    {% if suspension.actual_end_date %}
                                    <strong>تاريخ الانتهاء الفعلي:</strong> {{ suspension.actual_end_date|format_arabic_date }}<br>
                                    {% endif %}
                                    <strong>الحالة:</strong> <span class="badge badge-{{ 'danger' if suspension.status == 'نشط' else 'secondary' }}">{{ suspension.status }}</span><br>
                                    {% if suspension.decision_number %}
                                    <strong>رقم القرار:</strong> {{ suspension.decision_number }}<br>
                                    {% endif %}
                                </div>
                                {% if suspension.status == 'نشط' %}
                                <div class="timeline-footer">
                                    <button class="btn btn-success btn-sm" onclick="endSuspension({{ suspension.id }})">
                                        إنهاء التوقيف
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- التحويلات الخارجية -->
                        {% for transfer in history.external_transfers %}
                        <div class="time-label">
                            <span class="bg-dark">{{ transfer.transfer_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-exchange-alt bg-dark"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ transfer.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">تحويل خارجي</h3>
                                <div class="timeline-body">
                                    <strong>الجهة المحول إليها:</strong> {{ transfer.destination_organization }}<br>
                                    {% if transfer.destination_department %}
                                    <strong>القسم:</strong> {{ transfer.destination_department }}<br>
                                    {% endif %}
                                    <strong>تاريخ التحويل:</strong> {{ transfer.transfer_date|format_arabic_date }}<br>
                                    {% if transfer.reason %}
                                    <strong>السبب:</strong> {{ transfer.reason }}<br>
                                    {% endif %}
                                    {% if transfer.decision_number %}
                                    <strong>رقم القرار:</strong> {{ transfer.decision_number }}<br>
                                    {% endif %}
                                    <div class="alert alert-warning mt-2">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>ملاحظة:</strong> الموظف لم يعد ضمن تعداد المؤسسة
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- الانتدابات -->
                        {% for assignment in history.assignments %}
                        <div class="time-label">
                            <span class="bg-success">{{ assignment.assignment_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-briefcase bg-success"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ assignment.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">انتداب - {{ assignment.assignment_type }}</h3>
                                <div class="timeline-body">
                                    <strong>مكان الانتداب:</strong> {{ assignment.assignment_location }}<br>
                                    <strong>تاريخ الانتداب:</strong> {{ assignment.assignment_date|format_arabic_date }}<br>
                                    <strong>المدة المتوقعة:</strong> {{ assignment.expected_duration_months }} شهر<br>
                                    {% if assignment.actual_end_date %}
                                    <strong>تاريخ الانتهاء الفعلي:</strong> {{ assignment.actual_end_date|format_arabic_date }}<br>
                                    {% endif %}
                                    <strong>الحالة:</strong> <span class="badge badge-{{ 'success' if assignment.status == 'نشط' else 'secondary' }}">{{ assignment.status }}</span><br>
                                    {% if assignment.decision_number %}
                                    <strong>رقم القرار:</strong> {{ assignment.decision_number }}<br>
                                    {% endif %}
                                </div>
                                {% if assignment.status == 'نشط' %}
                                <div class="timeline-footer">
                                    <button class="btn btn-warning btn-sm" onclick="endAssignment({{ assignment.id }})">
                                        إنهاء الانتداب
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- التقاعد -->
                        {% for retirement in history.retirements %}
                        <div class="time-label">
                            <span class="bg-secondary">{{ retirement.retirement_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-user-clock bg-secondary"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ retirement.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">تقاعد - {{ retirement.retirement_type }}</h3>
                                <div class="timeline-body">
                                    <strong>تاريخ التقاعد:</strong> {{ retirement.retirement_date|format_arabic_date }}<br>
                                    <strong>سنوات الخدمة:</strong> {{ retirement.years_of_service }} سنة<br>
                                    {% if retirement.pension_amount %}
                                    <strong>مبلغ المعاش:</strong> {{ retirement.pension_amount }} دج<br>
                                    {% endif %}
                                    {% if retirement.decision_number %}
                                    <strong>رقم القرار:</strong> {{ retirement.decision_number }}<br>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- الوفيات -->
                        {% for death in history.deaths %}
                        <div class="time-label">
                            <span class="bg-dark">{{ death.death_date|format_arabic_date }}</span>
                        </div>
                        <div>
                            <i class="fas fa-cross bg-dark"></i>
                            <div class="timeline-item">
                                <span class="time"><i class="fas fa-clock"></i> {{ death.created_at|format_arabic_date }}</span>
                                <h3 class="timeline-header">وفاة</h3>
                                <div class="timeline-body">
                                    <strong>تاريخ الوفاة:</strong> {{ death.death_date|format_arabic_date }}<br>
                                    {% if death.death_place %}
                                    <strong>مكان الوفاة:</strong> {{ death.death_place }}<br>
                                    {% endif %}
                                    {% if death.death_cause %}
                                    <strong>سبب الوفاة:</strong> {{ death.death_cause }}<br>
                                    {% endif %}
                                    {% if death.certificate_number %}
                                    <strong>رقم شهادة الوفاة:</strong> {{ death.certificate_number }}<br>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- نهاية التايم لاين -->
                        <div>
                            <i class="fas fa-clock bg-gray"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نماذج منبثقة للإجراءات -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="actionModalTitle">تأكيد الإجراء</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="actionForm" method="POST">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="actionDate">التاريخ:</label>
                        <input type="date" class="form-control" id="actionDate" name="end_date" required>
                    </div>
                    <div class="form-group">
                        <label for="actionNotes">ملاحظات:</label>
                        <textarea class="form-control" id="actionNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تأكيد</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function endLeave(leaveId, type) {
    $('#actionModalTitle').text('إنهاء العطلة');
    $('#actionForm').attr('action', '{{ url_for("end_long_term_leave", leave_id=0) }}'.replace('0', leaveId));
    $('#actionDate').val(new Date().toISOString().split('T')[0]);
    $('#actionModal').modal('show');
}

function endAbsence(absenceId) {
    $('#actionModalTitle').text('إنهاء الاستيداع');
    $('#actionForm').attr('action', '{{ url_for("end_leave_of_absence", absence_id=0) }}'.replace('0', absenceId));
    $('#actionDate').val(new Date().toISOString().split('T')[0]);
    $('#actionModal').modal('show');
}

function endSuspension(suspensionId) {
    $('#actionModalTitle').text('إنهاء التوقيف');
    $('#actionForm').attr('action', '{{ url_for("end_suspension", suspension_id=0) }}'.replace('0', suspensionId));
    $('#actionDate').val(new Date().toISOString().split('T')[0]);
    $('#actionModal').modal('show');
}

function endAssignment(assignmentId) {
    $('#actionModalTitle').text('إنهاء الانتداب');
    $('#actionForm').attr('action', '{{ url_for("end_assignment", assignment_id=0) }}'.replace('0', assignmentId));
    $('#actionDate').val(new Date().toISOString().split('T')[0]);
    $('#actionModal').modal('show');
}

function approveResignation(resignationId) {
    $('#actionModalTitle').text('الموافقة على الاستقالة');
    $('#actionForm').attr('action', '{{ url_for("approve_resignation", resignation_id=0) }}'.replace('0', resignationId));
    $('#actionDate').attr('name', 'approval_date');
    $('#actionNotes').attr('name', 'notes');
    $('#actionDate').val(new Date().toISOString().split('T')[0]);
    $('#actionModal').modal('show');
}
</script>
{% endblock %}