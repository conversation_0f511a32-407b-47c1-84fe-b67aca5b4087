{% extends "base.html" %}

{% block title %}قائمة الموظفين المتوفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cross"></i>
                        قائمة الموظفين المتوفين
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> معلومات:</h6>
                                <p class="mb-0">إجمالي الموظفين المتوفين: <strong>{{ deceased|length }}</strong></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> ملاحظة:</h6>
                                <p class="mb-0">هؤلاء الموظفون لا يُحسبون في إجمالي عدد الموظفين النشطين</p>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ url_for('simple_status.dashboard') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button class="btn btn-success" onclick="window.print()">
                                <i class="fas fa-print"></i>
                                طباعة القائمة
                            </button>
                        </div>
                    </div>

                    {% if deceased %}
                    <!-- جدول المتوفين -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>#</th>
                                    <th>رقم التسجيل</th>
                                    <th>الاسم الكامل</th>
                                    <th>تاريخ الميلاد</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>تاريخ الوفاة</th>
                                    <th>سبب الوفاة</th>
                                    <th>رقم شهادة الوفاة</th>
                                    <th>تاريخ النقل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for person in deceased %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <span class="badge badge-secondary">
                                            {{ person.registration_number }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ person.first_name }} {{ person.last_name }}</strong>
                                    </td>
                                    <td>
                                        {% if person.birth_date %}
                                            {{ person.birth_date }}
                                            <br>
                                            <small class="text-muted">
                                                ({{ (person.death_date|strptime('%Y-%m-%d')).year - (person.birth_date|strptime('%Y-%m-%d')).year }} سنة)
                                            </small>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if person.hire_date %}
                                            {{ person.hire_date }}
                                            <br>
                                            <small class="text-muted">
                                                ({{ (person.death_date|strptime('%Y-%m-%d')).year - (person.hire_date|strptime('%Y-%m-%d')).year }} سنة خدمة)
                                            </small>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-dark">
                                            {{ person.death_date }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if person.death_cause == 'عادية' %}
                                            <span class="badge badge-secondary">
                                                <i class="fas fa-heart"></i> عادية
                                            </span>
                                        {% else %}
                                            <span class="badge badge-danger">
                                                <i class="fas fa-exclamation-triangle"></i> حادث عمل
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ person.death_certificate_number }}</code>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ person.transferred_at[:10] }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    onclick="viewEmployeeData({{ person.id }}, '{{ person.first_name }} {{ person.last_name }}')">
                                                <i class="fas fa-eye"></i>
                                                عرض البيانات
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <!-- رسالة عدم وجود بيانات -->
                    <div class="text-center py-5">
                        <i class="fas fa-cross fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد موظفون متوفون</h5>
                        <p class="text-muted">لم يتم تسجيل أي حالة وفاة حتى الآن</p>
                        <a href="{{ url_for('employees') }}" class="btn btn-primary">
                            <i class="fas fa-users"></i>
                            عرض قائمة الموظفين
                        </a>
                    </div>
                    {% endif %}

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض بيانات الموظف -->
<div class="modal fade" id="employeeDataModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user"></i>
                    بيانات الموظف المتوفى
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="employeeDataContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>جاري تحميل البيانات...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function viewEmployeeData(deceasedId, employeeName) {
    // فتح النافذة المنبثقة
    $('#employeeDataModal').modal('show');
    
    // تحديث عنوان النافذة
    $('#employeeDataModal .modal-title').html(`
        <i class="fas fa-user"></i>
        بيانات الموظف المتوفى: ${employeeName}
    `);
    
    // جلب البيانات (يمكن تطويرها لاحقاً)
    $('#employeeDataContent').html(`
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> معلومات:</h6>
            <p class="mb-0">يمكن إضافة عرض تفصيلي لجميع بيانات الموظف المحفوظة في JSON لاحقاً</p>
        </div>
        <div class="text-center">
            <p>معرف السجل: <strong>${deceasedId}</strong></p>
            <p>اسم الموظف: <strong>${employeeName}</strong></p>
        </div>
    `);
}

// تحسين الطباعة
window.addEventListener('beforeprint', function() {
    // إخفاء الأزرار عند الطباعة
    document.querySelectorAll('.btn, .modal').forEach(el => {
        el.style.display = 'none';
    });
});

window.addEventListener('afterprint', function() {
    // إظهار الأزرار بعد الطباعة
    document.querySelectorAll('.btn').forEach(el => {
        el.style.display = '';
    });
});
</script>

<style>
@media print {
    .btn, .modal {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}
</style>
{% endblock %}