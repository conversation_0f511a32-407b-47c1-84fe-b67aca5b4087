{% extends "base.html" %}

{% block title %}إضافة موظف جديد - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إضافة موظف جديد{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <a href="{{ url_for('employees') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الموظفين
        </a>
    </div>
</div>

<!-- نموذج إضافة الموظف -->
<form method="POST" enctype="multipart/form-data" id="employeeForm">
    <div class="row">
        <!-- البيانات الأساسية -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i>البيانات الأساسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم التسجيل <span class="required">*</span></label>
                                <input type="text" name="registration_number" class="form-control" 
                                       placeholder="123456" required maxlength="6" pattern="[0-9]{6}">
                                <div class="form-text">6 أرقام فقط - غير مكرر</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الجنس <span class="required">*</span></label>
                                <select name="gender" class="form-select" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="أنثى">أنثى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اللقب <span class="required">*</span></label>
                                <input type="text" name="last_name" class="form-control" 
                                       placeholder="اللقب" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم <span class="required">*</span></label>
                                <input type="text" name="first_name" class="form-control" 
                                       placeholder="الاسم" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom (فرنسي)</label>
                                <input type="text" name="last_name_fr" class="form-control" 
                                       placeholder="Nom">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Prénom (فرنسي)</label>
                                <input type="text" name="first_name_fr" class="form-control" 
                                       placeholder="Prénom">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الميلاد</label>
                                <input type="date" name="birth_date" class="form-control" 
                                       min="1960-01-01" max="2006-12-31">
                                <div class="form-text">من 19 إلى 65 سنة</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">ولاية الميلاد</label>
                                <select name="birth_wilaya_id" class="form-select" 
                                        onchange="updateCommunes(this, document.getElementById('birth_commune_id'))">
                                    <option value="">اختر الولاية</option>
                                    {% for wilaya in wilayas %}
                                    <option value="{{ wilaya.id }}">{{ wilaya.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">بلدية الميلاد</label>
                                <select name="birth_commune_id" class="form-select" id="birth_commune_id">
                                    <option value="">اختر البلدية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحالة العائلية</label>
                                <select name="marital_status" class="form-select" id="marital_status" 
                                        onchange="toggleChildrenCount()">
                                    <option value="">اختر الحالة</option>
                                    <option value="أعزب">أعزب</option>
                                    <option value="متزوج">متزوج</option>
                                    <option value="مطلق">مطلق</option>
                                    <option value="أرمل">أرمل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4" id="children_group">
                            <div class="mb-3">
                                <label class="form-label">عدد الأبناء</label>
                                <input type="number" name="children_count" class="form-control" 
                                       min="0" max="20" value="0" id="children_count">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">عدد الأشخاص المتكفل بهم</label>
                                <input type="number" name="dependents_count" class="form-control" 
                                       min="0" max="20" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">زمرة الدم</label>
                                <select name="blood_type" class="form-select">
                                    <option value="">اختر زمرة الدم</option>
                                    <option value="A+">A+</option>
                                    <option value="A-">A-</option>
                                    <option value="B+">B+</option>
                                    <option value="B-">B-</option>
                                    <option value="AB+">AB+</option>
                                    <option value="AB-">AB-</option>
                                    <option value="O+">O+</option>
                                    <option value="O-">O-</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرياضة الممارسة</label>
                                <input type="text" name="sport_practiced" class="form-control" 
                                       placeholder="كرة القدم، السباحة...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- صورة الموظف -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-camera me-2"></i>صورة الموظف</h5>
                </div>
                <div class="card-body text-center">
                    <div class="photo-upload-area" onclick="document.getElementById('photo').click()">
                        <img id="photo_preview" src="" alt="معاينة الصورة" 
                             style="display: none; max-width: 100%; max-height: 200px; border-radius: 0.5rem;">
                        <div id="upload_placeholder">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p class="text-muted">انقر لرفع صورة الموظف</p>
                            <small class="text-muted">الحد الأقصى: 16 ميجابايت</small>
                        </div>
                    </div>
                    <input type="file" name="photo" id="photo" accept="image/*" 
                           style="display: none;" onchange="previewPhoto(this)">
                    <div class="form-text mt-2">سيتم تعديل حجم الصورة تلقائياً إلى 300×400 بكسل</div>
                    
                    <!-- حالة الموظف تحت الصورة -->
                    <div class="mt-4">
                        <label class="form-label">حالة الموظف</label>
                        <select name="status" class="form-select employee-status-select" id="employee_status">
                            <option value="">اختر حالة الموظف</option>
                            <!-- سيتم تحميل الحالات ديناميكياً -->
                        </select>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-cog me-1"></i>
                                <a href="{{ url_for('settings') }}" target="_blank" class="text-decoration-none">
                                    إدارة الحالات من الإعدادات
                                </a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- بيانات الاتصال -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-phone me-2"></i>بيانات الاتصال</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف 1</label>
                        <input type="tel" name="phone1" class="form-control" 
                               placeholder="021-XX-XX-XX">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف 2</label>
                        <input type="tel" name="phone2" class="form-control" 
                               placeholder="05XX-XX-XX-XX">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" class="form-control" 
                               placeholder="<EMAIL>">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">العنوان الرئيسي</label>
                        <textarea name="main_address" class="form-control" rows="2" 
                                  placeholder="العنوان الكامل"></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">العنوان الثانوي</label>
                        <textarea name="secondary_address" class="form-control" rows="2" 
                                  placeholder="العنوان الثانوي (اختياري)"></textarea>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">اسم الشخص المتصل به في حالة الضرورة</label>
                        <input type="text" name="emergency_contact_name" class="form-control" 
                               placeholder="الاسم الكامل">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">عنوان الشخص المتصل به</label>
                        <textarea name="emergency_contact_address" class="form-control" rows="2" 
                                  placeholder="العنوان الكامل"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- البيانات المهنية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-briefcase me-2"></i>البيانات المهنية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">السلك</label>
                        <select name="corps_id" class="form-select" onchange="updateRanks(this, document.getElementById('current_rank_id'))">
                            <option value="">اختر السلك</option>
                            {% for corps in corps %}
                            <option value="{{ corps.id }}">{{ corps.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الرتبة الحالية</label>
                        <select name="current_rank_id" class="form-select" id="current_rank_id">
                            <option value="">اختر الرتبة</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الرتبة الحالية</label>
                        <select name="current_rank_id" class="form-select" id="current_rank_id">
                            <option value="">اختر الرتبة</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الترقية في الرتبة الحالية</label>
                        <input type="date" name="rank_promotion_date" class="form-control">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الوظيفة الحالية</label>
                        <select name="current_position_id" class="form-select">
                            <option value="">اختر الوظيفة</option>
                            {% for position in positions %}
                            <option value="{{ position.id }}">{{ position.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ التعيين في الوظيفة الحالية</label>
                        <input type="date" name="position_assignment_date" class="form-control">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المديرية</label>
                        <select name="current_directorate_id" class="form-select">
                            <option value="">اختر المديرية</option>
                            {% for directorate in directorates %}
                            <option value="{{ directorate.id }}">{{ directorate.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المصلحة</label>
                        <select name="current_service_id" class="form-select">
                            <option value="">اختر المصلحة</option>
                            {% for service in services %}
                            <option value="{{ service.id }}">{{ service.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ التوظيف/الدخول في إدارة الجمارك</label>
                        <input type="date" name="hiring_date" class="form-control">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رتبة التوظيف</label>
                        <select name="hiring_rank_id" class="form-select">
                            <option value="">اختر رتبة التوظيف</option>
                            {% for rank in ranks %}
                            <option value="{{ rank.id }}">{{ rank.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الوثائق والبطاقات -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-id-card me-2"></i>الوثائق والبطاقات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم الضمان الاجتماعي</label>
                        <input type="text" name="social_security_number" class="form-control"
                               maxlength="15" pattern="[0-9]{15}" placeholder="***************">
                        <div class="form-text">15 رقم - سيتم التحقق من صحته</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم الحساب الجاري البريدي</label>
                        <input type="text" name="postal_account_number" class="form-control"
                               maxlength="10" pattern="[0-9]{10}" placeholder="**********">
                        <div class="form-text">10 أرقام - سيتم التحقق من صحته</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم البطاقة المهنية</label>
                        <input type="text" name="professional_card_number" class="form-control">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ صدور البطاقة المهنية</label>
                        <input type="date" name="professional_card_issue_date" class="form-control">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">رقم بطاقة التعريف الوطنية</label>
                        <input type="text" name="national_id_number" class="form-control">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ صدورها</label>
                        <input type="date" name="national_id_issue_date" class="form-control">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">مكان صدورها</label>
                        <select name="national_id_issue_place_id" class="form-select">
                            <option value="">اختر مكان الصدور</option>
                            {% for wilaya in wilayas %}
                            <option value="{{ wilaya.id }}">{{ wilaya.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">رقم رخصة السياقة</label>
                        <input type="text" name="driving_license_number" class="form-control">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">الصنف</label>
                        <select name="driving_license_category" class="form-select">
                            <option value="">اختر الصنف</option>
                            <option value="A">A - دراجة نارية</option>
                            <option value="B">B - سيارة خفيفة</option>
                            <option value="C">C - شاحنة</option>
                            <option value="D">D - حافلة</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الصدور</label>
                        <input type="date" name="driving_license_issue_date" class="form-control">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">مكان الصدور</label>
                        <select name="driving_license_issue_place_id" class="form-select">
                            <option value="">اختر مكان الصدور</option>
                            {% for wilaya in wilayas %}
                            <option value="{{ wilaya.id }}">{{ wilaya.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم بطاقة التعاضدية</label>
                        <input type="text" name="mutual_card_number" class="form-control">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ صدورها</label>
                        <input type="date" name="mutual_card_issue_date" class="form-control">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الوضعية اتجاه الخدمة الوطنية</label>
                        <select name="military_service_status_id" class="form-select" onchange="toggleMilitaryEndDate()">
                            <option value="">اختر الوضعية</option>
                            {% for status in military_statuses %}
                            <option value="{{ status.id }}">{{ status.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6" id="military_end_date_group" style="display: none;">
                    <div class="mb-3">
                        <label class="form-label">إلى غاية (للمؤجل)</label>
                        <input type="date" name="military_service_end_date" class="form-control">
                        <div class="form-text">يظهر فقط إذا كانت الحالة "مؤجل"</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="card">
        <div class="card-body text-center">
            <button type="submit" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-save me-2"></i>حفظ الموظف
            </button>
            <a href="{{ url_for('employees') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_css %}
<style>
    /* تخصيص قائمة حالات الموظفين */
    .employee-status-select {
        background-color: #e8f4fd !important;
        border: 2px solid #17a2b8 !important;
        color: #0c5460 !important;
        font-weight: 600 !important;
    }
    
    .employee-status-select:focus {
        background-color: #d1ecf1 !important;
        border-color: #0c5460 !important;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25) !important;
    }
    
    .employee-status-select option {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 500;
        padding: 0.5rem;
    }
    
    .employee-status-select option:hover {
        background-color: #e9ecef;
    }
    
    .employee-status-select option[data-color] {
        font-weight: 600;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
// إخفاء/إظهار عدد الأطفال حسب الحالة العائلية
function toggleChildrenCount() {
    const maritalStatus = document.getElementById('marital_status');
    const childrenGroup = document.getElementById('children_group');
    
    if (maritalStatus.value === 'أعزب') {
        childrenGroup.style.display = 'none';
        document.getElementById('children_count').value = 0;
    } else {
        childrenGroup.style.display = 'block';
    }
}

// معاينة الصورة قبل الرفع
function previewPhoto(input) {
    const preview = document.getElementById('photo_preview');
    const placeholder = document.getElementById('upload_placeholder');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            placeholder.style.display = 'none';
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// التحقق من صحة النموذج
document.getElementById('employeeForm').addEventListener('submit', function(e) {
    const registrationNumber = document.querySelector('input[name="registration_number"]').value;
    const firstName = document.querySelector('input[name="first_name"]').value;
    const lastName = document.querySelector('input[name="last_name"]').value;
    const gender = document.querySelector('select[name="gender"]').value;
    
    if (!registrationNumber || !firstName || !lastName || !gender) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة (رقم التسجيل، الاسم، اللقب، الجنس)');
        return false;
    }
    
    if (!/^\d{6}$/.test(registrationNumber)) {
        e.preventDefault();
        alert('رقم التسجيل يجب أن يكون 6 أرقام فقط');
        return false;
    }
    
    // التحقق من العمر
    const birthDate = document.querySelector('input[name="birth_date"]').value;
    if (birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        const age = today.getFullYear() - birth.getFullYear();
        
        if (age < 19 || age > 65) {
            e.preventDefault();
            alert('عمر الموظف يجب أن يكون بين 19 و 65 سنة');
            return false;
        }
    }
});

// تحديث البلديات عند تغيير الولاية
function updateCommunes(wilayaSelect, communeSelect) {
    const wilayaId = wilayaSelect.value;
    
    // مسح البلديات الحالية
    communeSelect.innerHTML = '<option value="">اختر البلدية</option>';
    
    if (wilayaId) {
        fetch(`/api/communes/${wilayaId}`)
            .then(response => response.json())
            .then(communes => {
                communes.forEach(commune => {
                    const option = document.createElement('option');
                    option.value = commune.id;
                    option.textContent = commune.name;
                    communeSelect.appendChild(option);
                });
            })
            .catch(error => console.error('خطأ في جلب البلديات:', error));
    }
}

// تحديث الرتب عند تغيير السلك
function updateRanks(corpsSelect, rankSelect) {
    const corpsId = corpsSelect.value;

    // مسح الرتب الحالية
    rankSelect.innerHTML = '<option value="">اختر الرتبة</option>';

    if (corpsId) {
        fetch(`/api/ranks/${corpsId}`)
            .then(response => response.json())
            .then(ranks => {
                ranks.forEach(rank => {
                    const option = document.createElement('option');
                    option.value = rank.id;
                    option.textContent = rank.name;
                    rankSelect.appendChild(option);
                });
            })
            .catch(error => console.error('خطأ في جلب الرتب:', error));
    }
}

// إظهار/إخفاء تاريخ انتهاء الخدمة العسكرية للمؤجل
function toggleMilitaryEndDate() {
    const militarySelect = document.querySelector('select[name="military_service_status_id"]');
    const endDateGroup = document.getElementById('military_end_date_group');
    const selectedOption = militarySelect.options[militarySelect.selectedIndex];

    if (selectedOption && selectedOption.text.includes('مؤجل')) {
        endDateGroup.style.display = 'block';
    } else {
        endDateGroup.style.display = 'none';
        document.querySelector('input[name="military_service_end_date"]').value = '';
    }
}

// تحميل حالات الموظفين
function loadEmployeeStatuses() {
    const statusSelect = document.getElementById('employee_status');
    
    fetch('/api/employee_statuses')
        .then(response => response.json())
        .then(statuses => {
            statusSelect.innerHTML = '<option value="">اختر حالة الموظف</option>';
            statuses.forEach(status => {
                if (status.is_active) {
                    const option = document.createElement('option');
                    option.value = status.name;
                    option.textContent = status.name;
                    option.setAttribute('data-color', status.color);
                    option.style.color = status.color;
                    option.style.fontWeight = '600';
                    
                    // تحديد "نشط" كحالة افتراضية
                    if (status.name === 'نشط') {
                        option.selected = true;
                    }
                    
                    statusSelect.appendChild(option);
                }
            });
        })
        .catch(error => {
            console.error('خطأ في جلب حالات الموظفين:', error);
            // في حالة الخطأ، استخدم الحالات الافتراضية
            const defaultStatuses = ['نشط', 'معلق', 'متقاعد', 'مستقيل', 'متوفي'];
            statusSelect.innerHTML = '<option value="">اختر حالة الموظف</option>';
            defaultStatuses.forEach(status => {
                const option = document.createElement('option');
                option.value = status;
                option.textContent = status;
                statusSelect.appendChild(option);
                
                if (status === 'نشط') {
                    option.selected = true;
                }
            });
        });
}

// إخفاء عدد الأطفال في البداية إذا كان أعزب
document.addEventListener('DOMContentLoaded', function() {
    toggleChildrenCount();
    loadEmployeeStatuses();
});
</script>
{% endblock %}
