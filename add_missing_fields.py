#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة الحقول المفقودة إلى جدول الموظفين
"""

import sqlite3

def add_missing_fields():
    """إضافة الحقول المفقودة"""
    print("🔧 إضافة الحقول المفقودة إلى جدول الموظفين...")
    print("=" * 70)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    # قائمة الحقول المفقودة مع أنواعها
    missing_fields = [
        ('secondary_address', 'TEXT', 'العنوان الثانوي'),
        ('emergency_contact_name', 'TEXT', 'اسم الشخص المتصل به في حالة الضرورة'),
        ('emergency_contact_address', 'TEXT', 'عنوان الشخص المتصل به في حالة الضرورة'),
        ('rank_promotion_date', 'DATE', 'تاريخ الترقية في الرتبة الحالية'),
        ('current_position_id', 'INTEGER', 'الوظيفة الحالية'),
        ('position_assignment_date', 'DATE', 'تاريخ التعين في الوظيفة الحالية'),
        ('directorate_id', 'INTEGER', 'المديرية'),
        ('assignment_location_id', 'INTEGER', 'مكان التعيين'),
        ('initial_rank_id', 'INTEGER', 'رتبة التوظيف'),
        ('professional_card_number', 'TEXT', 'رقم بطاقة المهنية'),
        ('professional_card_issue_date', 'DATE', 'تاريخ صدور بطاقة المهنية'),
        ('national_id_number', 'TEXT', 'رقم بطاقة التعريف الوطنية'),
        ('national_id_issue_date', 'DATE', 'تاريخ صدور بطاقة التعريف الوطنية'),
        ('national_id_issue_place_id', 'INTEGER', 'مكان صدور بطاقة التعريف الوطنية'),
        ('driving_license_number', 'TEXT', 'رقم رخصة السياقة'),
        ('driving_license_category', 'TEXT', 'صنف رخصة السياقة'),
        ('driving_license_issue_date', 'DATE', 'تاريخ صدور رخصة السياقة'),
        ('driving_license_issue_place_id', 'INTEGER', 'مكان صدور رخصة السياقة'),
        ('mutual_card_number', 'TEXT', 'رقم بطاقة التعاضدية'),
        ('mutual_card_issue_date', 'DATE', 'تاريخ صدور بطاقة التعاضدية')
    ]
    
    # التحقق من الحقول الموجودة حالياً
    cursor.execute("PRAGMA table_info(employees)")
    existing_columns = [col[1] for col in cursor.fetchall()]
    
    added_count = 0
    skipped_count = 0
    
    for field_name, field_type, description in missing_fields:
        if field_name not in existing_columns:
            try:
                sql = f"ALTER TABLE employees ADD COLUMN {field_name} {field_type}"
                cursor.execute(sql)
                print(f"✅ تم إضافة: {field_name} ({field_type}) - {description}")
                added_count += 1
            except Exception as e:
                print(f"❌ خطأ في إضافة {field_name}: {e}")
        else:
            print(f"⚠️  موجود مسبقاً: {field_name} - {description}")
            skipped_count += 1
    
    conn.commit()
    conn.close()
    
    print("-" * 70)
    print(f"📊 ملخص العملية:")
    print(f"   ✅ الحقول المضافة: {added_count}")
    print(f"   ⚠️  الحقول الموجودة مسبقاً: {skipped_count}")
    print(f"   📋 إجمالي الحقول المعالجة: {len(missing_fields)}")
    
    if added_count > 0:
        print(f"\n🎉 تم إضافة {added_count} حقل جديد بنجاح!")
    
    # التحقق من العدد النهائي للحقول
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(employees)")
    final_columns = cursor.fetchall()
    conn.close()
    
    print(f"\n📈 العدد النهائي للحقول: {len(final_columns)}")

def show_updated_fields():
    """عرض جميع الحقول بعد التحديث"""
    print("\n" + "=" * 70)
    print("📋 جميع حقول الموظف بعد التحديث:")
    print("=" * 70)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(employees)")
    columns = cursor.fetchall()
    conn.close()
    
    # تصنيف الحقول
    categories = {
        'البيانات الأساسية': [
            'id', 'registration_number', 'first_name', 'last_name', 
            'first_name_fr', 'last_name_fr'
        ],
        'البيانات الشخصية': [
            'birth_date', 'birth_wilaya_id', 'birth_commune_id', 'gender',
            'marital_status', 'children_count', 'dependents_count', 
            'blood_type', 'sport_practiced'
        ],
        'البيانات المهنية': [
            'hire_date', 'initial_rank_id', 'current_rank_id', 'rank_promotion_date',
            'corps_id', 'current_position_id', 'position_assignment_date',
            'directorate_id', 'current_service_id', 'assignment_location_id', 'status'
        ],
        'بيانات الاتصال': [
            'phone', 'phone1', 'phone2', 'email', 'address', 'secondary_address',
            'emergency_contact_name', 'emergency_contact_address'
        ],
        'الوثائق والأرقام الرسمية': [
            'social_security_number', 'postal_account', 'national_id_number',
            'national_id_issue_date', 'national_id_issue_place_id',
            'professional_card_number', 'professional_card_issue_date',
            'driving_license_number', 'driving_license_category',
            'driving_license_issue_date', 'driving_license_issue_place_id',
            'mutual_card_number', 'mutual_card_issue_date'
        ],
        'البيانات التقنية': [
            'photo', 'created_at', 'updated_at'
        ]
    }
    
    all_fields = [col[1] for col in columns]
    
    for category, expected_fields in categories.items():
        print(f"\n🏷️  {category}:")
        category_count = 0
        for field in expected_fields:
            if field in all_fields:
                print(f"   ✅ {field}")
                category_count += 1
            else:
                print(f"   ❌ {field} (مفقود)")
        print(f"   📊 {category_count}/{len(expected_fields)} حقل")
    
    print(f"\n📊 إجمالي الحقول: {len(columns)}")

if __name__ == "__main__":
    add_missing_fields()
    show_updated_fields()