#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تهيئة قاعدة البيانات الكاملة لنظام إدارة موظفي الجمارك الجزائرية
"""

import sqlite3
from datetime import datetime

def create_database():
    """إنشاء قاعدة البيانات وجميع الجداول المطلوبة"""
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    print("🔄 بدء تهيئة قاعدة البيانات...")
    
    # 1. جدول الولايات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS wilayas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 2. جدول البلديات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS communes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            wilaya_id INTEGER NOT NULL,
            code TEXT NOT NULL,
            name TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (wilaya_id) REFERENCES wilayas (id)
        )
    ''')
    
    # 3. جدول الرتب
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ranks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            level INTEGER,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 4. جدول الأسلاك
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS corps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 5. جدول المصالح
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS services (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 6. جدول الموظفين الرئيسي
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            birth_date DATE,
            birth_wilaya_id INTEGER,
            birth_commune_id INTEGER,
            gender TEXT CHECK (gender IN ('ذكر', 'أنثى')),
            social_security_number TEXT UNIQUE,
            hire_date DATE,
            current_rank_id INTEGER,
            corps_id INTEGER,
            current_service_id INTEGER,
            postal_account TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            photo TEXT,
            status TEXT DEFAULT 'نشط',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (birth_wilaya_id) REFERENCES wilayas (id),
            FOREIGN KEY (birth_commune_id) REFERENCES communes (id),
            FOREIGN KEY (current_rank_id) REFERENCES ranks (id),
            FOREIGN KEY (corps_id) REFERENCES corps (id),
            FOREIGN KEY (current_service_id) REFERENCES services (id)
        )
    ''')
    
    # 7. جدول العطل السنوية
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS annual_leaves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            leave_type TEXT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            days_count INTEGER NOT NULL,
            reason TEXT,
            status TEXT DEFAULT 'معلقة',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # 8. جدول العطل المرضية
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sick_leaves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            days_count INTEGER NOT NULL,
            medical_certificate TEXT,
            status TEXT DEFAULT 'معلقة',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # 9. جدول العطل الأخرى
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS other_leaves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            leave_type TEXT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            days_count INTEGER NOT NULL,
            reason TEXT,
            status TEXT DEFAULT 'معلقة',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # 10. جدول الشهادات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS certificates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            certificate_type TEXT NOT NULL,
            institution TEXT,
            graduation_date DATE,
            specialization TEXT,
            grade TEXT,
            file_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # 11. جدول التدريب
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS training (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            training_name TEXT NOT NULL,
            institution TEXT,
            start_date DATE,
            end_date DATE,
            duration_hours INTEGER,
            certificate_obtained BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # 12. جدول ترقيات الرتب
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS rank_promotions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            old_rank_id INTEGER,
            new_rank_id INTEGER NOT NULL,
            promotion_date DATE NOT NULL,
            decision_number TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id),
            FOREIGN KEY (old_rank_id) REFERENCES ranks (id),
            FOREIGN KEY (new_rank_id) REFERENCES ranks (id)
        )
    ''')
    
    # 13. جدول ترقيات الدرجات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS grade_promotions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            old_grade INTEGER,
            new_grade INTEGER NOT NULL,
            promotion_date DATE NOT NULL,
            decision_number TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # 14. جدول التحويلات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS transfers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            old_service_id INTEGER,
            new_service_id INTEGER NOT NULL,
            transfer_date DATE NOT NULL,
            decision_number TEXT,
            reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id),
            FOREIGN KEY (old_service_id) REFERENCES services (id),
            FOREIGN KEY (new_service_id) REFERENCES services (id)
        )
    ''')
    
    # 15. جدول العقوبات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sanctions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            sanction_type TEXT NOT NULL,
            decision_date DATE NOT NULL,
            decision_number TEXT,
            reason TEXT,
            duration_days INTEGER,
            status TEXT DEFAULT 'نافذة',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    print("✅ تم إنشاء جميع الجداول")
    
    # إدراج البيانات المرجعية
    insert_reference_data(cursor)
    
    # إدراج بيانات تجريبية للموظفين
    insert_sample_employees(cursor)
    
    conn.commit()
    conn.close()
    
    print("🎉 تم إنشاء قاعدة البيانات بنجاح!")

def insert_reference_data(cursor):
    """إدراج البيانات المرجعية"""
    
    print("🔄 إدراج البيانات المرجعية...")
    
    # الولايات الجزائرية
    wilayas = [
        ('01', 'أدرار'), ('02', 'الشلف'), ('03', 'الأغواط'), ('04', 'أم البواقي'),
        ('05', 'باتنة'), ('06', 'بجاية'), ('07', 'بسكرة'), ('08', 'بشار'),
        ('09', 'البليدة'), ('10', 'البويرة'), ('11', 'تمنراست'), ('12', 'تبسة'),
        ('13', 'تلمسان'), ('14', 'تيارت'), ('15', 'تيزي وزو'), ('16', 'الجزائر'),
        ('17', 'الجلفة'), ('18', 'جيجل'), ('19', 'سطيف'), ('20', 'سعيدة'),
        ('21', 'سكيكدة'), ('22', 'سيدي بلعباس'), ('23', 'عنابة'), ('24', 'قالمة'),
        ('25', 'قسنطينة'), ('26', 'المدية'), ('27', 'مستغانم'), ('28', 'المسيلة'),
        ('29', 'معسكر'), ('30', 'ورقلة'), ('31', 'وهران'), ('32', 'البيض'),
        ('33', 'إليزي'), ('34', 'برج بوعريريج'), ('35', 'بومرداس'), ('36', 'الطارف'),
        ('37', 'تندوف'), ('38', 'تيسمسيلت'), ('39', 'الوادي'), ('40', 'خنشلة'),
        ('41', 'سوق أهراس'), ('42', 'تيبازة'), ('43', 'ميلة'), ('44', 'عين الدفلى'),
        ('45', 'النعامة'), ('46', 'عين تموشنت'), ('47', 'غرداية'), ('48', 'غليزان')
    ]
    
    for code, name in wilayas:
        cursor.execute('INSERT OR IGNORE INTO wilayas (code, name) VALUES (?, ?)', (code, name))
    
    # بلديات عينة (الجزائر العاصمة)
    communes = [
        (16, '1601', 'الجزائر الوسطى'), (16, '1602', 'سيدي امحمد'), (16, '1603', 'المدنية'),
        (16, '1604', 'حسين داي'), (16, '1605', 'بئر مراد رايس'), (16, '1606', 'الحراش'),
        (16, '1607', 'بئر خادم'), (16, '1608', 'بوزريعة'), (16, '1609', 'الكاليتوس'),
        (16, '1610', 'باب الزوار'), (16, '1611', 'القبة'), (16, '1612', 'الدار البيضاء')
    ]
    
    for wilaya_id, code, name in communes:
        cursor.execute('INSERT OR IGNORE INTO communes (wilaya_id, code, name) VALUES (?, ?, ?)', (wilaya_id, code, name))
    
    # الرتب
    ranks = [
        ('عون إدارة', 1, 'المستوى الأول في السلم الإداري'),
        ('كاتب إدارة', 2, 'المستوى الثاني في السلم الإداري'),
        ('كاتب إدارة رئيسي', 3, 'المستوى الثالث في السلم الإداري'),
        ('مساعد إداري', 4, 'المستوى الرابع في السلم الإداري'),
        ('مساعد إداري رئيسي', 5, 'المستوى الخامس في السلم الإداري'),
        ('ملحق إدارة', 6, 'المستوى السادس في السلم الإداري'),
        ('ملحق إدارة رئيسي', 7, 'المستوى السابع في السلم الإداري'),
        ('مفتش', 8, 'رتبة التفتيش'),
        ('مفتش رئيسي', 9, 'رتبة التفتيش العليا'),
        ('مفتش مركزي', 10, 'أعلى رتبة في التفتيش')
    ]
    
    for name, level, desc in ranks:
        cursor.execute('INSERT OR IGNORE INTO ranks (name, level, description) VALUES (?, ?, ?)', (name, level, desc))
    
    # الأسلاك
    corps_list = [
        ('الإدارة العامة', 'السلك الإداري العام'),
        ('التفتيش الجمركي', 'سلك مفتشي الجمارك'),
        ('الأمن والحراسة', 'سلك أمن المنشآت الجمركية'),
        ('المحاسبة والمالية', 'السلك المالي والمحاسبي'),
        ('الموارد البشرية', 'سلك إدارة الموارد البشرية'),
        ('تكنولوجيا المعلومات', 'سلك المعلوماتية والتكنولوجيا'),
        ('الشؤون القانونية', 'السلك القانوني'),
        ('التدريب والتكوين', 'سلك التدريب المهني')
    ]
    
    for name, desc in corps_list:
        cursor.execute('INSERT OR IGNORE INTO corps (name, description) VALUES (?, ?)', (name, desc))
    
    # المصالح
    services_list = [
        ('الإدارة العامة', 'مصلحة الإدارة العامة'),
        ('المحاسبة', 'مصلحة المحاسبة والمالية'),
        ('الموارد البشرية', 'مصلحة الموارد البشرية'),
        ('التفتيش', 'مصلحة التفتيش الجمركي'),
        ('الأمن', 'مصلحة الأمن والحراسة'),
        ('تكنولوجيا المعلومات', 'مصلحة المعلوماتية'),
        ('الشؤون القانونية', 'المصلحة القانونية'),
        ('التدريب', 'مصلحة التدريب والتكوين'),
        ('العلاقات العامة', 'مصلحة العلاقات العامة والإعلام'),
        ('الصيانة', 'مصلحة الصيانة والخدمات التقنية')
    ]
    
    for name, desc in services_list:
        cursor.execute('INSERT OR IGNORE INTO services (name, description) VALUES (?, ?)', (name, desc))
    
    print("✅ تم إدراج البيانات المرجعية")

def insert_sample_employees(cursor):
    """إدراج بيانات تجريبية للموظفين"""
    
    print("🔄 إدراج بيانات تجريبية للموظفين...")
    
    sample_employees = [
        {
            'registration_number': '123456',
            'first_name': 'أحمد',
            'last_name': 'بن محمد',
            'birth_date': '1985-01-15',
            'birth_wilaya_id': 16,  # الجزائر
            'birth_commune_id': 1,
            'gender': 'ذكر',
            'social_security_number': '185011234567890',
            'hire_date': '2010-03-01',
            'current_rank_id': 6,  # ملحق إدارة
            'corps_id': 1,  # الإدارة العامة
            'current_service_id': 1,  # الإدارة العامة
            'phone': '0555123456',
            'email': '<EMAIL>'
        },
        {
            'registration_number': '123457',
            'first_name': 'فاطمة',
            'last_name': 'بن علي',
            'birth_date': '1990-05-20',
            'birth_wilaya_id': 16,
            'birth_commune_id': 2,
            'gender': 'أنثى',
            'social_security_number': '290051234567891',
            'hire_date': '2015-06-15',
            'current_rank_id': 5,  # مساعد إداري رئيسي
            'corps_id': 4,  # المحاسبة والمالية
            'current_service_id': 2,  # المحاسبة
            'phone': '0555123457',
            'email': '<EMAIL>'
        },
        {
            'registration_number': '123458',
            'first_name': 'محمد',
            'last_name': 'بن حسن',
            'birth_date': '1988-12-10',
            'birth_wilaya_id': 16,
            'birth_commune_id': 3,
            'gender': 'ذكر',
            'social_security_number': '188121234567892',
            'hire_date': '2012-09-01',
            'current_rank_id': 7,  # ملحق إدارة رئيسي
            'corps_id': 5,  # الموارد البشرية
            'current_service_id': 3,  # الموارد البشرية
            'phone': '0555123458',
            'email': '<EMAIL>'
        },
        {
            'registration_number': '123459',
            'first_name': 'خديجة',
            'last_name': 'بن يوسف',
            'birth_date': '1992-03-25',
            'birth_wilaya_id': 16,
            'birth_commune_id': 4,
            'gender': 'أنثى',
            'social_security_number': '292031234567893',
            'hire_date': '2018-01-10',
            'current_rank_id': 8,  # مفتش
            'corps_id': 2,  # التفتيش الجمركي
            'current_service_id': 4,  # التفتيش
            'phone': '0555123459',
            'email': '<EMAIL>'
        },
        {
            'registration_number': '123460',
            'first_name': 'عبد الرحمن',
            'last_name': 'بن أحمد',
            'birth_date': '1987-08-14',
            'birth_wilaya_id': 16,
            'birth_commune_id': 5,
            'gender': 'ذكر',
            'social_security_number': '187081234567894',
            'hire_date': '2013-04-20',
            'current_rank_id': 6,  # ملحق إدارة
            'corps_id': 3,  # الأمن والحراسة
            'current_service_id': 5,  # الأمن
            'phone': '0555123460',
            'email': '<EMAIL>'
        }
    ]
    
    for emp in sample_employees:
        cursor.execute('''
            INSERT OR IGNORE INTO employees (
                registration_number, first_name, last_name, birth_date,
                birth_wilaya_id, birth_commune_id, gender, social_security_number,
                hire_date, current_rank_id, corps_id, current_service_id,
                phone, email, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'نشط')
        ''', (
            emp['registration_number'], emp['first_name'], emp['last_name'],
            emp['birth_date'], emp['birth_wilaya_id'], emp['birth_commune_id'],
            emp['gender'], emp['social_security_number'], emp['hire_date'],
            emp['current_rank_id'], emp['corps_id'], emp['current_service_id'],
            emp['phone'], emp['email']
        ))
    
    print("✅ تم إدراج البيانات التجريبية للموظفين")

if __name__ == '__main__':
    create_database()