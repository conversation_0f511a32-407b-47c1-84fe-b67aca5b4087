<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة بيانات الموظف: {{ employee.first_name }} {{ employee.last_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            body {
                font-size: 12px;
            }
            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .print-header {
            text-align: center;
            border-bottom: 3px solid #0d6efd;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .employee-photo {
            width: 150px;
            height: 200px;
            object-fit: cover;
            border: 2px solid #dee2e6;
            border-radius: 8px;
        }
        
        .info-row {
            margin-bottom: 15px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
            min-width: 150px;
            display: inline-block;
        }
        
        .info-value {
            color: #212529;
        }
        
        .section-title {
            background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
            color: white;
            padding: 10px 15px;
            margin: 20px 0 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .print-date {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 0.8em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- تاريخ الطباعة -->
    <div class="print-date no-print">
        <span id="current-date"></span>
    </div>

    <!-- أزرار التحكم -->
    <div class="container-fluid no-print mb-3">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <button onclick="window.close()" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
            </div>
            <div>
                <a href="{{ url_for('employee_detail', employee_id=employee.id) }}" class="btn btn-info" target="_blank">
                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- رأس الصفحة -->
        <div class="print-header">
            <h2 class="text-primary mb-2">الجمهورية الجزائرية الديمقراطية الشعبية</h2>
            <h3 class="mb-2">وزارة المالية - المديرية العامة للجمارك</h3>
            <h4 class="text-muted">بطاقة معلومات الموظف</h4>
        </div>

        <div class="row">
            <!-- الصورة والمعلومات الأساسية -->
            <div class="col-md-4 text-center">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="employee-photo mb-3">
                {% else %}
                <div class="employee-photo mb-3 d-flex align-items-center justify-content-center bg-light">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
                
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title text-primary">{{ employee.first_name }} {{ employee.last_name }}</h5>
                        <p class="card-text">
                            <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                            {% if employee.status %}
                            <span class="status-badge bg-primary text-white">{{ employee.status }}</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <!-- المعلومات التفصيلية -->
            <div class="col-md-8">
                <!-- البيانات الشخصية -->
                <div class="section-title">
                    <i class="fas fa-user me-2"></i>البيانات الشخصية
                </div>
                
                <div class="info-row">
                    <span class="info-label">اللقب:</span>
                    <span class="info-value">{{ employee.last_name or 'غير محدد' }}</span>
                    {% if employee.last_name_fr %}
                    <span class="text-muted ms-2">({{ employee.last_name_fr }})</span>
                    {% endif %}
                </div>
                
                <div class="info-row">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value">{{ employee.first_name or 'غير محدد' }}</span>
                    {% if employee.first_name_fr %}
                    <span class="text-muted ms-2">({{ employee.first_name_fr }})</span>
                    {% endif %}
                </div>
                
                <div class="info-row">
                    <span class="info-label">تاريخ الميلاد:</span>
                    <span class="info-value">
                        {{ employee.birth_date or 'غير محدد' }}
                        {% if employee.birth_date %}
                        <span class="text-muted ms-2">({{ employee.birth_date|calculate_age }} سنة)</span>
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">مكان الميلاد:</span>
                    <span class="info-value">
                        {% if employee.birth_commune_name and employee.birth_wilaya_name %}
                        {{ employee.birth_commune_name }}، {{ employee.birth_wilaya_name }}
                        {% elif employee.birth_wilaya_name %}
                        {{ employee.birth_wilaya_name }}
                        {% else %}
                        غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الجنس:</span>
                    <span class="info-value">
                        {% if employee.gender == 'ذكر' %}
                        <i class="fas fa-mars text-primary me-1"></i>ذكر
                        {% elif employee.gender == 'أنثى' %}
                        <i class="fas fa-venus text-danger me-1"></i>أنثى
                        {% else %}
                        غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">رقم الضمان الاجتماعي:</span>
                    <span class="info-value">{{ employee.social_security_number or 'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الحالة العائلية:</span>
                    <span class="info-value">{{ employee.marital_status or 'غير محدد' }}</span>
                </div>
                
                {% if employee.children_count %}
                <div class="info-row">
                    <span class="info-label">عدد الأبناء:</span>
                    <span class="info-value">{{ employee.children_count }}</span>
                </div>
                {% endif %}
                
                {% if employee.blood_type %}
                <div class="info-row">
                    <span class="info-label">زمرة الدم:</span>
                    <span class="info-value">{{ employee.blood_type }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- البيانات المهنية -->
        <div class="section-title">
            <i class="fas fa-briefcase me-2"></i>البيانات المهنية
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="info-row">
                    <span class="info-label">تاريخ التوظيف:</span>
                    <span class="info-value">
                        {{ employee.hire_date or 'غير محدد' }}
                        {% if employee.hire_date %}
                        <span class="text-muted ms-2">({{ employee.hire_date|calculate_service_years }} سنة خدمة)</span>
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الرتبة الحالية:</span>
                    <span class="info-value">{{ employee.rank_name or 'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">السلك:</span>
                    <span class="info-value">{{ employee.corps_name or 'غير محدد' }}</span>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="info-row">
                    <span class="info-label">مصلحة التعيين:</span>
                    <span class="info-value">{{ employee.service_name or 'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الحساب الجاري البريدي:</span>
                    <span class="info-value">{{ employee.postal_account or 'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        {% if employee.status %}
                        <span class="status-badge bg-info text-white">{{ employee.status }}</span>
                        {% else %}
                        غير محدد
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- بيانات الاتصال -->
        <div class="section-title">
            <i class="fas fa-phone me-2"></i>بيانات الاتصال
        </div>
        
        <div class="row">
            <div class="col-md-6">
                {% if employee.phone1 %}
                <div class="info-row">
                    <span class="info-label">الهاتف 1:</span>
                    <span class="info-value">{{ employee.phone1 }}</span>
                </div>
                {% endif %}
                
                {% if employee.phone2 %}
                <div class="info-row">
                    <span class="info-label">الهاتف 2:</span>
                    <span class="info-value">{{ employee.phone2 }}</span>
                </div>
                {% endif %}
                
                {% if employee.email %}
                <div class="info-row">
                    <span class="info-label">البريد الإلكتروني:</span>
                    <span class="info-value">{{ employee.email }}</span>
                </div>
                {% endif %}
            </div>
            
            <div class="col-md-6">
                {% if employee.address %}
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ employee.address }}</span>
                </div>
                {% endif %}
                
                {% if employee.sport_practiced %}
                <div class="info-row">
                    <span class="info-label">الرياضة الممارسة:</span>
                    <span class="info-value">{{ employee.sport_practiced }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- تذييل الصفحة -->
        <div class="mt-5 pt-4 border-top text-center">
            <p class="text-muted mb-1">تم إنشاء هذا التقرير بواسطة نظام إدارة موظفي الجمارك الجزائرية</p>
            <p class="text-muted small">تاريخ الطباعة: <span id="print-date"></span></p>
        </div>
    </div>

    <script>
        // تحديد تاريخ الطباعة
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('ar-DZ') + ' ' + now.toLocaleTimeString('ar-DZ');
            document.getElementById('current-date').textContent = 'تاريخ الطباعة: ' + dateStr;
            document.getElementById('print-date').textContent = dateStr;
        });
        
        // طباعة تلقائية عند فتح النافذة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>