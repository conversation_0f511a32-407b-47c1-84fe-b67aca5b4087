#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية - النسخة النظيفة
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# إعداد قاعدة البيانات
DATABASE = 'customs_employees.db'

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """إنشاء قاعدة البيانات والجداول الأساسية"""
    conn = get_db_connection()
    
    # جدول الموظفين الأساسي
    conn.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            first_name_arabic TEXT,
            last_name_arabic TEXT,
            birth_date DATE,
            birth_place TEXT,
            gender TEXT,
            marital_status TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            hire_date DATE,
            position TEXT,
            department TEXT,
            salary REAL,
            status TEXT DEFAULT 'نشط',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()

# الصفحة الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

# قائمة الموظفين
@app.route('/employees')
def employees():
    """قائمة الموظفين"""
    conn = get_db_connection()
    try:
        employees = conn.execute('SELECT * FROM employees ORDER BY id DESC').fetchall()
        return render_template('employees/index.html', employees=employees)
    except:
        return render_template('employees/index.html', employees=[])
    finally:
        conn.close()

# إضافة موظف
@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    """إضافة موظف جديد"""
    if request.method == 'POST':
        conn = get_db_connection()
        try:
            # الحقول الأساسية المطلوبة
            registration_number = request.form['registration_number']
            first_name = request.form['first_name']
            last_name = request.form['last_name']
            
            # التحقق من عدم وجود رقم التسجيل
            existing = conn.execute('SELECT id FROM employees WHERE registration_number = ?', 
                                  (registration_number,)).fetchone()
            if existing:
                flash('رقم التسجيل موجود مسبقاً', 'error')
                return render_template('employees/add.html')
            
            # إدراج الموظف الجديد
            conn.execute('''
                INSERT INTO employees (registration_number, first_name, last_name, 
                                     first_name_arabic, last_name_arabic, birth_date, 
                                     birth_place, gender, marital_status, phone, 
                                     email, address, hire_date, position, department, salary)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                registration_number, first_name, last_name,
                request.form.get('first_name_arabic', ''),
                request.form.get('last_name_arabic', ''),
                request.form.get('birth_date', ''),
                request.form.get('birth_place', ''),
                request.form.get('gender', ''),
                request.form.get('marital_status', ''),
                request.form.get('phone', ''),
                request.form.get('email', ''),
                request.form.get('address', ''),
                request.form.get('hire_date', ''),
                request.form.get('position', ''),
                request.form.get('department', ''),
                float(request.form.get('salary', 0) or 0)
            ))
            
            conn.commit()
            flash('تم إضافة الموظف بنجاح', 'success')
            return redirect(url_for('employees'))
            
        except Exception as e:
            flash(f'خطأ في إضافة الموظف: {str(e)}', 'error')
            return render_template('employees/add.html')
        finally:
            conn.close()
    
    return render_template('employees/add.html')

# صفحة حالات الموظفين
@app.route('/employee_statuses')
def employee_statuses():
    """صفحة إدارة حالات الموظفين"""
    conn = get_db_connection()
    
    try:
        # حساب عدد الموظفين لكل حالة
        status_counts = {}
        
        # الحالات النشطة (تدخل في التعداد)
        status_counts['active_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'نشط'").fetchone()[0]
        status_counts['assignment_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'منتدب'").fetchone()[0]
        status_counts['study_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'في دراسة/تكوين'").fetchone()[0]
        
        # الحالات غير النشطة (لا تدخل في التعداد)
        status_counts['leave_of_absence_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'مستودع'").fetchone()[0]
        status_counts['suspension_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'موقوف'").fetchone()[0]
        status_counts['long_term_leave_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'في عطلة طويلة الأمد'").fetchone()[0]
        status_counts['retirement_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'متقاعد'").fetchone()[0]
        status_counts['resignation_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'مستقيل'").fetchone()[0]
        status_counts['external_transfer_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'محول خارجياً'").fetchone()[0]
        status_counts['death_count'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'متوفى'").fetchone()[0]
        
        return render_template('employee_statuses/index.html', **status_counts)
        
    except Exception as e:
        # في حالة عدم وجود جدول الموظفين، نعرض أصفار
        status_counts = {
            'active_count': 0,
            'assignment_count': 0,
            'study_count': 0,
            'leave_of_absence_count': 0,
            'suspension_count': 0,
            'long_term_leave_count': 0,
            'retirement_count': 0,
            'resignation_count': 0,
            'external_transfer_count': 0,
            'death_count': 0
        }
        return render_template('employee_statuses/index.html', **status_counts)
    finally:
        conn.close()

if __name__ == '__main__':
    # إنشاء قاعدة البيانات عند التشغيل
    init_database()
    
    print("🚀 تشغيل نظام إدارة موظفي الجمارك الجزائرية")
    print("=" * 60)
    print("🌐 الوصول للنظام: http://localhost:5000")
    print("👥 قائمة الموظفين: http://localhost:5000/employees")
    print("➕ إضافة موظف: http://localhost:5000/add_employee")
    print("📊 حالات الموظفين: http://localhost:5000/employee_statuses")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
