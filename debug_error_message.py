#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص رسالة الخطأ المحددة
"""

from app import app

def debug_error_message():
    """تشخيص رسالة الخطأ"""
    print("🔍 تشخيص رسالة الخطأ المحددة")
    print("=" * 50)
    
    with app.test_client() as client:
        form_data = {
            'registration_number': '777777',
            'first_name': 'تجربة',
            'last_name': 'تشخيص',
            'social_security_number': '',  # فارغ
            'postal_account': '',  # فارغ
        }
        
        response = client.post('/add_employee', data=form_data, follow_redirects=True)
        
        if response.status_code == 200:
            content = response.data.decode('utf-8')
            
            # البحث عن رسائل الخطأ المحددة
            import re
            
            # البحث عن رسائل alert-danger
            danger_pattern = r'<div[^>]*alert-danger[^>]*>(.*?)</div>'
            danger_matches = re.findall(danger_pattern, content, re.DOTALL | re.IGNORECASE)
            
            if danger_matches:
                print("🚨 رسائل الخطأ الموجودة:")
                for i, match in enumerate(danger_matches, 1):
                    # تنظيف النص من HTML tags
                    clean_text = re.sub(r'<[^>]+>', '', match).strip()
                    print(f"  {i}. {clean_text}")
            else:
                print("✅ لا توجد رسائل خطأ")
            
            # البحث عن رسائل flash
            flash_pattern = r'flash\([\'"]([^\'"]+)[\'"]'
            
            # فحص محتوى الصفحة للكلمات المفتاحية
            keywords = [
                'رقم التسجيل',
                'رقم الضمان',
                'الحساب البريدي',
                'العمر',
                'خطأ',
                'غير صحيح',
                'موجود بالفعل'
            ]
            
            print(f"\n🔍 البحث عن الكلمات المفتاحية:")
            for keyword in keywords:
                if keyword in content:
                    # البحث عن السياق
                    lines = content.split('\n')
                    for line_num, line in enumerate(lines):
                        if keyword in line:
                            clean_line = re.sub(r'<[^>]+>', '', line).strip()
                            if clean_line:
                                print(f"  ✅ '{keyword}' في السطر {line_num}: {clean_line[:100]}...")
                                break
                else:
                    print(f"  ❌ '{keyword}' غير موجود")
            
            # حفظ المحتوى للفحص اليدوي
            with open('debug_response.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\n💾 تم حفظ محتوى الاستجابة في: debug_response.html")

if __name__ == "__main__":
    debug_error_message()