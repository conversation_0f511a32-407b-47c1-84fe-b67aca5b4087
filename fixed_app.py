#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os
import sys

app = Flask(__name__)
app.config['SECRET_KEY'] = 'customs-algeria-2025'

def init_database():
    """تهيئة قاعدة البيانات"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        # التحقق من وجود جدول الموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                registration_number TEXT UNIQUE,
                first_name TEXT,
                last_name TEXT,
                status TEXT DEFAULT 'نشط',
                current_rank_id INTEGER,
                current_service_id INTEGER
            )
        ''')
        
        # إدراج موظف تجريبي
        cursor.execute('''
            INSERT OR IGNORE INTO employees (registration_number, first_name, last_name, status)
            VALUES ('123456', 'موظف', 'تجريبي', 'نشط')
        ''')
        
        # جدول أسباب الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reason TEXT NOT NULL UNIQUE,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج أسباب افتراضية
        reasons = [
            'رعاية الأطفال',
            'الدراسة', 
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        for reason in reasons:
            cursor.execute('''
                INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
        
        conn.commit()
        conn.close()
        print("✅ تم تهيئة قاعدة البيانات")
        return True
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">
        <title>نظام إدارة موظفي الجمارك</title>
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                text-align: center; 
                padding: 50px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                margin: 0;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
                max-width: 600px;
                margin: 0 auto;
            }
            .btn { 
                padding: 15px 30px; 
                margin: 10px; 
                background: #28a745; 
                color: white; 
                text-decoration: none; 
                border-radius: 8px; 
                display: inline-block;
                font-size: 16px;
                transition: all 0.3s;
            }
            .btn:hover {
                background: #218838;
                transform: translateY(-2px);
            }
            h1 { font-size: 2.5em; margin-bottom: 20px; }
            p { font-size: 1.2em; margin-bottom: 30px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏛️ نظام إدارة موظفي الجمارك الجزائرية</h1>
            <p>مرحباً بك في النظام المحدث</p>
            <a href="/special_status/" class="btn">📋 الحالات الخاصة</a>
        </div>
    </body>
    </html>
    '''

@app.route('/special_status/')
def special_status_index():
    """الصفحة الرئيسية للحالات الخاصة"""
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">
        <title>الحالات الخاصة</title>
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                padding: 20px; 
                background: #f8f9fa;
                margin: 0;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 30px;
                text-align: center;
            }
            .card { 
                border: 1px solid #ddd; 
                padding: 25px; 
                margin: 15px 0; 
                border-radius: 10px; 
                background: white;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .btn { 
                padding: 12px 25px; 
                margin: 8px; 
                color: white; 
                text-decoration: none; 
                border-radius: 6px; 
                display: inline-block;
                transition: all 0.3s;
            }
            .btn-primary { background: #007bff; }
            .btn-secondary { background: #6c757d; }
            .btn-success { background: #28a745; }
            .btn-warning { background: #ffc107; color: #212529; }
            .btn:hover { transform: translateY(-2px); opacity: 0.9; }
            h1 { margin: 0; font-size: 2.2em; }
            h3 { color: #495057; margin-bottom: 15px; }
            .stats { 
                display: grid; 
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
                gap: 15px; 
                margin-bottom: 20px; 
            }
            .stat-card {
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
            }
            .stat-number { font-size: 2em; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📋 نظام إدارة الحالات الخاصة للموظفين</h1>
            <p>إدارة شاملة لجميع حالات الموظفين الخاصة</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div>حالات الاستيداع النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div>الاستقالات المعلقة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">7</div>
                <div>أسباب الاستيداع المتاحة</div>
            </div>
        </div>
        
        <div class="card">
            <h3>🔧 ملف الاستيداع</h3>
            <p>إدارة حالات الاستيداع مع تتبع الحد الأقصى 5 سنوات (60 شهر) في الحياة الوظيفية</p>
            <a href="/special_status/leave_reasons_settings" class="btn btn-secondary">⚙️ إعدادات الأسباب</a>
            <a href="/special_status/leave_of_absence/add" class="btn btn-success">➕ إضافة استيداع</a>
        </div>
        
        <div class="card">
            <h3>📋 الحالات الأخرى</h3>
            <p>الاستقالة، التوقيف، العزل، الوفيات، التقاعد، التحويل الخارجي</p>
            <div style="background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
                <strong>قيد التطوير:</strong> سيتم إضافة باقي الحالات الخاصة في التحديثات القادمة
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn btn-primary">🏠 العودة للرئيسية</a>
        </div>
    </body>
    </html>
    '''

@app.route('/special_status/leave_reasons_settings')
def leave_reasons_settings():
    """إعدادات أسباب الاستيداع"""
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    
    try:
        reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        reasons = []
    finally:
        conn.close()
    
    reasons_html = ""
    for i, reason in enumerate(reasons, 1):
        reasons_html += f'''
        <tr>
            <td>{i}</td>
            <td>{reason['reason']}</td>
            <td><span style="color: #28a745; font-weight: bold;">✓ نشط</span></td>
            <td>
                <button onclick="deactivateReason({reason['id']})" 
                        style="background: #dc3545; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                    🗑️ إلغاء تفعيل
                </button>
            </td>
        </tr>
        '''
    
    return f'''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">
        <title>إعدادات أسباب الاستيداع</title>
        <style>
            body {{ 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                padding: 20px; 
                background: #f8f9fa;
                margin: 0;
            }}
            .header {{
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                color: white;
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 25px;
                text-align: center;
            }}
            .card {{
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }}
            table {{ 
                width: 100%; 
                border-collapse: collapse; 
                margin: 20px 0; 
            }}
            th, td {{ 
                border: 1px solid #dee2e6; 
                padding: 12px; 
                text-align: right; 
            }}
            th {{ 
                background: #f8f9fa; 
                font-weight: bold;
                color: #495057;
            }}
            .btn {{ 
                padding: 12px 25px; 
                margin: 8px; 
                color: white; 
                text-decoration: none; 
                border-radius: 6px; 
                display: inline-block; 
                border: none;
                cursor: pointer;
                transition: all 0.3s;
            }}
            .btn-primary {{ background: #007bff; }}
            .btn-success {{ background: #28a745; }}
            .btn:hover {{ transform: translateY(-2px); opacity: 0.9; }}
            .form-group {{ margin: 15px 0; }}
            input[type="text"] {{ 
                padding: 12px; 
                width: 400px; 
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 16px;
            }}
            .alert {{
                background: #d1ecf1;
                border: 1px solid #bee5eb;
                color: #0c5460;
                padding: 15px;
                border-radius: 6px;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>⚙️ إعدادات أسباب الاستيداع</h1>
            <p>إدارة قائمة أسباب الاستيداع المتاحة للموظفين</p>
        </div>
        
        <div class="alert">
            <strong>📌 ملاحظة مهمة:</strong> الحد الأقصى للاستيداع هو 5 سنوات (60 شهر) في الحياة الوظيفية للموظف.
        </div>
        
        <div class="card">
            <h3>➕ إضافة سبب جديد</h3>
            <form method="POST" action="/special_status/add_leave_reason">
                <div class="form-group">
                    <input type="text" name="reason" placeholder="اكتب سبب الاستيداع الجديد..." required>
                    <button type="submit" class="btn btn-success">💾 إضافة</button>
                </div>
            </form>
        </div>
        
        <div class="card">
            <h3>📋 الأسباب الحالية</h3>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>سبب الاستيداع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {reasons_html}
                </tbody>
            </table>
        </div>
        
        <div style="text-align: center;">
            <a href="/special_status/" class="btn btn-primary">🔙 العودة للحالات الخاصة</a>
        </div>
        
        <script>
        function deactivateReason(id) {{
            if (confirm('هل أنت متأكد من إلغاء تفعيل هذا السبب؟\\nلن يظهر في قوائم الاختيار الجديدة.')) {{
                fetch('/special_status/api/leave_reason/' + id + '/deactivate', {{
                    method: 'POST'
                }})
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        alert('✅ تم إلغاء تفعيل السبب بنجاح');
                        location.reload();
                    }} else {{
                        alert('❌ خطأ في العملية');
                    }}
                }})
                .catch(error => {{
                    alert('❌ خطأ في الاتصال: ' + error);
                }});
            }}
        }}
        </script>
    </body>
    </html>
    '''

@app.route('/special_status/add_leave_reason', methods=['POST'])
def add_leave_reason():
    """إضافة سبب استيداع جديد"""
    reason = request.form.get('reason')
    if reason:
        conn = sqlite3.connect('customs_employees.db')
        try:
            conn.execute('''
                INSERT INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
            conn.commit()
        except:
            pass
        finally:
            conn.close()
    
    return redirect('/special_status/leave_reasons_settings')

@app.route('/special_status/leave_of_absence/add')
def add_leave_of_absence():
    """إضافة استيداع جديد"""
    # الحصول على قائمة الموظفين
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    
    try:
        employees = conn.execute('''
            SELECT id, registration_number, first_name, last_name
            FROM employees 
            ORDER BY registration_number
        ''').fetchall()
    except:
        employees = []
    
    try:
        leave_reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        leave_reasons = []
    finally:
        conn.close()
    
    employees_options = ""
    for emp in employees:
        employees_options += f'<option value="{emp["id"]}">{emp["registration_number"]} - {emp["first_name"]} {emp["last_name"]}</option>'
    
    reasons_options = ""
    for reason in leave_reasons:
        reasons_options += f'<option value="{reason["reason"]}">{reason["reason"]}</option>'
    
    return f'''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">
        <title>إضافة استيداع جديد</title>
        <style>
            body {{ 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                padding: 20px; 
                background: #f8f9fa;
                margin: 0;
            }}
            .header {{
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 25px;
                text-align: center;
            }}
            .card {{
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                max-width: 800px;
                margin: 0 auto;
            }}
            .form-group {{ 
                margin: 20px 0; 
            }}
            label {{ 
                display: block; 
                margin-bottom: 8px; 
                font-weight: bold; 
                color: #495057;
            }}
            input, select, textarea {{ 
                padding: 12px; 
                width: 100%; 
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 16px;
                box-sizing: border-box;
            }}
            .btn {{ 
                padding: 12px 25px; 
                margin: 8px; 
                color: white; 
                text-decoration: none; 
                border-radius: 6px; 
                display: inline-block; 
                border: none;
                cursor: pointer;
                transition: all 0.3s;
            }}
            .btn-success {{ background: #28a745; }}
            .btn-primary {{ background: #007bff; }}
            .btn:hover {{ transform: translateY(-2px); opacity: 0.9; }}
            .alert {{ 
                background: #d1ecf1; 
                border: 1px solid #bee5eb;
                color: #0c5460;
                padding: 20px; 
                border-radius: 6px; 
                margin: 20px 0; 
            }}
            .row {{ display: flex; gap: 20px; }}
            .col {{ flex: 1; }}
            small {{ color: #6c757d; font-size: 14px; }}
            .required {{ color: #dc3545; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>➕ إضافة استيداع جديد</h1>
            <p>إضافة حالة استيداع جديدة للموظف مع تتبع الحد الأقصى</p>
        </div>
        
        <div class="card">
            <form method="POST">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>الموظف <span class="required">*</span></label>
                            <select name="employee_id" required onchange="loadEmployeeInfo()">
                                <option value="">اختر الموظف</option>
                                {employees_options}
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>المدة (بالأشهر) <span class="required">*</span></label>
                            <input type="number" name="duration_months" min="1" max="60" required onchange="calculateEndDate()">
                            <small>الحد الأقصى: 60 شهر (5 سنوات) في الحياة الوظيفية</small>
                        </div>
                    </div>
                </div>
                
                <div id="employeeInfo" style="display: none;" class="alert">
                    <h4>📊 معلومات الموظف ورصيد الاستيداع:</h4>
                    <div id="employeeDetails"></div>
                </div>
                
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>سبب الاستيداع <span class="required">*</span></label>
                            <select name="reason" required>
                                <option value="">اختر السبب</option>
                                {reasons_options}
                            </select>
                            <small><a href="/special_status/leave_reasons_settings" target="_blank">⚙️ إدارة أسباب الاستيداع</a></small>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>تاريخ بداية الاستيداع <span class="required">*</span></label>
                            <input type="date" name="start_date" required onchange="calculateEndDate()">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>تاريخ نهاية الاستيداع</label>
                            <input type="date" name="end_date" readonly style="background: #f8f9fa;">
                            <small>يتم حساب هذا التاريخ تلقائياً بإضافة المدة إلى تاريخ البداية</small>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>رقم الفترة</label>
                            <input type="text" readonly placeholder="سيتم حسابه تلقائياً" style="background: #f8f9fa;">
                            <small>يتم تحديد رقم الفترة تلقائياً (الأولى، الثانية، إلخ)</small>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>رقم المقرر/الوثيقة</label>
                            <input type="text" name="decision_number" placeholder="رقم المقرر أو الوثيقة">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>تاريخ المقرر/الوثيقة</label>
                            <input type="date" name="decision_date">
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-success">💾 حفظ الاستيداع</button>
                    <a href="/special_status/" class="btn btn-primary">🔙 العودة</a>
                </div>
            </form>
        </div>
        
        <script>
        function loadEmployeeInfo() {{
            const employeeId = document.querySelector('[name="employee_id"]').value;
            if (!employeeId) {{
                document.getElementById('employeeInfo').style.display = 'none';
                return;
            }}
            
            // عرض معلومات افتراضية
            const details = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div>
                        <strong>🆔 رقم التسجيل:</strong> 123456<br>
                        <strong>👤 الاسم الكامل:</strong> موظف تجريبي<br>
                        <strong>🏅 الرتبة:</strong> موظف<br>
                        <strong>🏢 المصلحة:</strong> الإدارة العامة
                    </div>
                    <div>
                        <strong>📊 إجمالي الاستيداع المستخدم:</strong> 0 شهر (0 سنة)<br>
                        <strong>💰 الرصيد المتبقي:</strong> 60 شهر (5 سنوات)<br>
                        <strong>⚖️ الحد الأقصى المسموح:</strong> 5 سنوات<br>
                        <strong>✅ الحالة:</strong> <span style="color: #28a745;">متاح للاستيداع</span>
                    </div>
                </div>
            `;
            
            document.getElementById('employeeDetails').innerHTML = details;
            document.getElementById('employeeInfo').style.display = 'block';
        }}
        
        function calculateEndDate() {{
            const startDate = document.querySelector('[name="start_date"]').value;
            const durationMonths = parseInt(document.querySelector('[name="duration_months"]').value);
            
            if (startDate && durationMonths) {{
                const start = new Date(startDate);
                const end = new Date(start);
                end.setMonth(end.getMonth() + durationMonths);
                
                document.querySelector('[name="end_date"]').value = end.toISOString().split('T')[0];
            }}
        }}
        
        // تعيين التاريخ الحالي كافتراضي
        document.addEventListener('DOMContentLoaded', function() {{
            const today = new Date().toISOString().split('T')[0];
            document.querySelector('[name="start_date"]').value = today;
        }});
        </script>
    </body>
    </html>
    '''

@app.route('/special_status/api/leave_reason/<int:reason_id>/deactivate', methods=['POST'])
def api_deactivate_leave_reason(reason_id):
    """API لإلغاء تفعيل سبب الاستيداع"""
    conn = sqlite3.connect('customs_employees.db')
    try:
        conn.execute('''
            UPDATE leave_of_absence_reasons 
            SET is_active = 0
            WHERE id = ?
        ''', (reason_id,))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 تشغيل النظام المصحح...")
    print("=" * 50)
    
    # تهيئة قاعدة البيانات
    if init_database():
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
    else:
        print("❌ فشل في تهيئة قاعدة البيانات")
        sys.exit(1)
    
    print("🌐 الخادم سيعمل على: http://localhost:5000")
    print("📋 الحالات الخاصة: http://localhost:5000/special_status/")
    print("⚙️ إعدادات الاستيداع: http://localhost:5000/special_status/leave_reasons_settings")
    print("➕ إضافة استيداع: http://localhost:5000/special_status/leave_of_absence/add")
    print("=" * 50)
    
    try:
        app.run(debug=False, host='127.0.0.1', port=5000, threaded=True)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print("🔧 تأكد من أن المنفذ 5000 غير مستخدم")
