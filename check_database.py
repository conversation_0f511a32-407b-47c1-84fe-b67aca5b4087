#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية قاعدة البيانات وإضافة الأعمدة المفقودة
"""

import sqlite3

def check_and_update_database():
    """فحص وتحديث بنية قاعدة البيانات"""
    print("🔍 فحص بنية قاعدة البيانات...")
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    # الحصول على معلومات الأعمدة الحالية
    cursor.execute("PRAGMA table_info(employees)")
    columns = cursor.fetchall()
    
    print("📋 الأعمدة الموجودة حالياً:")
    existing_columns = []
    for col in columns:
        print(f"   - {col[1]} ({col[2]})")
        existing_columns.append(col[1])
    
    # الأعمدة المطلوبة
    required_columns = [
        ('first_name_fr', 'TEXT'),
        ('last_name_fr', 'TEXT'),
        ('marital_status', 'TEXT'),
        ('children_count', 'INTEGER DEFAULT 0'),
        ('dependents_count', 'INTEGER DEFAULT 0'),
        ('blood_type', 'TEXT'),
        ('sport_practiced', 'TEXT'),
        ('phone1', 'TEXT'),
        ('phone2', 'TEXT')
    ]
    
    print("\n🔧 إضافة الأعمدة المفقودة...")
    
    for col_name, col_type in required_columns:
        if col_name not in existing_columns:
            try:
                cursor.execute(f"ALTER TABLE employees ADD COLUMN {col_name} {col_type}")
                print(f"   ✅ تم إضافة العمود: {col_name}")
            except Exception as e:
                print(f"   ❌ خطأ في إضافة {col_name}: {e}")
        else:
            print(f"   ✅ العمود موجود: {col_name}")
    
    # إذا كان هناك عمود phone قديم، ننسخ بياناته إلى phone1
    if 'phone' in existing_columns and 'phone1' not in existing_columns:
        try:
            cursor.execute("UPDATE employees SET phone1 = phone WHERE phone IS NOT NULL")
            print("   ✅ تم نسخ بيانات phone إلى phone1")
        except Exception as e:
            print(f"   ⚠️ خطأ في نسخ بيانات الهاتف: {e}")
    
    conn.commit()
    conn.close()
    
    print("\n✅ تم تحديث قاعدة البيانات بنجاح!")

if __name__ == "__main__":
    check_and_update_database()