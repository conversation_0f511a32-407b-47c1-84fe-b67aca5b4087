{% extends "base.html" %}

{% block title %}إضافة عطلة طويلة الأمد - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-plus"></i>
                        إضافة عطلة طويلة الأمد
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user"></i> معلومات الموظف</h5>
                        <strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}<br>
                        <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                        <strong>الحالة الحالية:</strong> <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="leave_type">نوع العطلة <span class="text-danger">*</span></label>
                                    <select class="form-control" id="leave_type" name="leave_type" required>
                                        <option value="">اختر نوع العطلة</option>
                                        <option value="عطلة مرضية طويلة">عطلة مرضية طويلة</option>
                                        <option value="عطلة أمومة">عطلة أمومة</option>
                                        <option value="عطلة دراسية">عطلة دراسية</option>
                                        <option value="عطلة بدون راتب">عطلة بدون راتب</option>
                                        <option value="عطلة استثنائية">عطلة استثنائية</option>
                                        <option value="عطلة مرافقة زوج">عطلة مرافقة زوج</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار نوع العطلة
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">تاريخ بداية العطلة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ بداية العطلة
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expected_end_date">تاريخ الانتهاء المتوقع <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="expected_end_date" name="expected_end_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ الانتهاء المتوقع
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">تاريخ القرار</label>
                                    <input type="date" class="form-control" id="decision_date" name="decision_date">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">رقم القرار</label>
                                    <input type="text" class="form-control" id="decision_number" name="decision_number" placeholder="مثال: 2024/001">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="reason">سبب العطلة <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" required placeholder="اذكر سبب العطلة بالتفصيل"></textarea>
                            <div class="invalid-feedback">
                                يرجى إدخال سبب العطلة
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="أي ملاحظات إضافية (اختياري)"></textarea>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm" required>
                                <label class="custom-control-label" for="confirm">
                                    أؤكد صحة البيانات المدخلة وأن العطلة ستؤثر على حالة الموظف
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد صحة البيانات
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ العطلة
                            </button>
                            <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// التحقق من التواريخ
document.getElementById('start_date').addEventListener('change', function() {
    var startDate = new Date(this.value);
    var endDateInput = document.getElementById('expected_end_date');
    
    // تعيين الحد الأدنى لتاريخ الانتهاء
    var minEndDate = new Date(startDate);
    minEndDate.setDate(minEndDate.getDate() + 1);
    endDateInput.min = minEndDate.toISOString().split('T')[0];
    
    // إذا كان تاريخ الانتهاء أقل من تاريخ البداية، امسحه
    if (endDateInput.value && new Date(endDateInput.value) <= startDate) {
        endDateInput.value = '';
    }
});

// حساب مدة العطلة
function calculateDuration() {
    var startDate = document.getElementById('start_date').value;
    var endDate = document.getElementById('expected_end_date').value;
    
    if (startDate && endDate) {
        var start = new Date(startDate);
        var end = new Date(endDate);
        var diffTime = Math.abs(end - start);
        var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        // عرض المدة
        var durationInfo = document.getElementById('duration-info');
        if (!durationInfo) {
            durationInfo = document.createElement('small');
            durationInfo.id = 'duration-info';
            durationInfo.className = 'text-info';
            document.getElementById('expected_end_date').parentNode.appendChild(durationInfo);
        }
        durationInfo.textContent = `مدة العطلة: ${diffDays} يوم`;
    }
}

document.getElementById('start_date').addEventListener('change', calculateDuration);
document.getElementById('expected_end_date').addEventListener('change', calculateDuration);
</script>
{% endblock %}