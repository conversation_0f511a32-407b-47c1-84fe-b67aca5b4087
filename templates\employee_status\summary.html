{% extends "base.html" %}

{% block title %}ملخص حالات الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ stats.total_employees }}</h3>
                    <p>إجمالي الموظفين</p>
                </div>
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ stats.active_employees }}</h3>
                    <p>الموظفين النشطين</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ stats.external_transfers }}</h3>
                    <p>محول خارجياً</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-6">
            <div class="small-box bg-secondary">
                <div class="inner">
                    <h3>{{ stats.retired }}</h3>
                    <p>متقاعد</p>
                </div>
                <div class="icon">
                    <i class="fas fa-user-clock"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تفصيل الحالات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie"></i>
                        تفصيل حالات الموظفين
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for status, employees in status_breakdown.items() %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <span class="badge {{ status|status_badge_class }}">{{ status }}</span>
                                        ({{ employees|length }})
                                    </h5>
                                </div>
                                <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                    {% if employees %}
                                        <ul class="list-unstyled">
                                            {% for emp in employees[:10] %}
                                            <li class="mb-1">
                                                <small>
                                                    {{ emp.registration_number }} - 
                                                    {{ emp.first_name }} {{ emp.last_name }}
                                                    {% if emp.rank_name %}
                                                        <br><em>{{ emp.rank_name }}</em>
                                                    {% endif %}
                                                </small>
                                            </li>
                                            {% endfor %}
                                            {% if employees|length > 10 %}
                                            <li><small><em>... و {{ employees|length - 10 }} موظف آخر</em></small></li>
                                            {% endif %}
                                        </ul>
                                    {% else %}
                                        <p class="text-muted">لا يوجد موظفين بهذه الحالة</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أحدث التحويلات الخارجية -->
    {% if recent_transfers %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        أحدث التحويلات الخارجية
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('external_transfers_list') }}" class="btn btn-sm btn-primary">
                            عرض الكل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>تاريخ التحويل</th>
                                    <th>الجهة المحول إليها</th>
                                    <th>السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in recent_transfers %}
                                <tr>
                                    <td>
                                        {{ transfer.registration_number }} - 
                                        {{ transfer.first_name }} {{ transfer.last_name }}
                                    </td>
                                    <td>{{ transfer.transfer_date|format_arabic_date }}</td>
                                    <td>{{ transfer.destination_organization }}</td>
                                    <td>{{ transfer.reason or 'غير محدد' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- إجراءات سريعة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إجراءات سريعة</h3>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('employee_status_dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                        <a href="{{ url_for('external_transfers_list') }}" class="btn btn-warning">
                            <i class="fas fa-exchange-alt"></i> التحويلات الخارجية
                        </a>
                        <a href="{{ url_for('employees') }}" class="btn btn-info">
                            <i class="fas fa-users"></i> قائمة الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}