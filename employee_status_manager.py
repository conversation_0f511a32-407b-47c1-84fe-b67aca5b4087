#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية
Employee Status Management Module - Algerian Customs Employee Management System
"""

import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import json

class EmployeeStatusManager:
    """مدير حالات الموظفين"""
    
    def __init__(self, db_path: str = 'customs_employees.db'):
        self.db_path = db_path
        self.status_types = {
            'long_term_leave': 'عطلة طويلة الأمد',
            'resignation': 'استقالة', 
            'leave_of_absence': 'استيداع',
            'death': 'وفاة',
            'suspension': 'توقيف',
            'national_service': 'خدمة وطنية',
            'retirement': 'تقاعد',
            'external_transfer': 'تحويل خارجي',
            'assignment': 'انتداب',
            'replacement': 'إحلال'
        }
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    # ================================
    # العطلة طويلة الأمد
    # ================================
    
    def add_long_term_leave(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة عطلة طويلة الأمد"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # التحقق من عدم وجود عطلة نشطة
            active_leave = conn.execute('''
                SELECT * FROM employee_long_term_leaves 
                WHERE employee_id = ? AND status = 'نشط'
            ''', (employee_id,)).fetchone()
            
            if active_leave:
                return False, "يوجد عطلة طويلة الأمد نشطة للموظف"
            
            # إدراج العطلة الجديدة
            conn.execute('''
                INSERT INTO employee_long_term_leaves 
                (employee_id, leave_type, start_date, expected_end_date, reason, 
                 decision_number, decision_date, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'نشط', ?)
            ''', (
                employee_id,
                data.get('leave_type', ''),
                data.get('start_date'),
                data.get('expected_end_date'),
                data.get('reason', ''),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'في عطلة طويلة الأمد' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم إضافة العطلة طويلة الأمد بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة العطلة: {str(e)}"
    
    def end_long_term_leave(self, leave_id: int, end_date: str, notes: str = '') -> Tuple[bool, str]:
        """إنهاء عطلة طويلة الأمد"""
        try:
            conn = self.get_db_connection()
            
            # الحصول على بيانات العطلة
            leave = conn.execute('''
                SELECT * FROM employee_long_term_leaves WHERE id = ?
            ''', (leave_id,)).fetchone()
            
            if not leave:
                return False, "العطلة غير موجودة"
            
            # تحديث العطلة
            conn.execute('''
                UPDATE employee_long_term_leaves 
                SET status = 'منتهية', actual_end_date = ?, end_notes = ?
                WHERE id = ?
            ''', (end_date, notes, leave_id))
            
            # إعادة تفعيل الموظف
            conn.execute('''
                UPDATE employees SET status = 'نشط' WHERE id = ?
            ''', (leave['employee_id'],))
            
            conn.commit()
            conn.close()
            return True, "تم إنهاء العطلة بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إنهاء العطلة: {str(e)}"
    
    # ================================
    # الاستقالة
    # ================================
    
    def add_resignation(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة استقالة"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # التحقق من عدم وجود استقالة سابقة
            existing = conn.execute('''
                SELECT * FROM employee_resignations WHERE employee_id = ?
            ''', (employee_id,)).fetchone()
            
            if existing:
                return False, "يوجد استقالة مسجلة مسبقاً لهذا الموظف"
            
            # إدراج الاستقالة
            conn.execute('''
                INSERT INTO employee_resignations 
                (employee_id, resignation_date, reason, notice_period_days, 
                 last_working_day, decision_number, decision_date, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'معلقة', ?)
            ''', (
                employee_id,
                data.get('resignation_date'),
                data.get('reason', ''),
                data.get('notice_period_days', 30),
                data.get('last_working_day'),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'استقالة معلقة' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم تسجيل الاستقالة بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تسجيل الاستقالة: {str(e)}"
    
    def approve_resignation(self, resignation_id: int, approval_date: str, notes: str = '') -> Tuple[bool, str]:
        """الموافقة على الاستقالة"""
        try:
            conn = self.get_db_connection()
            
            # الحصول على بيانات الاستقالة
            resignation = conn.execute('''
                SELECT * FROM employee_resignations WHERE id = ?
            ''', (resignation_id,)).fetchone()
            
            if not resignation:
                return False, "الاستقالة غير موجودة"
            
            # تحديث الاستقالة
            conn.execute('''
                UPDATE employee_resignations 
                SET status = 'موافق عليها', approval_date = ?, approval_notes = ?
                WHERE id = ?
            ''', (approval_date, notes, resignation_id))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'مستقيل' WHERE id = ?
            ''', (resignation['employee_id'],))
            
            conn.commit()
            conn.close()
            return True, "تم الموافقة على الاستقالة"
            
        except Exception as e:
            return False, f"خطأ في الموافقة على الاستقالة: {str(e)}"
    
    # ================================
    # الاستيداع
    # ================================
    
    def add_leave_of_absence(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة استيداع"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # التحقق من عدم وجود استيداع نشط
            active_absence = conn.execute('''
                SELECT * FROM employee_leave_of_absence 
                WHERE employee_id = ? AND status = 'نشط'
            ''', (employee_id,)).fetchone()
            
            if active_absence:
                return False, "يوجد استيداع نشط للموظف"
            
            # إدراج الاستيداع
            conn.execute('''
                INSERT INTO employee_leave_of_absence 
                (employee_id, absence_type, start_date, expected_duration_months, 
                 reason, decision_number, decision_date, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'نشط', ?)
            ''', (
                employee_id,
                data.get('absence_type', ''),
                data.get('start_date'),
                data.get('expected_duration_months', 12),
                data.get('reason', ''),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'مستودع' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم تسجيل الاستيداع بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تسجيل الاستيداع: {str(e)}"
    
    def end_leave_of_absence(self, absence_id: int, end_date: str, notes: str = '') -> Tuple[bool, str]:
        """إنهاء الاستيداع"""
        try:
            conn = self.get_db_connection()
            
            # الحصول على بيانات الاستيداع
            absence = conn.execute('''
                SELECT * FROM employee_leave_of_absence WHERE id = ?
            ''', (absence_id,)).fetchone()
            
            if not absence:
                return False, "الاستيداع غير موجود"
            
            # تحديث الاستيداع
            conn.execute('''
                UPDATE employee_leave_of_absence 
                SET status = 'منتهي', actual_end_date = ?, end_notes = ?
                WHERE id = ?
            ''', (end_date, notes, absence_id))
            
            # إعادة تفعيل الموظف
            conn.execute('''
                UPDATE employees SET status = 'نشط' WHERE id = ?
            ''', (absence['employee_id'],))
            
            conn.commit()
            conn.close()
            return True, "تم إنهاء الاستيداع بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إنهاء الاستيداع: {str(e)}"
    
    # ================================
    # الوفاة
    # ================================
    
    def add_death_record(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """تسجيل وفاة موظف"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # التحقق من عدم وجود تسجيل وفاة سابق
            existing = conn.execute('''
                SELECT * FROM employee_deaths WHERE employee_id = ?
            ''', (employee_id,)).fetchone()
            
            if existing:
                return False, "يوجد تسجيل وفاة مسبق لهذا الموظف"
            
            # إدراج تسجيل الوفاة
            conn.execute('''
                INSERT INTO employee_deaths 
                (employee_id, death_date, death_place, death_cause, 
                 certificate_number, certificate_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id,
                data.get('death_date'),
                data.get('death_place', ''),
                data.get('death_cause', ''),
                data.get('certificate_number', ''),
                data.get('certificate_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'متوفى' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم تسجيل الوفاة بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تسجيل الوفاة: {str(e)}"
    
    # ================================
    # التوقيف
    # ================================
    
    def add_suspension(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة توقيف"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # التحقق من عدم وجود توقيف نشط
            active_suspension = conn.execute('''
                SELECT * FROM employee_suspensions 
                WHERE employee_id = ? AND status = 'نشط'
            ''', (employee_id,)).fetchone()
            
            if active_suspension:
                return False, "يوجد توقيف نشط للموظف"
            
            # إدراج التوقيف
            conn.execute('''
                INSERT INTO employee_suspensions 
                (employee_id, suspension_date, reason, suspension_type, 
                 duration_days, decision_number, decision_date, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'نشط', ?)
            ''', (
                employee_id,
                data.get('suspension_date'),
                data.get('reason', ''),
                data.get('suspension_type', 'مؤقت'),
                data.get('duration_days'),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'موقوف' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم تسجيل التوقيف بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تسجيل التوقيف: {str(e)}"
    
    def end_suspension(self, suspension_id: int, end_date: str, notes: str = '') -> Tuple[bool, str]:
        """إنهاء التوقيف"""
        try:
            conn = self.get_db_connection()
            
            # الحصول على بيانات التوقيف
            suspension = conn.execute('''
                SELECT * FROM employee_suspensions WHERE id = ?
            ''', (suspension_id,)).fetchone()
            
            if not suspension:
                return False, "التوقيف غير موجود"
            
            # تحديث التوقيف
            conn.execute('''
                UPDATE employee_suspensions 
                SET status = 'منتهي', actual_end_date = ?, end_notes = ?
                WHERE id = ?
            ''', (end_date, notes, suspension_id))
            
            # إعادة تفعيل الموظف
            conn.execute('''
                UPDATE employees SET status = 'نشط' WHERE id = ?
            ''', (suspension['employee_id'],))
            
            conn.commit()
            conn.close()
            return True, "تم إنهاء التوقيف بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إنهاء التوقيف: {str(e)}"
    
    # ================================
    # التقاعد
    # ================================
    
    def add_retirement(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة تقاعد"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # التحقق من عدم وجود تقاعد سابق
            existing = conn.execute('''
                SELECT * FROM employee_retirements WHERE employee_id = ?
            ''', (employee_id,)).fetchone()
            
            if existing:
                return False, "يوجد تسجيل تقاعد مسبق لهذا الموظف"
            
            # إدراج التقاعد
            conn.execute('''
                INSERT INTO employee_retirements 
                (employee_id, retirement_date, retirement_type, years_of_service, 
                 pension_amount, decision_number, decision_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id,
                data.get('retirement_date'),
                data.get('retirement_type', 'تقاعد عادي'),
                data.get('years_of_service', 0),
                data.get('pension_amount', 0),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'متقاعد' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم تسجيل التقاعد بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تسجيل التقاعد: {str(e)}"
    
    # ================================
    # التحويل الخارجي (معالجة خاصة)
    # ================================
    
    def add_external_transfer(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة تحويل خارجي - ينقل الموظف إلى ملف منفصل"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # إدراج التحويل الخارجي
            conn.execute('''
                INSERT INTO employee_external_transfers 
                (employee_id, transfer_date, destination_organization, 
                 destination_department, reason, decision_number, decision_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id,
                data.get('transfer_date'),
                data.get('destination_organization', ''),
                data.get('destination_department', ''),
                data.get('reason', ''),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف إلى "محول خارجياً"
            # هذا يعني أنه لن يظهر في التعداد العادي للمؤسسة
            conn.execute('''
                UPDATE employees SET status = 'محول خارجياً' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم تسجيل التحويل الخارجي بنجاح - الموظف لم يعد ضمن تعداد المؤسسة"
            
        except Exception as e:
            return False, f"خطأ في تسجيل التحويل الخارجي: {str(e)}"
    
    # ================================
    # الانتداب
    # ================================
    
    def add_assignment(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة انتداب"""
        try:
            conn = self.get_db_connection()
            
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # إدراج الانتداب
            conn.execute('''
                INSERT INTO employee_assignments 
                (employee_id, assignment_date, assignment_location, assignment_type,
                 expected_duration_months, decision_number, decision_date, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'نشط', ?)
            ''', (
                employee_id,
                data.get('assignment_date'),
                data.get('assignment_location', ''),
                data.get('assignment_type', ''),
                data.get('expected_duration_months', 12),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'منتدب' WHERE id = ?
            ''', (employee_id,))
            
            conn.commit()
            conn.close()
            return True, "تم تسجيل الانتداب بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تسجيل الانتداب: {str(e)}"
    
    def end_assignment(self, assignment_id: int, end_date: str, notes: str = '') -> Tuple[bool, str]:
        """إنهاء الانتداب"""
        try:
            conn = self.get_db_connection()
            
            # الحصول على بيانات الانتداب
            assignment = conn.execute('''
                SELECT * FROM employee_assignments WHERE id = ?
            ''', (assignment_id,)).fetchone()
            
            if not assignment:
                return False, "الانتداب غير موجود"
            
            # تحديث الانتداب
            conn.execute('''
                UPDATE employee_assignments 
                SET status = 'منتهي', actual_end_date = ?, end_notes = ?
                WHERE id = ?
            ''', (end_date, notes, assignment_id))
            
            # إعادة تفعيل الموظف
            conn.execute('''
                UPDATE employees SET status = 'نشط' WHERE id = ?
            ''', (assignment['employee_id'],))
            
            conn.commit()
            conn.close()
            return True, "تم إنهاء الانتداب بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إنهاء الانتداب: {str(e)}"
    
    # ================================
    # وظائف الاستعلام والتقارير
    # ================================
    
    def get_employee_status_history(self, employee_id: int) -> Dict:
        """الحصول على تاريخ حالات الموظف"""
        conn = self.get_db_connection()
        history = {
            'long_term_leaves': [],
            'resignations': [],
            'leave_of_absence': [],
            'deaths': [],
            'suspensions': [],
            'retirements': [],
            'external_transfers': [],
            'assignments': [],
            'replacements': []
        }
        
        try:
            # العطل طويلة الأمد
            history['long_term_leaves'] = conn.execute('''
                SELECT * FROM employee_long_term_leaves 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
            # الاستقالات
            history['resignations'] = conn.execute('''
                SELECT * FROM employee_resignations 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
            # الاستيداع
            history['leave_of_absence'] = conn.execute('''
                SELECT * FROM employee_leave_of_absence 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
            # الوفيات
            history['deaths'] = conn.execute('''
                SELECT * FROM employee_deaths 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
            # التوقيفات
            history['suspensions'] = conn.execute('''
                SELECT * FROM employee_suspensions 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
            # التقاعد
            history['retirements'] = conn.execute('''
                SELECT * FROM employee_retirements 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
            # التحويلات الخارجية
            history['external_transfers'] = conn.execute('''
                SELECT * FROM employee_external_transfers 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
            # الانتدابات
            history['assignments'] = conn.execute('''
                SELECT * FROM employee_assignments 
                WHERE employee_id = ? ORDER BY created_at DESC
            ''', (employee_id,)).fetchall()
            
        except Exception as e:
            print(f"خطأ في الحصول على تاريخ الحالات: {e}")
        
        conn.close()
        return history
    
    def get_employees_by_status(self, status: str) -> List:
        """الحصول على الموظفين حسب الحالة"""
        conn = self.get_db_connection()
        
        employees = conn.execute('''
            SELECT e.*, r.name as rank_name, s.name as service_name
            FROM employees e
            LEFT JOIN ranks r ON e.current_rank_id = r.id
            LEFT JOIN services s ON e.current_service_id = s.id
            WHERE e.status = ?
            ORDER BY e.registration_number
        ''', (status,)).fetchall()
        
        conn.close()
        return employees
    
    def get_active_employees_count(self) -> int:
        """عدد الموظفين النشطين (باستثناء المحولين خارجياً)"""
        conn = self.get_db_connection()
        
        count = conn.execute('''
            SELECT COUNT(*) FROM employees 
            WHERE status NOT IN ('محول خارجياً', 'متوفى', 'مستقيل', 'متقاعد')
        ''').fetchone()[0]
        
        conn.close()
        return count
    
    def get_external_transfers(self) -> List:
        """الحصول على قائمة التحويلات الخارجية"""
        conn = self.get_db_connection()
        
        transfers = conn.execute('''
            SELECT et.*, e.registration_number, e.first_name, e.last_name,
                   r.name as rank_name
            FROM employee_external_transfers et
            JOIN employees e ON et.employee_id = e.id
            LEFT JOIN ranks r ON e.current_rank_id = r.id
            ORDER BY et.transfer_date DESC
        ''').fetchall()
        
        conn.close()
        return transfers
    
    def get_status_statistics(self) -> Dict:
        """إحصائيات الحالات"""
        conn = self.get_db_connection()
        
        stats = {}
        
        # إحصائيات عامة
        stats['total_employees'] = conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0]
        stats['active_employees'] = self.get_active_employees_count()
        
        # إحصائيات الحالات
        for status_key, status_name in self.status_types.items():
            if status_key == 'external_transfer':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = 'محول خارجياً'
                ''').fetchone()[0]
            elif status_key == 'death':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = 'متوفى'
                ''').fetchone()[0]
            elif status_key == 'resignation':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status IN ('مستقيل', 'استقالة معلقة')
                ''').fetchone()[0]
            elif status_key == 'retirement':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = 'متقاعد'
                ''').fetchone()[0]
            elif status_key == 'suspension':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = 'موقوف'
                ''').fetchone()[0]
            elif status_key == 'leave_of_absence':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = 'مستودع'
                ''').fetchone()[0]
            elif status_key == 'long_term_leave':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = 'في عطلة طويلة الأمد'
                ''').fetchone()[0]
            elif status_key == 'assignment':
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = 'منتدب'
                ''').fetchone()[0]
        
        conn.close()
        return stats
    
    # ================================
    # وظائف مساعدة
    # ================================
    
    def validate_status_data(self, status_type: str, data: Dict) -> Tuple[bool, str]:
        """التحقق من صحة بيانات الحالة"""
        required_fields = {
            'long_term_leave': ['start_date', 'leave_type', 'reason'],
            'resignation': ['resignation_date', 'reason'],
            'leave_of_absence': ['start_date', 'absence_type', 'reason'],
            'death': ['death_date'],
            'suspension': ['suspension_date', 'reason'],
            'retirement': ['retirement_date', 'retirement_type'],
            'external_transfer': ['transfer_date', 'destination_organization'],
            'assignment': ['assignment_date', 'assignment_location']
        }
        
        if status_type not in required_fields:
            return False, "نوع الحالة غير صحيح"
        
        for field in required_fields[status_type]:
            if not data.get(field):
                return False, f"الحقل {field} مطلوب"
        
        return True, "البيانات صحيحة"
    
    def get_status_display_name(self, status_key: str) -> str:
        """الحصول على الاسم المعروض للحالة"""
        return self.status_types.get(status_key, status_key)


# ================================
# وظائف مساعدة للاستخدام المباشر
# ================================

def create_status_manager(db_path: str = 'customs_employees.db') -> EmployeeStatusManager:
    """إنشاء مدير حالات الموظفين"""
    return EmployeeStatusManager(db_path)

def get_employee_current_status(employee_id: int, db_path: str = 'customs_employees.db') -> Dict:
    """الحصول على الحالة الحالية للموظف"""
    manager = EmployeeStatusManager(db_path)
    conn = manager.get_db_connection()
    
    employee = conn.execute('''
        SELECT status FROM employees WHERE id = ?
    ''', (employee_id,)).fetchone()
    
    conn.close()
    
    if employee:
        return {
            'employee_id': employee_id,
            'current_status': employee['status'],
            'status_display': manager.get_status_display_name(employee['status'])
        }
    
    return None