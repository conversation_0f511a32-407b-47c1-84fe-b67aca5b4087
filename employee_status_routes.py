#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات إدارة حالات الموظفين - Flask Routes
Employee Status Management Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from comprehensive_employee_status_system import ComprehensiveEmployeeStatusSystem
from datetime import datetime, date
import json

# إنشاء Blueprint
employee_status_bp = Blueprint('employee_status', __name__, url_prefix='/employee_status')

# إنشاء نسخة من النظام
status_system = ComprehensiveEmployeeStatusSystem()

# ================================
# الصفحة الرئيسية لحالات الموظفين
# ================================

@employee_status_bp.route('/')
def index():
    """الصفحة الرئيسية لحالات الموظفين"""
    stats = status_system.get_employee_statistics()
    return render_template('employee_status/comprehensive_dashboard.html', stats=stats)

# ================================
# الحالات المؤقتة
# ================================

@employee_status_bp.route('/leave_of_absence/add/<int:employee_id>', methods=['GET', 'POST'])
def add_leave_of_absence(employee_id):
    """إضافة استيداع"""
    if request.method == 'POST':
        data = {
            'reason_id': request.form.get('reason_id'),
            'duration_months': request.form.get('duration_months'),
            'start_date': request.form.get('start_date'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'created_by': 'النظام'  # يمكن تحديثه لاحقاً
        }
        
        success, message = status_system.add_leave_of_absence(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على أسباب الاستيداع
    conn = status_system.get_db_connection()
    reasons = conn.execute('''
        SELECT id, reason FROM leave_of_absence_reasons WHERE is_active = 1
    ''').fetchall()
    conn.close()
    
    # الحصول على بيانات الموظف
    conn = status_system.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('employee_status/add_leave_of_absence.html', 
                         employee=employee, reasons=reasons)

@employee_status_bp.route('/suspension/add/<int:employee_id>', methods=['GET', 'POST'])
def add_suspension(employee_id):
    """إضافة توقيف"""
    if request.method == 'POST':
        data = {
            'suspension_date': request.form.get('suspension_date'),
            'suspension_reason': request.form.get('suspension_reason'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'end_date': request.form.get('end_date') if request.form.get('end_date') else None,
            'created_by': 'النظام'
        }
        
        success, message = status_system.add_suspension(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على بيانات الموظف
    conn = status_system.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('employee_status/add_suspension.html', employee=employee)

# ================================
# الحالات النهائية (الحذف)
# ================================

@employee_status_bp.route('/death/add/<int:employee_id>', methods=['GET', 'POST'])
def add_death(employee_id):
    """إضافة وفاة (نقل لجدول الوفيات)"""
    if request.method == 'POST':
        data = {
            'death_date': request.form.get('death_date'),
            'death_cause': request.form.get('death_cause'),
            'death_certificate_number': request.form.get('death_certificate_number'),
            'transferred_by': 'النظام'
        }
        
        success, message = status_system.transfer_to_death(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على بيانات الموظف
    conn = status_system.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('employee_status/add_death.html', employee=employee)

@employee_status_bp.route('/external_transfer/add/<int:employee_id>', methods=['GET', 'POST'])
def add_external_transfer(employee_id):
    """إضافة تحويل خارجي"""
    if request.method == 'POST':
        data = {
            'transfer_date': request.form.get('transfer_date'),
            'destination_directorate': request.form.get('destination_directorate'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'transferred_by': 'النظام'
        }
        
        success, message = status_system.transfer_to_external(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على بيانات الموظف
    conn = status_system.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('employee_status/add_external_transfer.html', employee=employee)

# ================================
# عرض الحالات المحذوفة
# ================================

@employee_status_bp.route('/deceased')
def deceased_employees():
    """عرض الموظفين المتوفين"""
    conn = status_system.get_db_connection()
    deceased = conn.execute('''
        SELECT * FROM deceased_employees ORDER BY death_date DESC
    ''').fetchall()
    conn.close()
    
    return render_template('employee_status/deceased_list.html', deceased=deceased)

@employee_status_bp.route('/external_transfers')
def external_transfers():
    """عرض الموظفين المحولين خارجياً"""
    conn = status_system.get_db_connection()
    transfers = conn.execute('''
        SELECT * FROM external_transfer_employees ORDER BY transfer_date DESC
    ''').fetchall()
    conn.close()
    
    return render_template('employee_status/external_transfers_list.html', transfers=transfers)

@employee_status_bp.route('/retired')
def retired_employees():
    """عرض الموظفين المتقاعدين"""
    conn = status_system.get_db_connection()
    retired = conn.execute('''
        SELECT * FROM retired_employees ORDER BY retirement_date DESC
    ''').fetchall()
    conn.close()
    
    return render_template('employee_status/retired_list.html', retired=retired)

# ================================
# عرض تفاصيل الموظف المحذوف
# ================================

@employee_status_bp.route('/deceased/<int:deceased_id>')
def view_deceased(deceased_id):
    """عرض تفاصيل موظف متوفى"""
    conn = status_system.get_db_connection()
    deceased = conn.execute('''
        SELECT * FROM deceased_employees WHERE id = ?
    ''', (deceased_id,)).fetchone()
    conn.close()
    
    if not deceased:
        flash('الموظف المتوفى غير موجود', 'error')
        return redirect(url_for('employee_status.deceased_employees'))
    
    # تحويل JSON إلى dict
    employee_data = json.loads(deceased['employee_data']) if deceased['employee_data'] else {}
    
    return render_template('employee_status/view_deceased.html', 
                         deceased=deceased, employee_data=employee_data)

# ================================
# إدارة أسباب الاستيداع
# ================================

@employee_status_bp.route('/settings/leave_reasons')
def manage_leave_reasons():
    """إدارة أسباب الاستيداع"""
    conn = status_system.get_db_connection()
    reasons = conn.execute('''
        SELECT * FROM leave_of_absence_reasons ORDER BY reason
    ''').fetchall()
    conn.close()
    
    return render_template('employee_status/manage_leave_reasons.html', reasons=reasons)

@employee_status_bp.route('/settings/leave_reasons/add', methods=['POST'])
def add_leave_reason():
    """إضافة سبب استيداع جديد"""
    reason = request.form.get('reason')
    if not reason:
        flash('يجب إدخال سبب الاستيداع', 'error')
        return redirect(url_for('employee_status.manage_leave_reasons'))
    
    conn = status_system.get_db_connection()
    try:
        conn.execute('''
            INSERT INTO leave_of_absence_reasons (reason) VALUES (?)
        ''', (reason,))
        conn.commit()
        flash('تم إضافة السبب بنجاح', 'success')
    except Exception as e:
        flash(f'خطأ في الإضافة: {str(e)}', 'error')
    finally:
        conn.close()
    
    return redirect(url_for('employee_status.manage_leave_reasons'))

# ================================
# API للإحصائيات
# ================================

@employee_status_bp.route('/api/statistics')
def api_statistics():
    """API للحصول على الإحصائيات"""
    stats = status_system.get_employee_statistics()
    return jsonify(stats)

@employee_status_bp.route('/api/employee/<int:employee_id>/leave_balance')
def api_employee_leave_balance(employee_id):
    """API للحصول على رصيد الاستيداع للموظف"""
    conn = status_system.get_db_connection()
    try:
        total_used = conn.execute('''
            SELECT COALESCE(SUM(duration_months), 0) 
            FROM employee_leave_of_absence 
            WHERE employee_id = ?
        ''', (employee_id,)).fetchone()[0]
        
        remaining = 60 - total_used  # 60 شهر = 5 سنوات
        
        return jsonify({
            'employee_id': employee_id,
            'total_used_months': total_used,
            'remaining_months': remaining,
            'remaining_years': remaining / 12
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

def register_employee_status_routes(app):
    """تسجيل مسارات حالات الموظفين"""
    app.register_blueprint(employee_status_bp)
    print("✅ تم تسجيل مسارات حالات الموظفين")