{% extends "base.html" %}

{% block page_title %}إضافة استقالة جديدة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-user-minus me-2"></i>
                            إضافة استقالة جديدة
                        </h3>
                        <a href="{{ url_for('special_status.resignations') }}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" id="resignationForm">
                        <div class="row">
                            <!-- اختيار الموظف -->
                            <div class="col-md-6 mb-3">
                                <label for="employee_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                                <select class="form-select" id="employee_id" name="employee_id" required onchange="loadEmployeeInfo()">
                                    <option value="">اختر الموظف</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">
                                        {{ employee.registration_number }} - {{ employee.first_name }} {{ employee.last_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- تاريخ الاستقالة -->
                            <div class="col-md-6 mb-3">
                                <label for="resignation_date" class="form-label">تاريخ الاستقالة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="resignation_date" name="resignation_date" required>
                            </div>
                        </div>

                        <!-- معلومات الموظف -->
                        <div id="employeeInfo" class="row mb-3" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6>معلومات الموظف:</h6>
                                    <div id="employeeDetails"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- السبب -->
                            <div class="col-12 mb-3">
                                <label for="reason" class="form-label">سبب الاستقالة</label>
                                <textarea class="form-control" id="reason" name="reason" rows="3" 
                                         placeholder="اكتب سبب الاستقالة..."></textarea>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم القرار -->
                            <div class="col-md-6 mb-3">
                                <label for="decision_number" class="form-label">رقم القرار</label>
                                <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                       placeholder="رقم قرار الاستقالة">
                            </div>

                            <!-- تاريخ القرار -->
                            <div class="col-md-6 mb-3">
                                <label for="decision_date" class="form-label">تاريخ القرار</label>
                                <input type="date" class="form-control" id="decision_date" name="decision_date">
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ السريان -->
                            <div class="col-md-6 mb-3">
                                <label for="effective_date" class="form-label">تاريخ السريان</label>
                                <input type="date" class="form-control" id="effective_date" name="effective_date">
                            </div>

                            <!-- حالة الاستقالة -->
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">حالة الاستقالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="pending">معلقة</option>
                                    <option value="approved">مؤكدة</option>
                                    <option value="rejected">مرفوضة</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- ملاحظات -->
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات إضافية</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                         placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-save me-2"></i>حفظ الاستقالة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadEmployeeInfo() {
    const employeeId = document.getElementById('employee_id').value;
    
    if (!employeeId) {
        document.getElementById('employeeInfo').style.display = 'none';
        return;
    }
    
    fetch(`/special_status/api/employee/${employeeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('خطأ في تحميل بيانات الموظف: ' + data.error);
                return;
            }
            
            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>رقم التسجيل:</strong> ${data.registration_number}<br>
                        <strong>الاسم الكامل:</strong> ${data.first_name} ${data.last_name}
                    </div>
                    <div class="col-md-6">
                        <strong>الرتبة:</strong> ${data.rank_name || 'غير محدد'}<br>
                        <strong>المصلحة:</strong> ${data.service_name || 'غير محدد'}
                    </div>
                </div>
            `;
            
            document.getElementById('employeeDetails').innerHTML = details;
            document.getElementById('employeeInfo').style.display = 'block';
        })
        .catch(error => {
            alert('خطأ في تحميل البيانات: ' + error);
        });
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
        document.getElementById('resignationForm').reset();
        document.getElementById('employeeInfo').style.display = 'none';
    }
}

// تعيين التاريخ الحالي كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('resignation_date').value = today;
});

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('resignationForm').addEventListener('submit', function(e) {
    const employeeId = document.getElementById('employee_id').value;
    const resignationDate = document.getElementById('resignation_date').value;
    
    if (!employeeId) {
        e.preventDefault();
        alert('يرجى اختيار الموظف');
        return;
    }
    
    if (!resignationDate) {
        e.preventDefault();
        alert('يرجى تحديد تاريخ الاستقالة');
        return;
    }
    
    // التحقق من أن تاريخ الاستقالة ليس في المستقبل البعيد
    const selectedDate = new Date(resignationDate);
    const today = new Date();
    const oneYearFromNow = new Date();
    oneYearFromNow.setFullYear(today.getFullYear() + 1);
    
    if (selectedDate > oneYearFromNow) {
        e.preventDefault();
        alert('تاريخ الاستقالة لا يمكن أن يكون أكثر من سنة في المستقبل');
        return;
    }
});
</script>
{% endblock %}
