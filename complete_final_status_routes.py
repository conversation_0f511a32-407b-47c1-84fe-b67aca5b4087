#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات الحالات النهائية الكاملة
Complete Final Status Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from complete_status_transfers import CompleteStatusTransfers
from datetime import datetime, date

# إنشاء Blueprint
final_status_bp = Blueprint('final_status', __name__, url_prefix='/final_status')

# إنشاء نسخة من المدير
transfer_manager = CompleteStatusTransfers()

@final_status_bp.route('/external_transfer/add/<int:employee_id>', methods=['GET', 'POST'])
def add_external_transfer(employee_id):
    """إضافة تحويل خارجي"""
    if request.method == 'POST':
        data = {
            'transfer_date': request.form.get('transfer_date'),
            'destination_directorate': request.form.get('destination_directorate'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'transferred_by': 'النظام'
        }
        
        success, message = transfer_manager.transfer_to_external(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على بيانات الموظف
    conn = transfer_manager.manager.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('final_status/add_external_transfer.html', employee=employee)

@final_status_bp.route('/dismissal/add/<int:employee_id>', methods=['GET', 'POST'])
def add_dismissal(employee_id):
    """إضافة عزل"""
    if request.method == 'POST':
        data = {
            'dismissal_date': request.form.get('dismissal_date'),
            'dismissal_reason': request.form.get('dismissal_reason'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'transferred_by': 'النظام'
        }
        
        success, message = transfer_manager.transfer_to_dismissal(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على بيانات الموظف
    conn = transfer_manager.manager.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('final_status/add_dismissal.html', employee=employee)

@final_status_bp.route('/retirement/add/<int:employee_id>', methods=['GET', 'POST'])
def add_retirement(employee_id):
    """إضافة تقاعد"""
    if request.method == 'POST':
        data = {
            'retirement_date': request.form.get('retirement_date'),
            'retirement_decision_number': request.form.get('retirement_decision_number'),
            'retirement_decision_date': request.form.get('retirement_decision_date'),
            'retirement_card_number': request.form.get('retirement_card_number'),
            'retirement_card_issue_date': request.form.get('retirement_card_issue_date'),
            'transferred_by': 'النظام'
        }
        
        success, message = transfer_manager.transfer_to_retirement(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على بيانات الموظف
    conn = transfer_manager.manager.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('final_status/add_retirement.html', employee=employee)

@final_status_bp.route('/external_transfers')
def external_transfers_list():
    """قائمة المحولين خارجياً"""
    transfers = transfer_manager.get_external_transfers()
    return render_template('final_status/external_transfers_list.html', transfers=transfers)

@final_status_bp.route('/dismissed')
def dismissed_list():
    """قائمة المعزولين"""
    dismissed = transfer_manager.get_dismissed_employees()
    return render_template('final_status/dismissed_list.html', dismissed=dismissed)

@final_status_bp.route('/retired')
def retired_list():
    """قائمة المتقاعدين"""
    retired = transfer_manager.get_retired_employees()
    return render_template('final_status/retired_list.html', retired=retired)

@final_status_bp.route('/api/employee_data/<table_name>/<int:record_id>')
def api_employee_data(table_name, record_id):
    """API للحصول على البيانات الأصلية للموظف"""
    data = transfer_manager.get_employee_original_data(table_name, record_id)
    if data:
        return jsonify(data)
    else:
        return jsonify({'error': 'البيانات غير موجودة'}), 404

def register_final_status_routes(app):
    """تسجيل مسارات الحالات النهائية"""
    app.register_blueprint(final_status_bp)
    print("✅ تم تسجيل مسارات الحالات النهائية")
