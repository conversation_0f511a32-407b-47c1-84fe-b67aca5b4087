{% extends "base.html" %}

{% block title %}{{ employee.first_name }} {{ employee.last_name }} - تفاصيل الموظف{% endblock %}

{% block page_title %}تفاصيل الموظف{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الموظفين
                </a>
                <a href="{{ url_for('edit_employee', employee_id=employee.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>تعديل البيانات
                </a>
            </div>
            <div>
                <button class="btn btn-info" onclick="printEmployee()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <button class="btn btn-success" onclick="exportEmployee()">
                    <i class="fas fa-download me-2"></i>تصدير
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- العمود الأيسر - الصورة والمعلومات السريعة -->
    <div class="col-lg-4">
        <!-- صورة الموظف -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-camera me-2"></i>صورة الموظف</h5>
            </div>
            <div class="card-body text-center">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة {{ employee.first_name }} {{ employee.last_name }}" 
                     class="img-fluid employee-photo mb-3" 
                     style="max-width: 250px; max-height: 300px; border-radius: 0.5rem; border: 2px solid #dee2e6;">
                {% else %}
                <div class="bg-secondary text-white d-flex align-items-center justify-content-center mb-3" 
                     style="width: 250px; height: 300px; border-radius: 0.5rem; margin: 0 auto;">
                    <i class="fas fa-user fa-5x"></i>
                </div>
                <p class="text-muted">لا توجد صورة</p>
                {% endif %}
                
                <!-- معلومات سريعة -->
                <h4 class="text-primary mb-1">{{ employee.first_name }} {{ employee.last_name }}</h4>
                {% if employee.first_name_fr or employee.last_name_fr %}
                <p class="text-muted mb-3">{{ employee.first_name_fr }} {{ employee.last_name_fr }}</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary mb-1">{{ employee.registration_number }}</h6>
                            <small class="text-muted">رقم التسجيل</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="mb-1">
                            <span class="badge employee-status-badge" data-status="{{ employee.status or 'غير محدد' }}">
                                {{ employee.status or 'غير محدد' }}
                            </span>
                        </h6>
                        <small class="text-muted">الحالة</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-phone me-2"></i>بيانات الاتصال</h5>
            </div>
            <div class="card-body">
                {% if employee.phone1 %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">رقم الهاتف 1</label>
                    <p class="mb-0"><i class="fas fa-phone text-primary me-2"></i>{{ employee.phone1 }}</p>
                </div>
                {% endif %}
                
                {% if employee.phone2 %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">رقم الهاتف 2</label>
                    <p class="mb-0"><i class="fas fa-mobile-alt text-success me-2"></i>{{ employee.phone2 }}</p>
                </div>
                {% endif %}
                
                {% if employee.phone %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">رقم الهاتف (قديم)</label>
                    <p class="mb-0"><i class="fas fa-phone-alt text-warning me-2"></i>{{ employee.phone }}</p>
                </div>
                {% endif %}
                
                {% if employee.email %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">البريد الإلكتروني</label>
                    <p class="mb-0"><i class="fas fa-envelope text-primary me-2"></i>{{ employee.email }}</p>
                </div>
                {% endif %}
                
                {% if employee.address %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">العنوان الرئيسي</label>
                    <p class="mb-0"><i class="fas fa-map-marker-alt text-danger me-2"></i>{{ employee.address }}</p>
                </div>
                {% endif %}
                
                {% if employee.secondary_address %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">العنوان الثانوي</label>
                    <p class="mb-0"><i class="fas fa-map-marker text-info me-2"></i>{{ employee.secondary_address }}</p>
                </div>
                {% endif %}
                
                <!-- رسالة إذا لم توجد بيانات اتصال -->
                {% if not employee.phone1 and not employee.phone2 and not employee.phone and not employee.email and not employee.address and not employee.secondary_address %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-info-circle me-2"></i>لا توجد بيانات اتصال مسجلة
                </div>
                {% endif %}
            </div>
        </div>

        <!-- بيانات الطوارئ -->
        {% if employee.emergency_contact_name or employee.emergency_contact_address %}
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>بيانات الطوارئ</h5>
            </div>
            <div class="card-body">
                {% if employee.emergency_contact_name %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">الشخص المتصل به في حالة الضرورة</label>
                    <p class="mb-0"><i class="fas fa-user-shield text-danger me-2"></i>{{ employee.emergency_contact_name }}</p>
                </div>
                {% endif %}
                
                {% if employee.emergency_contact_address %}
                <div class="mb-3">
                    <label class="form-label text-muted fw-bold">عنوان الشخص المتصل به</label>
                    <p class="mb-0"><i class="fas fa-home text-danger me-2"></i>{{ employee.emergency_contact_address }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- معلومات إضافية سريعة -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات سريعة</h5>
            </div>
            <div class="card-body">
                {% if employee.birth_date %}
                <div class="mb-2">
                    <small class="text-muted">العمر:</small>
                    <span class="fw-bold text-primary">{{ employee.birth_date|calculate_age }} سنة</span>
                </div>
                {% endif %}
                
                {% if employee.hire_date %}
                <div class="mb-2">
                    <small class="text-muted">سنوات الخدمة:</small>
                    <span class="fw-bold text-success">{{ employee.hire_date|calculate_service_years }} سنة</span>
                </div>
                {% endif %}
                
                {% if employee.gender %}
                <div class="mb-2">
                    <small class="text-muted">الجنس:</small>
                    {% if employee.gender == 'ذكر' %}
                    <span class="badge bg-primary"><i class="fas fa-mars me-1"></i>ذكر</span>
                    {% elif employee.gender == 'أنثى' %}
                    <span class="badge bg-pink" style="background-color: #e91e63;"><i class="fas fa-venus me-1"></i>أنثى</span>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if employee.marital_status %}
                <div class="mb-2">
                    <small class="text-muted">الحالة العائلية:</small>
                    {% if employee.marital_status == 'متزوج' %}
                    <span class="badge bg-success"><i class="fas fa-heart me-1"></i>{{ employee.marital_status }}</span>
                    {% elif employee.marital_status == 'أعزب' %}
                    <span class="badge bg-info"><i class="fas fa-user me-1"></i>{{ employee.marital_status }}</span>
                    {% else %}
                    <span class="badge bg-secondary">{{ employee.marital_status }}</span>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if employee.blood_type %}
                <div class="mb-2">
                    <small class="text-muted">زمرة الدم:</small>
                    <span class="badge bg-danger"><i class="fas fa-tint me-1"></i>{{ employee.blood_type }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- العمود الأيمن - التفاصيل الكاملة -->
    <div class="col-lg-8">
        <!-- البيانات الشخصية الأساسية -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>البيانات الشخصية الأساسية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">اللقب (عربي)</label>
                            <p class="mb-0 fs-5 fw-bold text-dark">{{ employee.last_name or 'غير محدد' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الاسم (عربي)</label>
                            <p class="mb-0 fs-5 fw-bold text-dark">{{ employee.first_name or 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">اللقب (فرنسي)</label>
                            <p class="mb-0 text-primary">{{ employee.last_name_fr or 'غير محدد' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الاسم (فرنسي)</label>
                            <p class="mb-0 text-primary">{{ employee.first_name_fr or 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ الميلاد</label>
                            <p class="mb-0">
                                {% if employee.birth_date %}
                                <i class="fas fa-calendar text-primary me-2"></i>{{ employee.birth_date }}
                                <br><small class="text-success fw-bold">العمر: {{ employee.birth_date|calculate_age }} سنة</small>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">مكان الميلاد</label>
                            <p class="mb-0">
                                {% if employee.birth_wilaya_name %}
                                <i class="fas fa-map-marker-alt text-danger me-2"></i>{{ employee.birth_wilaya_name }}
                                {% if employee.birth_commune_name %}
                                - {{ employee.birth_commune_name }}
                                {% endif %}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الجنس</label>
                            <p class="mb-0">
                                {% if employee.gender == 'ذكر' %}
                                <span class="badge bg-primary fs-6"><i class="fas fa-mars me-1"></i>ذكر</span>
                                {% elif employee.gender == 'أنثى' %}
                                <span class="badge fs-6" style="background-color: #e91e63;"><i class="fas fa-venus me-1"></i>أنثى</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- البيانات الشخصية التفصيلية -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>البيانات الشخصية التفصيلية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">رقم الضمان الاجتماعي</label>
                            <p class="mb-0">
                                {% if employee.social_security_number %}
                                <span class="font-monospace bg-light p-2 rounded border">{{ employee.social_security_number }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الحساب الجاري البريدي</label>
                            <p class="mb-0">
                                {% if employee.postal_account %}
                                <span class="font-monospace bg-light p-2 rounded border">{{ employee.postal_account }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الحالة العائلية</label>
                            <p class="mb-0">
                                {% if employee.marital_status %}
                                    {% if employee.marital_status == 'متزوج' %}
                                    <span class="badge bg-success fs-6"><i class="fas fa-heart me-1"></i>{{ employee.marital_status }}</span>
                                    {% elif employee.marital_status == 'أعزب' %}
                                    <span class="badge bg-info fs-6"><i class="fas fa-user me-1"></i>{{ employee.marital_status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary fs-6">{{ employee.marital_status }}</span>
                                    {% endif %}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">عدد الأبناء</label>
                            <p class="mb-0">
                                {% if employee.children_count is not none %}
                                <i class="fas fa-child text-primary me-2"></i><span class="fw-bold">{{ employee.children_count }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">عدد المتكفل بهم</label>
                            <p class="mb-0">
                                {% if employee.dependents_count is not none %}
                                <i class="fas fa-users text-success me-2"></i><span class="fw-bold">{{ employee.dependents_count }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">زمرة الدم</label>
                            <p class="mb-0">
                                {% if employee.blood_type %}
                                <span class="badge bg-danger fs-6"><i class="fas fa-tint me-1"></i>{{ employee.blood_type }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الرياضة الممارسة</label>
                            <p class="mb-0">
                                {% if employee.sport_practiced %}
                                <i class="fas fa-running text-success me-2"></i>{{ employee.sport_practiced }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- البيانات المهنية -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>البيانات المهنية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ التوظيف</label>
                            <p class="mb-0">
                                {% if employee.hire_date %}
                                <i class="fas fa-calendar-plus text-success me-2"></i>{{ employee.hire_date }}
                                <br><small class="text-primary fw-bold">منذ {{ employee.hire_date|calculate_service_years }} سنة</small>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الحالة الوظيفية</label>
                            <p class="mb-0">
                                <span class="badge employee-status-badge fs-6" data-status="{{ employee.status or 'غير محدد' }}">
                                    {{ employee.status or 'غير محدد' }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الرتبة الحالية</label>
                            <p class="mb-0">
                                {% if employee.rank_name %}
                                <i class="fas fa-star text-warning me-2"></i><span class="text-info fw-bold">{{ employee.rank_name }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">السلك</label>
                            <p class="mb-0">
                                {% if employee.corps_name %}
                                <i class="fas fa-users text-primary me-2"></i>{{ employee.corps_name }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">مصلحة التعيين</label>
                            <p class="mb-0">
                                {% if employee.service_name %}
                                <i class="fas fa-building text-success me-2"></i>{{ employee.service_name }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- الحقول المهنية الجديدة -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">رتبة التوظيف</label>
                            <p class="mb-0">
                                {% if employee.initial_rank_id %}
                                <span class="badge bg-secondary">{{ employee.initial_rank_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ الترقية في الرتبة</label>
                            <p class="mb-0">
                                {% if employee.rank_promotion_date %}
                                <i class="fas fa-calendar text-warning me-2"></i>{{ employee.rank_promotion_date }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">الوظيفة الحالية</label>
                            <p class="mb-0">
                                {% if employee.current_position_id %}
                                <span class="badge bg-info">{{ employee.current_position_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ التعين في الوظيفة</label>
                            <p class="mb-0">
                                {% if employee.position_assignment_date %}
                                <i class="fas fa-calendar text-info me-2"></i>{{ employee.position_assignment_date }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">المديرية</label>
                            <p class="mb-0">
                                {% if employee.directorate_id %}
                                <span class="badge bg-primary">{{ employee.directorate_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">مكان التعيين</label>
                            <p class="mb-0">
                                {% if employee.assignment_location_id %}
                                <span class="badge bg-success">{{ employee.assignment_location_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الوثائق والأرقام الرسمية -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-id-badge me-2"></i>الوثائق والأرقام الرسمية</h5>
            </div>
            <div class="card-body">
                <!-- بطاقة التعريف الوطنية -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">رقم بطاقة التعريف الوطنية</label>
                            <p class="mb-0">
                                {% if employee.national_id_number %}
                                <span class="font-monospace bg-light p-2 rounded border">{{ employee.national_id_number }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ صدور بطاقة التعريف</label>
                            <p class="mb-0">
                                {% if employee.national_id_issue_date %}
                                <i class="fas fa-calendar text-primary me-2"></i>{{ employee.national_id_issue_date }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">مكان صدور بطاقة التعريف</label>
                            <p class="mb-0">
                                {% if employee.national_id_issue_place_id %}
                                <span class="badge bg-secondary">{{ employee.national_id_issue_place_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- بطاقة المهنية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">رقم بطاقة المهنية</label>
                            <p class="mb-0">
                                {% if employee.professional_card_number %}
                                <span class="font-monospace bg-light p-2 rounded border">{{ employee.professional_card_number }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ صدور بطاقة المهنية</label>
                            <p class="mb-0">
                                {% if employee.professional_card_issue_date %}
                                <i class="fas fa-calendar text-success me-2"></i>{{ employee.professional_card_issue_date }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- رخصة السياقة -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">رقم رخصة السياقة</label>
                            <p class="mb-0">
                                {% if employee.driving_license_number %}
                                <span class="font-monospace bg-light p-2 rounded border">{{ employee.driving_license_number }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">صنف رخصة السياقة</label>
                            <p class="mb-0">
                                {% if employee.driving_license_category %}
                                <span class="badge bg-warning text-dark">{{ employee.driving_license_category }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ صدور رخصة السياقة</label>
                            <p class="mb-0">
                                {% if employee.driving_license_issue_date %}
                                <i class="fas fa-calendar text-warning me-2"></i>{{ employee.driving_license_issue_date }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">مكان صدور رخصة السياقة</label>
                            <p class="mb-0">
                                {% if employee.driving_license_issue_place_id %}
                                <span class="badge bg-warning text-dark">{{ employee.driving_license_issue_place_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- بطاقة التعاضدية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">رقم بطاقة التعاضدية</label>
                            <p class="mb-0">
                                {% if employee.mutual_card_number %}
                                <span class="font-monospace bg-light p-2 rounded border">{{ employee.mutual_card_number }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ صدور بطاقة التعاضدية</label>
                            <p class="mb-0">
                                {% if employee.mutual_card_issue_date %}
                                <i class="fas fa-calendar text-info me-2"></i>{{ employee.mutual_card_issue_date }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>معلومات النظام</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">معرف الموظف</label>
                            <p class="mb-0">
                                <span class="badge bg-primary fs-6">{{ employee.id }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">تاريخ الإنشاء</label>
                            <p class="mb-0">
                                {% if employee.created_at %}
                                <i class="fas fa-plus-circle text-success me-2"></i>{{ employee.created_at }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">آخر تحديث</label>
                            <p class="mb-0">
                                {% if employee.updated_at %}
                                <i class="fas fa-edit text-warning me-2"></i>{{ employee.updated_at }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">معرف ولاية الميلاد</label>
                            <p class="mb-0">
                                {% if employee.birth_wilaya_id %}
                                <span class="badge bg-info">{{ employee.birth_wilaya_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">معرف بلدية الميلاد</label>
                            <p class="mb-0">
                                {% if employee.birth_commune_id %}
                                <span class="badge bg-secondary">{{ employee.birth_commune_id }}</span>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted fw-bold">حالة الصورة</label>
                            <p class="mb-0">
                                {% if employee.photo %}
                                <span class="badge bg-success"><i class="fas fa-check me-1"></i>موجودة</span>
                                {% else %}
                                <span class="badge bg-warning text-dark"><i class="fas fa-times me-1"></i>غير موجودة</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .employee-photo {
        transition: transform 0.3s ease;
        cursor: pointer;
    }
    
    .employee-photo:hover {
        transform: scale(1.05);
    }
    
    .employee-status-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        font-weight: 600;
    }
    
    .employee-status-badge[data-status="نشط"] {
        background-color: #28a745 !important;
        color: white;
    }
    
    .employee-status-badge[data-status="متقاعد"] {
        background-color: #6c757d !important;
        color: white;
    }
    
    .employee-status-badge[data-status="مستقيل"] {
        background-color: #dc3545 !important;
        color: white;
    }
    
    .employee-status-badge[data-status="محول خارجياً"] {
        background-color: #fd7e14 !important;
        color: white;
    }
    
    .employee-status-badge[data-status="موقوف"] {
        background-color: #ffc107 !important;
        color: #000;
    }
    
    .employee-status-badge[data-status="مستودع"] {
        background-color: #17a2b8 !important;
        color: white;
    }
    
    .employee-status-badge[data-status="منتدب"] {
        background-color: #6f42c1 !important;
        color: white;
    }
    
    .employee-status-badge[data-status="في عطلة طويلة الأمد"] {
        background-color: #e83e8c !important;
        color: white;
    }
    
    .card-header {
        font-weight: 600;
    }
    
    .form-label.fw-bold {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }
    
    .badge.fs-6 {
        font-size: 0.9rem !important;
        padding: 0.4rem 0.8rem;
    }
    
    .font-monospace {
        font-family: 'Courier New', monospace;
        font-size: 0.95rem;
    }
    
    .bg-light.p-2.rounded.border {
        display: inline-block;
        min-width: 150px;
        text-align: center;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .card {
            border: 1px solid #000 !important;
            break-inside: avoid;
        }
        
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function printEmployee() {
    // فتح صفحة الطباعة المخصصة في نافذة جديدة
    window.open(`/employees/{{ employee.id }}/print`, '_blank', 'width=800,height=600');
}

function exportEmployee() {
    // يمكن إضافة وظيفة التصدير هنا
    alert('سيتم إضافة وظيفة التصدير قريباً');
}

// تحميل ألوان الحالات ديناميكياً
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة المزيد من JavaScript هنا إذا لزم الأمر
    console.log('تم تحميل صفحة تفاصيل الموظف بنجاح - جميع الحقول الـ50');
});
</script>
{% endblock %}