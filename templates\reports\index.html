{% extends "base.html" %}

{% block title %}التقارير - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}التقارير والطباعة{% endblock %}

{% block content %}
<!-- أنواع التقارير -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 report-card" onclick="generateReport('employees')">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x text-primary mb-3"></i>
                <h5>تقرير الموظفين</h5>
                <p class="text-muted">تقرير شامل لجميع الموظفين مع بياناتهم</p>
                <span class="badge bg-primary">{{ stats.total_employees }} موظف</span>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 report-card" onclick="generateReport('leaves')">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
                <h5>تقرير العطل</h5>
                <p class="text-muted">تقرير العطل السنوية والمرضية والأخرى</p>
                <span class="badge bg-warning">{{ stats.total_leaves }} عطلة</span>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 report-card" onclick="generateReport('promotions')">
            <div class="card-body text-center">
                <i class="fas fa-arrow-up fa-3x text-success mb-3"></i>
                <h5>تقرير الترقيات</h5>
                <p class="text-muted">تقرير ترقيات الرتب والدرجات</p>
                <span class="badge bg-success">{{ stats.total_promotions }} ترقية</span>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card h-100 report-card" onclick="generateReport('comprehensive')">
            <div class="card-body text-center">
                <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                <h5>التقرير الشامل</h5>
                <p class="text-muted">تقرير شامل لجميع بيانات النظام</p>
                <span class="badge bg-info">جميع البيانات</span>
            </div>
        </div>
    </div>
</div>

<!-- خيارات التقرير -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-filter me-2"></i>خيارات التقرير</h5>
    </div>
    <div class="card-body">
        <form id="reportOptionsForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">نوع التقرير</label>
                        <select name="report_type" class="form-select" id="reportType">
                            <option value="employees">تقرير الموظفين</option>
                            <option value="leaves">تقرير العطل</option>
                            <option value="promotions">تقرير الترقيات</option>
                            <option value="sanctions">تقرير العقوبات والمكافآت</option>
                            <option value="transfers">تقرير التنقلات</option>
                            <option value="comprehensive">التقرير الشامل</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="start_date" class="form-control" id="startDate">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="end_date" class="form-control" id="endDate">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">تنسيق التصدير</label>
                        <select name="export_format" class="form-select">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="word">Word</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">المديرية</label>
                        <select name="directorate_id" class="form-select">
                            <option value="">جميع المديريات</option>
                            {% for directorate in directorates %}
                            <option value="{{ directorate.id }}">{{ directorate.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">السلك</label>
                        <select name="corps_id" class="form-select">
                            <option value="">جميع الأسلاك</option>
                            {% for corps in corps %}
                            <option value="{{ corps.id }}">{{ corps.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">حالة الموظف</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="نشط">نشط</option>
                            <option value="تحويل">تحويل</option>
                            <option value="موقف">موقف</option>
                            <option value="استيداع">استيداع</option>
                            <option value="منتدب">منتدب</option>
                            <option value="متوفي">متوفي</option>
                            <option value="مفصول">مفصول</option>
                            <option value="مستقيل">مستقيل</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <button type="button" class="btn btn-primary me-2" onclick="generateCustomReport()">
                    <i class="fas fa-file-alt me-2"></i>إنشاء التقرير
                </button>
                <button type="button" class="btn btn-success me-2" onclick="exportReport()">
                    <i class="fas fa-download me-2"></i>تصدير
                </button>
                <button type="button" class="btn btn-info" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- معاينة التقرير -->
<div class="card" id="reportPreview" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-eye me-2"></i>معاينة التقرير</h5>
        <div>
            <button class="btn btn-sm btn-outline-primary" onclick="refreshPreview()">
                <i class="fas fa-sync-alt me-2"></i>تحديث
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="closePreview()">
                <i class="fas fa-times me-2"></i>إغلاق
            </button>
        </div>
    </div>
    <div class="card-body" id="reportContent">
        <!-- سيتم ملء محتوى التقرير هنا -->
    </div>
</div>

<!-- تقارير سريعة -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-bolt me-2"></i>تقارير سريعة</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="d-grid">
                    <button class="btn btn-outline-primary" onclick="quickReport('active_employees')">
                        <i class="fas fa-users me-2"></i>الموظفين النشطين
                    </button>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="d-grid">
                    <button class="btn btn-outline-warning" onclick="quickReport('current_leaves')">
                        <i class="fas fa-calendar-times me-2"></i>العطل الحالية
                    </button>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="d-grid">
                    <button class="btn btn-outline-success" onclick="quickReport('recent_promotions')">
                        <i class="fas fa-arrow-up me-2"></i>الترقيات الأخيرة
                    </button>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="d-grid">
                    <button class="btn btn-outline-danger" onclick="quickReport('sanctions_rewards')">
                        <i class="fas fa-gavel me-2"></i>العقوبات والمكافآت
                    </button>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="d-grid">
                    <button class="btn btn-outline-info" onclick="quickReport('transfers')">
                        <i class="fas fa-exchange-alt me-2"></i>التنقلات الأخيرة
                    </button>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="d-grid">
                    <button class="btn btn-outline-secondary" onclick="quickReport('employee_statuses')">
                        <i class="fas fa-user-cog me-2"></i>حالات الموظفين
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.report-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.report-card:hover .card-body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

#reportPreview {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media print {
    .card-header, .btn, .navbar, .sidebar {
        display: none !important;
    }
    
    #reportContent {
        padding: 0 !important;
        margin: 0 !important;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
function generateReport(type) {
    document.getElementById('reportType').value = type;
    generateCustomReport();
}

function generateCustomReport() {
    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    // إظهار معاينة التقرير
    const preview = document.getElementById('reportPreview');
    const content = document.getElementById('reportContent');
    
    preview.style.display = 'block';
    content.innerHTML = '<div class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i><p>جاري إنشاء التقرير...</p></div>';
    
    // محاكاة تحميل التقرير
    setTimeout(() => {
        let reportContent = generateReportContent(reportType, startDate, endDate);
        content.innerHTML = reportContent;
    }, 1500);
}

function generateReportContent(type, startDate, endDate) {
    const currentDate = new Date().toLocaleDateString('ar-DZ');
    
    let content = `
        <div class="report-header text-center mb-4">
            <h2>الجمهورية الجزائرية الديمقراطية الشعبية</h2>
            <h3>وزارة المالية - مديرية الجمارك</h3>
            <hr>
            <h4>${getReportTitle(type)}</h4>
            <p class="text-muted">تاريخ الإنشاء: ${currentDate}</p>
        </div>
    `;
    
    switch(type) {
        case 'employees':
            content += generateEmployeesReport();
            break;
        case 'leaves':
            content += generateLeavesReport();
            break;
        case 'promotions':
            content += generatePromotionsReport();
            break;
        case 'comprehensive':
            content += generateComprehensiveReport();
            break;
        default:
            content += '<p class="text-center">نوع التقرير غير مدعوم</p>';
    }
    
    content += `
        <div class="report-footer mt-5 pt-3 border-top">
            <div class="row">
                <div class="col-6">
                    <p><strong>المدير:</strong> ________________</p>
                    <p><strong>التوقيع:</strong></p>
                </div>
                <div class="col-6 text-end">
                    <p><strong>تاريخ الطباعة:</strong> ${currentDate}</p>
                    <p><strong>رقم الصفحة:</strong> 1</p>
                </div>
            </div>
        </div>
    `;
    
    return content;
}

function getReportTitle(type) {
    const titles = {
        'employees': 'تقرير الموظفين',
        'leaves': 'تقرير العطل والإجازات',
        'promotions': 'تقرير الترقيات',
        'sanctions': 'تقرير العقوبات والمكافآت',
        'transfers': 'تقرير التنقلات والحركات',
        'comprehensive': 'التقرير الشامل'
    };
    return titles[type] || 'تقرير عام';
}

function generateEmployeesReport() {
    return `
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>رقم التسجيل</th>
                        <th>الاسم الكامل</th>
                        <th>السلك</th>
                        <th>الرتبة</th>
                        <th>المديرية</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center text-muted">سيتم ملء البيانات من قاعدة البيانات</td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

function generateLeavesReport() {
    return `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>العطل السنوية</h5>
                        <h3 class="text-primary">{{ stats.annual_leaves }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>العطل المرضية</h5>
                        <h3 class="text-warning">{{ stats.sick_leaves }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5>العطل الأخرى</h5>
                        <h3 class="text-info">{{ stats.other_leaves }}</h3>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generatePromotionsReport() {
    return `
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع الترقية</th>
                        <th>من</th>
                        <th>إلى</th>
                        <th>تاريخ السريان</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5" class="text-center text-muted">سيتم ملء البيانات من قاعدة البيانات</td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

function generateComprehensiveReport() {
    return `
        <div class="row mb-4">
            <div class="col-12">
                <h5>ملخص عام</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>الموظفين</h6>
                                <h4 class="text-primary">{{ stats.total_employees }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>العطل</h6>
                                <h4 class="text-warning">{{ stats.total_leaves }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>الترقيات</h6>
                                <h4 class="text-success">{{ stats.total_promotions }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h6>التنقلات</h6>
                                <h4 class="text-info">{{ stats.total_transfers }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function quickReport(type) {
    alert(`سيتم إنشاء تقرير سريع: ${type}`);
    generateCustomReport();
}

function exportReport() {
    const format = document.querySelector('select[name="export_format"]').value;
    alert(`سيتم تصدير التقرير بصيغة ${format}`);
}

function printReport() {
    window.print();
}

function refreshPreview() {
    generateCustomReport();
}

function closePreview() {
    document.getElementById('reportPreview').style.display = 'none';
}

// تحديد تاريخ اليوم كقيمة افتراضية
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('endDate').value = today;
    
    // تحديد بداية الشهر
    const firstDay = new Date();
    firstDay.setDate(1);
    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
});
</script>
{% endblock %}
