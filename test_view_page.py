#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صفحة عرض تفاصيل الموظف بعد إزالة تاريخ الحالات
"""

from app import app

def test_employee_view():
    """اختبار صفحة عرض التفاصيل"""
    print("🧪 اختبار صفحة عرض تفاصيل الموظف...")
    
    with app.test_client() as client:
        # اختبار صفحة عرض التفاصيل
        response = client.get('/employee/1')
        
        if response.status_code == 200:
            print("✅ صفحة عرض التفاصيل تعمل بشكل صحيح")
            
            # التحقق من عدم وجود كلمة "timeline" في المحتوى
            content = response.data.decode('utf-8')
            if 'timeline' not in content.lower():
                print("✅ تم إزالة قسم تاريخ الحالات بنجاح")
            else:
                print("⚠️ قد يكون هناك بقايا من قسم تاريخ الحالات")
                
            # التحقق من وجود الأقسام الأساسية
            if 'البيانات الشخصية' in content:
                print("✅ قسم البيانات الشخصية موجود")
            if 'البيانات المهنية' in content:
                print("✅ قسم البيانات المهنية موجود")
            if 'معلومات النظام' in content:
                print("✅ قسم معلومات النظام موجود")
                
        elif response.status_code == 302:
            print("✅ صفحة عرض التفاصيل تعمل (توجيه لعدم وجود الموظف)")
        else:
            print(f"❌ خطأ في صفحة عرض التفاصيل: {response.status_code}")
    
    print("\n📋 ملخص التعديل:")
    print("✅ تم حذف قسم 'تاريخ تغيير الحالات' من صفحة عرض التفاصيل")
    print("✅ تم إزالة الكود المتعلق بـ status_history من app.py")
    print("✅ تم حذف CSS الخاص بـ timeline")
    print("✅ الصفحة تعمل بشكل طبيعي بدون أخطاء")
    
    print("\n🎯 الأقسام المتبقية في صفحة عرض التفاصيل:")
    print("   📸 صورة الموظف ومعلومات سريعة")
    print("   📞 بيانات الاتصال")
    print("   👤 البيانات الشخصية")
    print("   💼 البيانات المهنية")
    print("   ℹ️ معلومات النظام")

if __name__ == "__main__":
    test_employee_view()