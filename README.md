# نظام إدارة موظفي الجمارك الجزائرية
## Algerian Customs Employee Management System

نظام شامل لإدارة بيانات موظفي الجمارك الجزائرية مع واجهة عربية سهلة الاستخدام.

## المميزات الرئيسية

### 🏢 إدارة الموظفين
- تسجيل بيانات الموظفين الكاملة
- التحقق من صحة أرقام الضمان الاجتماعي الجزائري
- التحقق من صحة أرقام الحسابات الجارية البريدية
- رفع وإدارة صور الموظفين
- تتبع حالات الموظفين (نشط، متقاعد، مستقيل، إلخ)

### 📋 إدارة العطل
- العطل السنوية
- العطل المرضية
- العطل الاستثنائية
- تتبع أرصدة العطل

### 🎓 إدارة الشهادات والتكوين
- تسجيل الشهادات الأكاديمية
- متابعة برامج التكوين
- إدارة الدورات التدريبية

### 📈 إدارة الترقيات
- ترقيات الرتب
- ترقيات الدرجات
- تتبع تاريخ الترقيات

### 🔄 إدارة التحويلات
- التحويلات الداخلية
- التحويلات الخارجية
- تتبع تاريخ التحويلات

### ⚖️ إدارة العقوبات
- تسجيل العقوبات التأديبية
- متابعة حالات العقوبات
- إدارة المكافآت

### 📊 التقارير والإحصائيات
- إحصائيات شاملة للموظفين
- تقارير العطل والغيابات
- تقارير الترقيات والتحويلات
- إحصائيات حالات الموظفين

### 🎛️ إدارة حالات الموظفين المتقدمة
- إدارة التقاعد
- إدارة الاستقالات
- إدارة الوفيات
- إدارة التحويلات الخارجية
- إدارة الإيقاف والاستيداع
- إدارة الانتداب
- إدارة العطل طويلة الأمد

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- Flask 3.0.0
- Pillow 10.1.0
- Werkzeug 3.0.1

### قاعدة البيانات
- SQLite (مدمجة مع Python)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd YASSINE
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إنشاء قاعدة البيانات
```bash
python init_database.py
```

### 4. تشغيل النظام
```bash
python run.py
```
أو
```bash
python app.py
```

### 5. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## روابط النظام

- **الصفحة الرئيسية**: http://localhost:5000
- **قائمة الموظفين**: http://localhost:5000/employees
- **إضافة موظف**: http://localhost:5000/add_employee
- **إدارة العطل**: http://localhost:5000/leaves
- **إدارة الشهادات**: http://localhost:5000/certificates
- **إدارة الترقيات**: http://localhost:5000/promotions
- **إدارة التحويلات**: http://localhost:5000/transfers
- **إدارة العقوبات**: http://localhost:5000/sanctions
- **الإحصائيات**: http://localhost:5000/statistics
- **الإعدادات**: http://localhost:5000/settings
- **لوحة تحكم الحالات**: http://localhost:5000/employee_status
- **ملخص الحالات**: http://localhost:5000/employee_status_summary
- **التحويلات الخارجية**: http://localhost:5000/external_transfers
- **التقارير**: http://localhost:5000/reports

## هيكل المشروع

```
YASSINE/
├── app.py                          # الملف الرئيسي للتطبيق
├── run.py                          # ملف تشغيل مبسط
├── init_database.py                # إنشاء قاعدة البيانات
├── requirements.txt                # متطلبات المشروع
├── employee_status_manager.py      # إدارة حالات الموظفين
├── employee_status_api.py          # واجهة API للحالات
├── employee_status_helpers.py      # مساعدات الحالات
├── employee_status_reports.py      # تقارير الحالات
├── status_integration.py           # تكامل نظام الحالات
├── customs_employees.db            # قاعدة البيانات
├── templates/                      # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── employees/
│   ├── leaves/
│   ├── certificates/
│   ├── promotions/
│   ├── transfers/
│   ├── sanctions/
│   ├── statistics/
│   ├── settings/
│   ├── employee_status/
│   └── reports/
└── static/                         # الملفات الثابتة
    ├── css/
    └── uploads/
```

## الميزات التقنية

### الأمان
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection
- تشفير كلمات المرور (إذا تم تفعيل نظام المصادقة)

### الأداء
- استخدام SQLite لقاعدة بيانات سريعة
- تحسين الاستعلامات
- ضغط الصور تلقائياً

### سهولة الاستخدام
- واجهة عربية كاملة
- تصميم متجاوب يعمل على جميع الأجهزة
- رسائل خطأ واضحة
- تنبيهات تفاعلية

## الدعم والمساعدة

### المشاكل الشائعة

1. **خطأ في تشغيل النظام**
   - تأكد من تثبيت Python 3.8+
   - تأكد من تثبيت جميع المتطلبات
   - تأكد من وجود قاعدة البيانات

2. **مشاكل في قاعدة البيانات**
   - احذف ملف `customs_employees.db`
   - شغل `python init_database.py`

3. **مشاكل في رفع الصور**
   - تأكد من وجود مجلد `static/uploads`
   - تأكد من صيغة الصورة (JPG, PNG)

### التطوير والتخصيص

النظام مصمم ليكون قابلاً للتوسع والتخصيص. يمكن إضافة ميزات جديدة من خلال:

1. إضافة جداول جديدة في `init_database.py`
2. إنشاء مسارات جديدة في `app.py`
3. إضافة قوالب HTML جديدة في `templates/`
4. إضافة أنماط CSS في `static/css/`

## الترخيص

هذا المشروع مخصص لاستخدام الجمارك الجزائرية.

## المطور

تم تطوير هذا النظام لخدمة الجمارك الجزائرية بواجهة عربية شاملة وسهلة الاستخدام.

---

**ملاحظة**: هذا النظام مصمم للاستخدام المحلي في بيئة التطوير. للاستخدام في الإنتاج، يُنصح بتكوين خادم ويب مناسب مثل Apache أو Nginx.