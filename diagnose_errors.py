#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص أخطاء النظام
"""

import sys
import os
import sqlite3
import subprocess

def print_header(title):
    """طباعة رأس القسم"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def check_python():
    """فحص Python"""
    print_header("فحص Python")
    print(f"✅ إصدار Python: {sys.version}")
    print(f"✅ مسار Python: {sys.executable}")

def check_flask():
    """فحص Flask"""
    print_header("فحص Flask")
    try:
        import flask
        print(f"✅ Flask متوفر - الإصدار: {flask.__version__}")
        return True
    except ImportError:
        print("❌ Flask غير مثبت")
        try:
            print("🔄 محاولة تثبيت Flask...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "flask"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ تم تثبيت Flask بنجاح")
                import flask
                print(f"✅ Flask الإصدار: {flask.__version__}")
                return True
            else:
                print(f"❌ فشل في تثبيت Flask: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تثبيت Flask: {e}")
            return False

def check_files():
    """فحص الملفات المطلوبة"""
    print_header("فحص الملفات")
    
    required_files = [
        'simple_main.py',
        'templates/base.html',
        'templates/index.html',
        'templates/employee_statuses/index.html',
        'templates/employees/index.html',
        'templates/employees/add.html'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    return missing_files

def check_database():
    """فحص قاعدة البيانات"""
    print_header("فحص قاعدة البيانات")
    
    if not os.path.exists('customs_employees.db'):
        print("⚠️  قاعدة البيانات غير موجودة - ستُنشأ عند التشغيل")
        return True
    
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        # فحص جدول الموظفين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='employees'")
        if cursor.fetchone():
            print("✅ جدول الموظفين موجود")
            
            # عدد الموظفين
            cursor.execute("SELECT COUNT(*) FROM employees")
            count = cursor.fetchone()[0]
            print(f"📊 عدد الموظفين: {count}")
        else:
            print("⚠️  جدول الموظفين غير موجود - سيُنشأ عند التشغيل")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_simple_import():
    """اختبار استيراد الوحدات"""
    print_header("اختبار الاستيراد")
    
    modules = ['flask', 'sqlite3', 'os', 'datetime']
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")

def create_missing_templates():
    """إنشاء القوالب المفقودة"""
    print_header("إنشاء القوالب المفقودة")
    
    # إنشاء مجلدات
    os.makedirs('templates/employees', exist_ok=True)
    os.makedirs('templates/leaves', exist_ok=True)
    os.makedirs('templates/certificates', exist_ok=True)
    os.makedirs('templates/sanctions', exist_ok=True)
    os.makedirs('templates/transfers', exist_ok=True)
    os.makedirs('templates/promotions', exist_ok=True)
    os.makedirs('templates/settings', exist_ok=True)
    os.makedirs('templates/reports', exist_ok=True)
    
    # قالب بسيط للموظفين
    if not os.path.exists('templates/employees/add.html'):
        with open('templates/employees/add.html', 'w', encoding='utf-8') as f:
            f.write('''{% extends "base.html" %}
{% block title %}إضافة موظف{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2>إضافة موظف جديد</h2>
    <form method="POST">
        <div class="mb-3">
            <label class="form-label">رقم التسجيل *</label>
            <input type="text" name="registration_number" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">الاسم الأول *</label>
            <input type="text" name="first_name" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">اسم العائلة *</label>
            <input type="text" name="last_name" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">إضافة</button>
        <a href="{{ url_for('employees') }}" class="btn btn-secondary">إلغاء</a>
    </form>
</div>
{% endblock %}''')
        print("✅ تم إنشاء templates/employees/add.html")
    
    # قوالب بسيطة للصفحات الأخرى
    simple_pages = [
        ('leaves/index.html', 'العطل والإجازات'),
        ('certificates/index.html', 'الشهادات والتكوين'),
        ('sanctions/index.html', 'العقوبات والمكافآت'),
        ('transfers/index.html', 'التنقلات والحركات'),
        ('promotions/index.html', 'مختلف الترقيات'),
        ('settings/index.html', 'الإعدادات'),
        ('reports/index.html', 'التقارير')
    ]
    
    for page, title in simple_pages:
        if not os.path.exists(f'templates/{page}'):
            with open(f'templates/{page}', 'w', encoding='utf-8') as f:
                content = '''{% extends "base.html" %}
{% block title %}''' + title + '''{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2>''' + title + '''</h2>
    <p>هذه الصفحة قيد التطوير...</p>
</div>
{% endblock %}'''
                f.write(content)
            print(f"✅ تم إنشاء templates/{page}")

def run_test_server():
    """تشغيل خادم اختبار"""
    print_header("تشغيل خادم اختبار")
    
    try:
        print("🚀 محاولة تشغيل simple_main.py...")
        process = subprocess.Popen([sys.executable, "simple_main.py"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # انتظار قصير
        import time
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ الخادم يعمل!")
            print("🌐 جرب فتح: http://localhost:5000")
            process.terminate()
        else:
            stdout, stderr = process.communicate()
            print("❌ فشل في تشغيل الخادم")
            if stderr:
                print(f"خطأ: {stderr}")
            if stdout:
                print(f"مخرجات: {stdout}")
                
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص أخطاء نظام إدارة موظفي الجمارك الجزائرية")
    
    # فحص Python
    check_python()
    
    # فحص Flask
    flask_ok = check_flask()
    
    # اختبار الاستيراد
    test_simple_import()
    
    # فحص الملفات
    missing_files = check_files()
    
    # فحص قاعدة البيانات
    db_ok = check_database()
    
    # إنشاء القوالب المفقودة
    if missing_files:
        create_missing_templates()
    
    # تشغيل اختبار
    if flask_ok:
        run_test_server()
    
    print_header("ملخص التشخيص")
    if flask_ok and not missing_files and db_ok:
        print("✅ جميع المتطلبات متوفرة - النظام جاهز للتشغيل")
        print("🚀 شغل: python simple_main.py")
    else:
        print("⚠️  توجد مشاكل تحتاج إصلاح")
        if not flask_ok:
            print("   - Flask غير متوفر")
        if missing_files:
            print(f"   - ملفات مفقودة: {len(missing_files)}")
        if not db_ok:
            print("   - مشاكل في قاعدة البيانات")

if __name__ == "__main__":
    main()
