#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الحالات الخاصة للموظفين
Special Employee Status Manager
"""

import sqlite3
from datetime import datetime, date
from typing import Dict, List, Optional, Any

class SpecialStatusManager:
    """مدير الحالات الخاصة للموظفين"""
    
    def __init__(self, db_path: str = 'customs_employees.db'):
        self.db_path = db_path
        
        # أنواع الحالات الخاصة
        self.status_types = {
            'resignation': 'استقالة',
            'death': 'وفاة', 
            'retirement': 'تقاعد',
            'external_transfer': 'تحويل خارجي',
            'leave_of_absence': 'استيداع',
            'suspension': 'توقيف'
        }
        
        # أنواع التقاعد
        self.retirement_types = [
            'تقاعد نسبي',
            'تقاعد تناسبي',
            'تقاعد بدون شرط السن',
            'تقاعد للعجز',
            'تقاعد اختياري'
        ]
        
        # أسباب الاستيداع
        self.leave_reasons = [
            'رعاية الأطفال',
            'الدراسة',
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        # أسباب التوقيف
        self.suspension_reasons = [
            'تحقيق إداري',
            'تحقيق قضائي',
            'مخالفة إدارية',
            'غياب غير مبرر',
            'أسباب تأديبية',
            'أسباب أخرى'
        ]
    
    def get_db_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_employee_info(self, employee_id: int) -> Optional[Dict]:
        """الحصول على معلومات الموظف"""
        conn = self.get_db_connection()
        try:
            employee = conn.execute('''
                SELECT e.*, r.name as rank_name, s.name as service_name
                FROM employees e
                LEFT JOIN ranks r ON e.current_rank_id = r.id
                LEFT JOIN services s ON e.current_service_id = s.id
                WHERE e.id = ?
            ''', (employee_id,)).fetchone()
            
            return dict(employee) if employee else None
        finally:
            conn.close()
    
    def get_all_employees(self) -> List[Dict]:
        """الحصول على قائمة جميع الموظفين النشطين"""
        conn = self.get_db_connection()
        try:
            employees = conn.execute('''
                SELECT e.id, e.registration_number, e.first_name, e.last_name,
                       r.name as rank_name, s.name as service_name
                FROM employees e
                LEFT JOIN ranks r ON e.current_rank_id = r.id
                LEFT JOIN services s ON e.current_service_id = s.id
                WHERE e.status = 'نشط'
                ORDER BY e.registration_number
            ''').fetchall()
            
            return [dict(emp) for emp in employees]
        finally:
            conn.close()
    
    # ================================
    # إدارة الاستقالات
    # ================================
    
    def add_resignation(self, data: Dict) -> bool:
        """إضافة استقالة جديدة"""
        conn = self.get_db_connection()
        try:
            conn.execute('''
                INSERT INTO employee_resignations 
                (employee_id, resignation_date, reason, decision_number, 
                 decision_date, effective_date, notes, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['employee_id'],
                data['resignation_date'],
                data.get('reason', ''),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('effective_date'),
                data.get('notes', ''),
                data.get('status', 'pending')
            ))
            
            # تحديث حالة الموظف إذا كانت الاستقالة مؤكدة
            if data.get('status') == 'approved':
                conn.execute('''
                    UPDATE employees SET status = 'مستقيل' WHERE id = ?
                ''', (data['employee_id'],))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"خطأ في إضافة الاستقالة: {e}")
            return False
        finally:
            conn.close()
    
    def get_resignations(self) -> List[Dict]:
        """الحصول على قائمة الاستقالات"""
        conn = self.get_db_connection()
        try:
            resignations = conn.execute('''
                SELECT r.*, e.registration_number, e.first_name, e.last_name,
                       rank.name as rank_name, s.name as service_name
                FROM employee_resignations r
                JOIN employees e ON r.employee_id = e.id
                LEFT JOIN ranks rank ON e.current_rank_id = rank.id
                LEFT JOIN services s ON e.current_service_id = s.id
                ORDER BY r.resignation_date DESC
            ''').fetchall()
            
            return [dict(res) for res in resignations]
        finally:
            conn.close()
    
    # ================================
    # إدارة الوفيات
    # ================================
    
    def add_death(self, data: Dict) -> bool:
        """إضافة وفاة جديدة"""
        conn = self.get_db_connection()
        try:
            conn.execute('''
                INSERT INTO employee_deaths 
                (employee_id, death_date, death_place, death_cause, 
                 certificate_number, burial_place, family_contact, 
                 decision_number, decision_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['employee_id'],
                data['death_date'],
                data.get('death_place', ''),
                data.get('death_cause', ''),
                data.get('certificate_number', ''),
                data.get('burial_place', ''),
                data.get('family_contact', ''),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'متوفى' WHERE id = ?
            ''', (data['employee_id'],))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"خطأ في إضافة الوفاة: {e}")
            return False
        finally:
            conn.close()
    
    def get_deaths(self) -> List[Dict]:
        """الحصول على قائمة الوفيات"""
        conn = self.get_db_connection()
        try:
            deaths = conn.execute('''
                SELECT d.*, e.registration_number, e.first_name, e.last_name,
                       rank.name as rank_name, s.name as service_name
                FROM employee_deaths d
                JOIN employees e ON d.employee_id = e.id
                LEFT JOIN ranks rank ON e.current_rank_id = rank.id
                LEFT JOIN services s ON e.current_service_id = s.id
                ORDER BY d.death_date DESC
            ''').fetchall()
            
            return [dict(death) for death in deaths]
        finally:
            conn.close()
    
    # ================================
    # إدارة التقاعد
    # ================================
    
    def add_retirement(self, data: Dict) -> bool:
        """إضافة تقاعد جديد"""
        conn = self.get_db_connection()
        try:
            conn.execute('''
                INSERT INTO employee_retirements 
                (employee_id, retirement_date, retirement_type, years_of_service,
                 pension_amount, decision_number, decision_date, effective_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['employee_id'],
                data['retirement_date'],
                data['retirement_type'],
                data.get('years_of_service'),
                data.get('pension_amount'),
                data.get('decision_number', ''),
                data.get('decision_date'),
                data.get('effective_date'),
                data.get('notes', '')
            ))
            
            # تحديث حالة الموظف
            conn.execute('''
                UPDATE employees SET status = 'متقاعد' WHERE id = ?
            ''', (data['employee_id'],))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"خطأ في إضافة التقاعد: {e}")
            return False
        finally:
            conn.close()
    
    def get_retirements(self) -> List[Dict]:
        """الحصول على قائمة المتقاعدين"""
        conn = self.get_db_connection()
        try:
            retirements = conn.execute('''
                SELECT r.*, e.registration_number, e.first_name, e.last_name,
                       rank.name as rank_name, s.name as service_name
                FROM employee_retirements r
                JOIN employees e ON r.employee_id = e.id
                LEFT JOIN ranks rank ON e.current_rank_id = rank.id
                LEFT JOIN services s ON e.current_service_id = s.id
                ORDER BY r.retirement_date DESC
            ''').fetchall()
            
            return [dict(ret) for ret in retirements]
        finally:
            conn.close()
    
    # ================================
    # إحصائيات الحالات الخاصة
    # ================================
    
    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات الحالات الخاصة"""
        conn = self.get_db_connection()
        try:
            stats = {}
            
            # عدد الاستقالات
            stats['resignations'] = conn.execute(
                'SELECT COUNT(*) FROM employee_resignations'
            ).fetchone()[0]
            
            # عدد الوفيات
            stats['deaths'] = conn.execute(
                'SELECT COUNT(*) FROM employee_deaths'
            ).fetchone()[0]
            
            # عدد المتقاعدين
            stats['retirements'] = conn.execute(
                'SELECT COUNT(*) FROM employee_retirements'
            ).fetchone()[0]
            
            # عدد التحويلات الخارجية
            stats['external_transfers'] = conn.execute(
                'SELECT COUNT(*) FROM employee_external_transfers'
            ).fetchone()[0] if self._table_exists('employee_external_transfers') else 0
            
            # عدد المستودعين
            stats['leave_of_absence'] = conn.execute(
                'SELECT COUNT(*) FROM employee_leave_of_absence WHERE status = "active"'
            ).fetchone()[0] if self._table_exists('employee_leave_of_absence') else 0
            
            # عدد الموقوفين
            stats['suspensions'] = conn.execute(
                'SELECT COUNT(*) FROM employee_suspensions WHERE status = "active"'
            ).fetchone()[0] if self._table_exists('employee_suspensions') else 0
            
            return stats
        finally:
            conn.close()
    
    def _table_exists(self, table_name: str) -> bool:
        """التحقق من وجود جدول"""
        conn = self.get_db_connection()
        try:
            result = conn.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            ''', (table_name,)).fetchone()
            return result is not None
        finally:
            conn.close()
