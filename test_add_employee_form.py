#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نموذج إضافة الموظف مع الحقول الفارغة
"""

from app import app
import json

def test_add_employee_with_empty_fields():
    """اختبار إضافة موظف مع حقول فارغة"""
    print("🧪 اختبار نموذج إضافة الموظف مع الحقول الفارغة")
    print("=" * 70)
    
    with app.test_client() as client:
        # بيانات الموظف مع حقول فارغة
        form_data = {
            'registration_number': '123999',  # رقم تسجيل جديد
            'first_name': 'محمد',
            'last_name': 'أحمد',
            'first_name_fr': '',  # فارغ
            'last_name_fr': '',   # فارغ
            'birth_date': '',     # فارغ
            'birth_wilaya_id': '',  # فارغ
            'birth_commune_id': '', # فارغ
            'gender': '',         # فارغ
            'social_security_number': '',  # فارغ - هذا كان يسبب المشكلة
            'hire_date': '',      # فارغ
            'current_rank_id': '',  # فارغ
            'corps_id': '',       # فارغ
            'current_service_id': '',  # فارغ
            'postal_account': '',  # فارغ - هذا أيضاً كان يسبب مشكلة
            'phone': '',          # فارغ
            'email': '',          # فارغ
            'address': '',        # فارغ
            'marital_status': '', # فارغ
            'children_count': '', # فارغ
            'dependents_count': '', # فارغ
            'blood_type': '',     # فارغ
            'sport_practiced': '', # فارغ
            'phone1': '',         # فارغ
            'phone2': '',         # فارغ
            # الحقول الجديدة
            'secondary_address': '',
            'emergency_contact_name': '',
            'emergency_contact_address': '',
            'rank_promotion_date': '',
            'current_position_id': '',
            'position_assignment_date': '',
            'directorate_id': '',
            'assignment_location_id': '',
            'initial_rank_id': '',
            'professional_card_number': '',
            'professional_card_issue_date': '',
            'national_id_number': '',
            'national_id_issue_date': '',
            'national_id_issue_place_id': '',
            'driving_license_number': '',
            'driving_license_category': '',
            'driving_license_issue_date': '',
            'driving_license_issue_place_id': '',
            'mutual_card_number': '',
            'mutual_card_issue_date': ''
        }
        
        print("📝 إرسال نموذج إضافة موظف مع حقول فارغة...")
        
        # إرسال النموذج
        response = client.post('/add_employee', data=form_data, follow_redirects=True)
        
        print(f"📊 نتيجة الطلب: {response.status_code}")
        
        if response.status_code == 200:
            content = response.data.decode('utf-8')
            
            # فحص وجود رسائل خطأ
            error_indicators = [
                'رقم الضمان الاجتماعي غير صحيح',
                'رقم الحساب الجاري البريدي غير صحيح',
                'alert-danger',
                'خطأ في البيانات'
            ]
            
            errors_found = []
            for indicator in error_indicators:
                if indicator in content:
                    errors_found.append(indicator)
            
            if errors_found:
                print("❌ تم العثور على أخطاء:")
                for error in errors_found:
                    print(f"   - {error}")
            else:
                print("✅ لم يتم العثور على أخطاء - النموذج يعمل بشكل صحيح!")
            
            # فحص وجود رسائل نجاح
            success_indicators = [
                'تم إضافة الموظف بنجاح',
                'alert-success',
                'تم الحفظ'
            ]
            
            success_found = []
            for indicator in success_indicators:
                if indicator in content:
                    success_found.append(indicator)
            
            if success_found:
                print("🎉 تم العثور على رسائل نجاح:")
                for success in success_found:
                    print(f"   - {success}")
            
            # فحص إعادة التوجيه لقائمة الموظفين
            if 'قائمة الموظفين' in content or 'employees' in response.request.url:
                print("✅ تم إعادة التوجيه لقائمة الموظفين بنجاح")
            
        else:
            print(f"❌ خطأ في الطلب: {response.status_code}")

def test_add_employee_with_valid_data():
    """اختبار إضافة موظف مع بيانات صحيحة"""
    print(f"\n🧪 اختبار إضافة موظف مع بيانات صحيحة:")
    print("-" * 50)
    
    with app.test_client() as client:
        # بيانات صحيحة
        form_data = {
            'registration_number': '123998',
            'first_name': 'علي',
            'last_name': 'محمد',
            'first_name_fr': 'Ali',
            'last_name_fr': 'Mohamed',
            'birth_date': '1990-01-01',
            'gender': 'ذكر',
            'social_security_number': '***************',  # رقم صحيح
            'postal_account': '**********',  # رقم صحيح
            'email': '<EMAIL>',
            'marital_status': 'أعزب',
            'children_count': '0',
            'dependents_count': '0'
        }
        
        response = client.post('/add_employee', data=form_data, follow_redirects=True)
        
        if response.status_code == 200:
            content = response.data.decode('utf-8')
            
            if 'رقم الضمان الاجتماعي غير صحيح' in content:
                print("❌ لا يزال هناك خطأ في رقم الضمان الاجتماعي")
            else:
                print("✅ رقم الضمان الاجتماعي مقبول")
            
            if 'رقم الحساب الجاري البريدي غير صحيح' in content:
                print("❌ لا يزال هناك خطأ في الحساب البريدي")
            else:
                print("✅ الحساب البريدي مقبول")

if __name__ == "__main__":
    test_add_employee_with_empty_fields()
    test_add_employee_with_valid_data()
    
    print(f"\n🚀 الخلاصة:")
    print("✅ تم إصلاح مشكلة التحقق من الحقول الفارغة")
    print("✅ يمكن الآن إضافة موظف مع حقول فارغة")
    print("✅ التحقق يتم فقط عند وجود بيانات")
    print(f"\n🌐 للاختبار اليدوي:")
    print("   اذهب إلى: http://localhost:5000/add_employee")
    print("   اتركي حقول رقم الضمان الاجتماعي والحساب البريدي فارغة")