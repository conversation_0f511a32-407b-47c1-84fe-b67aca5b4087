#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة موظفي الجمارك الجزائرية
"""

import subprocess
import sys
import os
import time

def check_flask():
    """التحقق من Flask وتثبيته إذا لزم الأمر"""
    try:
        import flask
        print("✅ Flask متوفر")
        return True
    except ImportError:
        print("⚠️  Flask غير مثبت. جاري التثبيت...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "flask"])
            print("✅ تم تثبيت Flask بنجاح")
            return True
        except:
            print("❌ فشل في تثبيت Flask")
            return False

def main():
    """الدالة الرئيسية"""
    print("\n" + "="*60)
    print("🏛️ نظام إدارة موظفي الجمارك الجزائرية")
    print("="*60)
    
    # التحقق من Flask
    if not check_flask():
        print("❌ لا يمكن تشغيل النظام بدون Flask")
        input("اضغط Enter للخروج...")
        return
    
    # اختيار الملف للتشغيل
    print("\n📋 اختر نسخة النظام:")
    print("1️⃣  النسخة المبسطة (simple_main.py) - موصى بها")
    print("2️⃣  النسخة الكاملة (main.py)")
    print("3️⃣  صفحة HTML فقط (بدون خادم)")
    
    choice = input("\nاختر رقماً (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 تشغيل النسخة المبسطة...")
        try:
            subprocess.run([sys.executable, "simple_main.py"])
        except KeyboardInterrupt:
            print("\n⏹️  تم إيقاف النظام")
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    elif choice == "2":
        print("\n🚀 تشغيل النسخة الكاملة...")
        try:
            subprocess.run([sys.executable, "main.py"])
        except KeyboardInterrupt:
            print("\n⏹️  تم إيقاف النظام")
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    elif choice == "3":
        print("\n🌐 فتح صفحة HTML...")
        try:
            if os.path.exists("status_final.html"):
                if os.name == 'nt':  # Windows
                    os.startfile("status_final.html")
                else:  # Mac/Linux
                    subprocess.run(["open", "status_final.html"])
                print("✅ تم فتح الصفحة في المتصفح")
            else:
                print("❌ ملف status_final.html غير موجود")
        except Exception as e:
            print(f"❌ خطأ في فتح الصفحة: {e}")
    
    else:
        print("❌ خيار غير صحيح")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
