@echo off
cls
echo.
echo ==========================================
echo     نظام إدارة موظفي الجمارك الجزائرية
echo              تشخيص الأخطاء
echo ==========================================
echo.

echo 🔍 فحص Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير متوفر أو غير مثبت بشكل صحيح
    echo 💡 تأكد من تثبيت Python وإضافته لمتغير PATH
    pause
    exit /b 1
)

echo.
echo 🔍 فحص Flask...
python -c "import flask; print('✅ Flask متوفر - الإصدار:', flask.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Flask غير مثبت
    echo 🔄 محاولة تثبيت Flask...
    python -m pip install flask
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت Flask بنجاح
)

echo.
echo 🔍 فحص الملفات المطلوبة...
if not exist "simple_main.py" (
    echo ❌ simple_main.py غير موجود
    pause
    exit /b 1
)
echo ✅ simple_main.py موجود

if not exist "templates\base.html" (
    echo ❌ templates\base.html غير موجود
    pause
    exit /b 1
)
echo ✅ templates\base.html موجود

if not exist "templates\index.html" (
    echo ❌ templates\index.html غير موجود
    pause
    exit /b 1
)
echo ✅ templates\index.html موجود

if not exist "templates\employee_statuses\index.html" (
    echo ❌ templates\employee_statuses\index.html غير موجود
    pause
    exit /b 1
)
echo ✅ templates\employee_statuses\index.html موجود

echo.
echo 🔍 فحص قاعدة البيانات...
if exist "customs_employees.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ⚠️  قاعدة البيانات غير موجودة - ستُنشأ عند التشغيل
)

echo.
echo ==========================================
echo ✅ جميع المتطلبات متوفرة
echo 🚀 تشغيل النظام...
echo ==========================================
echo.

echo 🌐 سيتم فتح النظام على: http://localhost:5000
echo 📊 صفحة حالات الموظفين: http://localhost:5000/employee_statuses
echo 👥 إدارة الموظفين: http://localhost:5000/employees
echo.
echo ⏹️  اضغط Ctrl+C لإيقاف الخادم
echo.

python simple_main.py

echo.
echo ==========================================
echo 📝 ملاحظات:
echo    - إذا ظهرت أخطاء، تأكد من تثبيت Flask
echo    - تأكد من وجود جميع ملفات templates
echo    - تحقق من أن المنفذ 5000 غير مستخدم
echo ==========================================
pause
