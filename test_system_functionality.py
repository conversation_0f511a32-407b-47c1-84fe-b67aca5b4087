#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف النظام الكامل
Test Complete System Functionality
"""

import requests
import json
from datetime import datetime

def test_system_functionality():
    """اختبار وظائف النظام الكامل"""
    print("🧪 اختبار وظائف النظام الكامل")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # 1. اختبار الصفحة الرئيسية
    print("1️⃣  اختبار الصفحة الرئيسية...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("   ✅ الصفحة الرئيسية تعمل")
        else:
            print(f"   ❌ خطأ في الصفحة الرئيسية: {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 2. اختبار قائمة الموظفين
    print("\n2️⃣  اختبار قائمة الموظفين...")
    try:
        response = requests.get(f"{base_url}/employees")
        if response.status_code == 200:
            print("   ✅ قائمة الموظفين تعمل")
            # التحقق من وجود أزرار الحالات
            if "الحالات" in response.text:
                print("   ✅ أزرار الحالات موجودة")
            else:
                print("   ⚠️  أزرار الحالات غير ظاهرة")
        else:
            print(f"   ❌ خطأ في قائمة الموظفين: {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 3. اختبار لوحة الحالات
    print("\n3️⃣  اختبار لوحة الحالات...")
    try:
        response = requests.get(f"{base_url}/status/")
        if response.status_code == 200:
            print("   ✅ لوحة الحالات تعمل")
        else:
            print(f"   ❌ خطأ في لوحة الحالات: {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 4. اختبار API الإحصائيات
    print("\n4️⃣  اختبار API الإحصائيات...")
    try:
        response = requests.get(f"{base_url}/status/api/statistics")
        if response.status_code == 200:
            stats = response.json()
            print("   ✅ API الإحصائيات يعمل")
            print(f"   📊 النشطين: {stats.get('active', 0)}")
            print(f"   📊 المستودعين: {stats.get('leave_of_absence', 0)}")
            print(f"   📊 المتوفين: {stats.get('deceased', 0)}")
            print(f"   📊 المحولين خارجياً: {stats.get('external_transfer', 0)}")
        else:
            print(f"   ❌ خطأ في API الإحصائيات: {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 5. اختبار قوائم الحالات النهائية
    print("\n5️⃣  اختبار قوائم الحالات النهائية...")
    
    final_status_pages = [
        ("/status/deceased", "المتوفين"),
        ("/final_status/external_transfers", "المحولين خارجياً"),
        ("/final_status/dismissed", "المعزولين"),
        ("/final_status/retired", "المتقاعدين")
    ]
    
    for url, name in final_status_pages:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                print(f"   ✅ قائمة {name} تعمل")
            else:
                print(f"   ❌ خطأ في قائمة {name}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ خطأ في قائمة {name}: {e}")
    
    # 6. اختبار نماذج الإضافة
    print("\n6️⃣  اختبار نماذج الإضافة...")
    
    # الحصول على أول موظف للاختبار
    try:
        import sqlite3
        conn = sqlite3.connect('customs_employees.db')
        employee = conn.execute('SELECT id FROM employees LIMIT 1').fetchone()
        conn.close()
        
        if employee:
            employee_id = employee[0]
            
            form_pages = [
                (f"/status/leave_of_absence/add/{employee_id}", "نموذج الاستيداع"),
                (f"/status/death/add/{employee_id}", "نموذج الوفاة"),
                (f"/final_status/external_transfer/add/{employee_id}", "نموذج التحويل الخارجي"),
                (f"/final_status/dismissal/add/{employee_id}", "نموذج العزل"),
                (f"/final_status/retirement/add/{employee_id}", "نموذج التقاعد")
            ]
            
            for url, name in form_pages:
                try:
                    response = requests.get(f"{base_url}{url}")
                    if response.status_code == 200:
                        print(f"   ✅ {name} يعمل")
                    else:
                        print(f"   ❌ خطأ في {name}: {response.status_code}")
                except Exception as e:
                    print(f"   ❌ خطأ في {name}: {e}")
        else:
            print("   ⚠️  لا يوجد موظفين لاختبار النماذج")
            
    except Exception as e:
        print(f"   ❌ خطأ في الوصول لقاعدة البيانات: {e}")
    
    print(f"\n🎉 انتهى اختبار النظام!")
    print(f"📋 النظام جاهز للاستخدام على: {base_url}")

def show_system_summary():
    """عرض ملخص النظام"""
    print(f"\n🌟 ملخص النظام الكامل")
    print("=" * 60)
    
    print("✅ الميزات المكتملة:")
    print("   🔴 الحالات النهائية (تُحذف من جدول الموظفين):")
    print("      - الوفاة → جدول deceased_employees")
    print("      - التحويل الخارجي → جدول external_transfers")
    print("      - العزل → جدول dismissed_employees")
    print("      - التقاعد → جدول retired_employees")
    
    print("   🟢 الحالات المؤقتة (تبقى في جدول الموظفين):")
    print("      - الاستيداع (مع حساب الرصيد)")
    print("      - التوقيف")
    print("      - الاستقالة")
    print("      - الخدمة الوطنية")
    print("      - العطلة الطويلة")
    print("      - الانتداب")
    print("      - الدراسة/التكوين")
    
    print("   🎯 الميزات التقنية:")
    print("      - حفظ البيانات الكاملة في JSON")
    print("      - عدم احتساب المحذوفين في العدد")
    print("      - إمكانية الوصول للمعلومات من جداول الحالات")
    print("      - تتبع تاريخ التغييرات")
    print("      - API شامل للبيانات")
    
    print(f"\n🌐 الروابط المهمة:")
    print(f"   - الصفحة الرئيسية: http://localhost:5000/")
    print(f"   - قائمة الموظفين: http://localhost:5000/employees")
    print(f"   - لوحة الحالات: http://localhost:5000/status/")
    print(f"   - المتوفين: http://localhost:5000/status/deceased")
    print(f"   - المحولين خارجياً: http://localhost:5000/final_status/external_transfers")
    print(f"   - المعزولين: http://localhost:5000/final_status/dismissed")
    print(f"   - المتقاعدين: http://localhost:5000/final_status/retired")

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار النظام الكامل لإدارة حالات الموظفين")
    print("=" * 80)
    
    # اختبار الوظائف
    test_system_functionality()
    
    # عرض الملخص
    show_system_summary()
    
    print(f"\n🎯 النظام مكتمل 100% وجاهز للاستخدام!")
    print(f"📞 للدعم: راجع ملف COMPLETE_SYSTEM_SUMMARY.md")

if __name__ == "__main__":
    main()