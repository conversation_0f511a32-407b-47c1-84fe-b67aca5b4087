#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وإصلاح النظام
"""

import sqlite3
import os
from datetime import datetime

def create_basic_tables():
    """إنشاء الجداول الأساسية"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        print("🔧 إنشاء الجداول الأساسية...")
        
        # جدول أسباب الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reason TEXT NOT NULL UNIQUE,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج أسباب افتراضية
        default_reasons = [
            'رعاية الأطفال',
            'الدراسة', 
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        for reason in default_reasons:
            cursor.execute('''
                INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
        
        # جدول الاستيداع المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_leave_of_absence_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                period_number INTEGER NOT NULL,
                duration_months INTEGER NOT NULL,
                reason TEXT NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول الاستقالات المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_resignations_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                request_date DATE NOT NULL,
                reason TEXT,
                is_approved BOOLEAN,
                approval_decision_number TEXT,
                approval_decision_date DATE,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء الجداول الأساسية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def create_simple_manager():
    """إنشاء مدير بسيط للحالات الخاصة"""
    manager_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الحالات الخاصة البسيط
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class SimpleSpecialStatusManager:
    """مدير الحالات الخاصة البسيط"""
    
    def __init__(self, db_path: str = 'customs_employees.db'):
        self.db_path = db_path
        self.MAX_LEAVE_OF_ABSENCE_YEARS = 5
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_leave_reasons(self) -> List:
        """الحصول على قائمة أسباب الاستيداع"""
        conn = self.get_db_connection()
        try:
            reasons = conn.execute('''
                SELECT * FROM leave_of_absence_reasons 
                WHERE is_active = 1 
                ORDER BY reason
            ''').fetchall()
            return reasons
        except:
            return []
        finally:
            conn.close()
    
    def add_leave_reason(self, reason: str) -> bool:
        """إضافة سبب استيداع جديد"""
        try:
            conn = self.get_db_connection()
            conn.execute('''
                INSERT INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
            conn.commit()
            conn.close()
            return True
        except:
            return False
    
    def get_employee_leave_balance(self, employee_id: int) -> Dict:
        """حساب رصيد الاستيداع للموظف"""
        conn = self.get_db_connection()
        
        try:
            total_months = conn.execute('''
                SELECT COALESCE(SUM(duration_months), 0) 
                FROM employee_leave_of_absence_updated 
                WHERE employee_id = ?
            ''', (employee_id,)).fetchone()[0]
        except:
            total_months = 0
        
        max_months = self.MAX_LEAVE_OF_ABSENCE_YEARS * 12
        remaining_months = max_months - total_months
        
        conn.close()
        
        return {
            'total_used_months': total_months,
            'total_used_years': total_months / 12,
            'remaining_months': remaining_months,
            'remaining_years': remaining_months / 12,
            'max_allowed_years': self.MAX_LEAVE_OF_ABSENCE_YEARS
        }
    
    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات"""
        conn = self.get_db_connection()
        stats = {}
        
        try:
            stats['active_leave_of_absence'] = conn.execute(
                'SELECT COUNT(*) FROM employee_leave_of_absence_updated WHERE status = "active"'
            ).fetchone()[0]
        except:
            stats['active_leave_of_absence'] = 0
        
        try:
            stats['pending_resignations'] = conn.execute(
                'SELECT COUNT(*) FROM employee_resignations_updated WHERE status = "pending"'
            ).fetchone()[0]
        except:
            stats['pending_resignations'] = 0
        
        # إضافة إحصائيات افتراضية
        stats.update({
            'active_suspensions': 0,
            'total_deaths': 0,
            'total_external_transfers': 0,
            'total_dismissals': 0,
            'total_retirements': 0
        })
        
        conn.close()
        return stats
'''
    
    try:
        with open('simple_special_status_manager.py', 'w', encoding='utf-8') as f:
            f.write(manager_code)
        print("✅ تم إنشاء المدير البسيط")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء المدير: {e}")
        return False

def update_routes():
    """تحديث ملف المسارات"""
    try:
        # قراءة الملف الحالي
        with open('special_status_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استبدال الاستيراد
        content = content.replace(
            'from updated_special_status_manager import UpdatedSpecialStatusManager',
            'from simple_special_status_manager import SimpleSpecialStatusManager'
        )
        
        content = content.replace(
            'updated_status_manager = UpdatedSpecialStatusManager()',
            'simple_manager = SimpleSpecialStatusManager()'
        )
        
        # استبدال استخدام المدير
        content = content.replace('updated_status_manager.', 'simple_manager.')
        
        # كتابة الملف المحدث
        with open('special_status_routes.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث ملف المسارات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث المسارات: {e}")
        return False

def test_system():
    """اختبار النظام"""
    try:
        print("\n🧪 اختبار النظام...")
        
        # اختبار الاستيراد
        from simple_special_status_manager import SimpleSpecialStatusManager
        manager = SimpleSpecialStatusManager()
        
        print("✅ تم استيراد المدير بنجاح")
        
        # اختبار الوظائف
        reasons = manager.get_leave_reasons()
        print(f"✅ تم الحصول على {len(reasons)} سبب للاستيداع")
        
        stats = manager.get_statistics()
        print(f"✅ تم الحصول على الإحصائيات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح وتشغيل النظام")
    print("=" * 50)
    
    # إنشاء الجداول الأساسية
    if not create_basic_tables():
        print("❌ فشل في إنشاء الجداول")
        return
    
    # إنشاء المدير البسيط
    if not create_simple_manager():
        print("❌ فشل في إنشاء المدير")
        return
    
    # تحديث المسارات
    if not update_routes():
        print("❌ فشل في تحديث المسارات")
        return
    
    # اختبار النظام
    if not test_system():
        print("❌ فشل في اختبار النظام")
        return
    
    print("\n🎉 تم إصلاح النظام بنجاح!")
    print("🚀 يمكنك الآن تشغيل التطبيق بـ: python run.py")

if __name__ == '__main__':
    main()
