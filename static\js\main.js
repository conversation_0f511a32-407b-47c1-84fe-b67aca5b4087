/**
 * نظام إدارة موظفي الجمارك الجزائرية
 * ملف JavaScript الرئيسي
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة القوائم المنسدلة
    initializeDropdowns();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الجداول
    initializeTables();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    console.log('تم تحميل نظام إدارة موظفي الجمارك الجزائرية');
    
    // إضافة تأثيرات التحميل
    addLoadingEffects();
    
    // تهيئة التنبيهات
    initializeAlerts();
}

/**
 * تهيئة القوائم المنسدلة
 */
function initializeDropdowns() {
    // تفعيل القوائم المنسدلة في Bootstrap
    const dropdownElements = document.querySelectorAll('.dropdown-toggle');
    dropdownElements.forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const dropdownMenu = this.nextElementSibling;
            if (dropdownMenu) {
                dropdownMenu.classList.toggle('show');
            }
        });
    });
    
    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.matches('.dropdown-toggle')) {
            const dropdowns = document.querySelectorAll('.dropdown-menu');
            dropdowns.forEach(function(dropdown) {
                dropdown.classList.remove('show');
            });
        }
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // تهيئة نماذج إضافة الموظفين
    initializeEmployeeForms();
    
    // تهيئة نماذج العطل
    initializeLeaveForms();
    
    // تهيئة نماذج الشهادات
    initializeCertificateForms();
    
    // تهيئة نماذج حالات الموظفين
    initializeStatusForms();
}

/**
 * تهيئة نماذج الموظفين
 */
function initializeEmployeeForms() {
    const employeeForm = document.getElementById('addEmployeeForm');
    if (employeeForm) {
        // حساب العمر تلقائياً من تاريخ الميلاد
        const birthDateInput = employeeForm.querySelector('input[name="birth_date"]');
        if (birthDateInput) {
            birthDateInput.addEventListener('change', function() {
                calculateAge(this.value);
            });
        }
        
        // التحقق من صحة رقم التسجيل
        const registrationInput = employeeForm.querySelector('input[name="registration_number"]');
        if (registrationInput) {
            registrationInput.addEventListener('input', function() {
                validateRegistrationNumber(this.value);
            });
        }
    }
}

/**
 * تهيئة نماذج العطل
 */
function initializeLeaveForms() {
    const leaveForm = document.getElementById('addLeaveForm');
    if (leaveForm) {
        const startDateInput = leaveForm.querySelector('input[name="start_date"]');
        const endDateInput = leaveForm.querySelector('input[name="end_date"]');
        const daysCountInput = leaveForm.querySelector('input[name="days_count"]');
        
        if (startDateInput && endDateInput && daysCountInput) {
            function calculateLeaveDays() {
                if (startDateInput.value && endDateInput.value) {
                    const startDate = new Date(startDateInput.value);
                    const endDate = new Date(endDateInput.value);
                    const timeDiff = endDate.getTime() - startDate.getTime();
                    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
                    daysCountInput.value = daysDiff > 0 ? daysDiff : 0;
                }
            }
            
            startDateInput.addEventListener('change', calculateLeaveDays);
            endDateInput.addEventListener('change', calculateLeaveDays);
        }
    }
}

/**
 * تهيئة نماذج الشهادات
 */
function initializeCertificateForms() {
    // إضافة التحقق من صحة البيانات للشهادات
    const certificateForm = document.getElementById('addCertificateForm');
    if (certificateForm) {
        certificateForm.addEventListener('submit', function(e) {
            e.preventDefault();
            validateCertificateForm(this);
        });
    }
    
    const trainingForm = document.getElementById('addTrainingForm');
    if (trainingForm) {
        trainingForm.addEventListener('submit', function(e) {
            e.preventDefault();
            validateTrainingForm(this);
        });
    }
}

/**
 * تهيئة نماذج حالات الموظفين
 */
function initializeStatusForms() {
    const statusForm = document.getElementById('addStatusForm');
    if (statusForm) {
        const statusTypeSelect = statusForm.querySelector('select[name="status_type"]');
        if (statusTypeSelect) {
            statusTypeSelect.addEventListener('change', function() {
                handleStatusTypeChange(this.value);
            });
        }
    }
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // إضافة وظائف البحث والتصفية للجداول
    const searchInputs = document.querySelectorAll('input[type="search"]');
    searchInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            filterTable(this.value, this.closest('.card').querySelector('table'));
        });
    });
    
    // إضافة وظائف الترتيب للجداول
    const tableHeaders = document.querySelectorAll('th[data-sort]');
    tableHeaders.forEach(function(header) {
        header.addEventListener('click', function() {
            sortTable(this.dataset.sort, this.closest('table'));
        });
    });
}

/**
 * حساب العمر من تاريخ الميلاد
 */
function calculateAge(birthDate) {
    if (!birthDate) return;
    
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }
    
    // عرض العمر في حقل منفصل إذا كان موجوداً
    const ageDisplay = document.querySelector('.age-display');
    if (ageDisplay) {
        ageDisplay.textContent = age + ' سنة';
    }
}

/**
 * التحقق من صحة رقم التسجيل
 */
function validateRegistrationNumber(regNumber) {
    const isValid = /^\d{6}$/.test(regNumber);
    const input = document.querySelector('input[name="registration_number"]');
    
    if (input) {
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        }
    }
    
    return isValid;
}

/**
 * معالجة تغيير نوع حالة الموظف
 */
function handleStatusTypeChange(statusType) {
    // إضافة حقول إضافية حسب نوع الحالة
    const additionalFields = document.querySelector('.additional-fields');
    if (additionalFields) {
        additionalFields.innerHTML = '';
        
        if (statusType === 'national_service') {
            // إضافة حقول الخدمة الوطنية
            additionalFields.innerHTML = `
                <div class="col-md-6 mb-3">
                    <label class="form-label">الوضعية</label>
                    <select class="form-select" name="service_status">
                        <option value="completed">مكتملة</option>
                        <option value="deferred">مؤجلة</option>
                        <option value="exempted">معفى</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">تاريخ انتهاء التأجيل</label>
                    <input type="date" class="form-control" name="deferment_end_date">
                </div>
            `;
        }
    }
}

/**
 * تصفية الجداول
 */
function filterTable(searchTerm, table) {
    if (!table) return;
    
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(function(row) {
        const text = row.textContent.toLowerCase();
        if (text.includes(term)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * ترتيب الجداول
 */
function sortTable(column, table) {
    // تنفيذ ترتيب الجدول حسب العمود المحدد
    console.log('ترتيب الجدول حسب:', column);
}

/**
 * إضافة تأثيرات التحميل
 */
function addLoadingEffects() {
    // إضافة تأثيرات التحميل للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            if (this.type === 'submit') {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                this.disabled = true;
            }
        });
    });
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إغلاق التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * عرض رسالة نجاح
 */
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
}

/**
 * عرض رسالة خطأ
 */
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
}

/**
 * التحقق من صحة نموذج الشهادة
 */
function validateCertificateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (isValid) {
        showSuccessMessage('تم حفظ الشهادة بنجاح');
        form.reset();
        // إغلاق النافذة المنبثقة
        const modal = form.closest('.modal');
        if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
    } else {
        showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
    }
}

/**
 * التحقق من صحة نموذج التدريب
 */
function validateTrainingForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (isValid) {
        showSuccessMessage('تم حفظ التدريب بنجاح');
        form.reset();
        // إغلاق النافذة المنبثقة
        const modal = form.closest('.modal');
        if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
    } else {
        showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
    }
}
