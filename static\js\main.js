/**
 * نظام إدارة موظفي الجمارك الجزائرية - النسخة الجديدة
 * ملف JavaScript الرئيسي
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - النسخة الجديدة');
    console.log('✅ تم حل مشكلة special_status.index نهائياً');
    
    // تهيئة التطبيق
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إضافة تأثيرات التحميل
    addLoadingEffects();
    
    // تهيئة التنبيهات
    initializeAlerts();
    
    // تهيئة القوائم المنسدلة
    initializeDropdowns();
}

/**
 * إضافة تأثيرات التحميل
 */
function addLoadingEffects() {
    // إضافة تأثيرات التحميل للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            if (this.type === 'submit') {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                this.disabled = true;
            }
        });
    });
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إغلاق التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * تهيئة القوائم المنسدلة
 */
function initializeDropdowns() {
    // تفعيل القوائم المنسدلة في Bootstrap
    const dropdownElements = document.querySelectorAll('.dropdown-toggle');
    dropdownElements.forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const dropdownMenu = this.nextElementSibling;
            if (dropdownMenu) {
                dropdownMenu.classList.toggle('show');
            }
        });
    });
    
    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.matches('.dropdown-toggle')) {
            const dropdowns = document.querySelectorAll('.dropdown-menu');
            dropdowns.forEach(function(dropdown) {
                dropdown.classList.remove('show');
            });
        }
    });
}

/**
 * عرض رسالة نجاح
 */
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
}

/**
 * عرض رسالة خطأ
 */
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
}
