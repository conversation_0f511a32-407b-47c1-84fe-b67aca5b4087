#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import subprocess
import sys

def test_server():
    """اختبار الخادم"""
    print("🧪 اختبار الخادم...")
    
    try:
        # محاولة الاتصال بالخادم
        import urllib.request
        
        # اختبار الصفحة الرئيسية
        try:
            response = urllib.request.urlopen('http://localhost:5000/', timeout=5)
            if response.getcode() == 200:
                print("✅ الصفحة الرئيسية تعمل")
            else:
                print(f"❌ خطأ في الصفحة الرئيسية: {response.getcode()}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال بالصفحة الرئيسية: {e}")
        
        # اختبار صفحة الحالات الخاصة
        try:
            response = urllib.request.urlopen('http://localhost:5000/special_status/', timeout=5)
            if response.getcode() == 200:
                print("✅ صفحة الحالات الخاصة تعمل")
            else:
                print(f"❌ خطأ في صفحة الحالات الخاصة: {response.getcode()}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال بصفحة الحالات الخاصة: {e}")
        
        # اختبار صفحة إعدادات الاستيداع
        try:
            response = urllib.request.urlopen('http://localhost:5000/special_status/leave_reasons_settings', timeout=5)
            if response.getcode() == 200:
                print("✅ صفحة إعدادات الاستيداع تعمل")
            else:
                print(f"❌ خطأ في صفحة الإعدادات: {response.getcode()}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال بصفحة الإعدادات: {e}")
        
        # اختبار صفحة إضافة الاستيداع
        try:
            response = urllib.request.urlopen('http://localhost:5000/special_status/leave_of_absence/add', timeout=5)
            if response.getcode() == 200:
                print("✅ صفحة إضافة الاستيداع تعمل")
            else:
                print(f"❌ خطأ في صفحة إضافة الاستيداع: {response.getcode()}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال بصفحة إضافة الاستيداع: {e}")
        
        print("\n🎉 انتهى الاختبار!")
        print("🌐 الروابط المتاحة:")
        print("   - الصفحة الرئيسية: http://localhost:5000/")
        print("   - الحالات الخاصة: http://localhost:5000/special_status/")
        print("   - إعدادات الاستيداع: http://localhost:5000/special_status/leave_reasons_settings")
        print("   - إضافة استيداع: http://localhost:5000/special_status/leave_of_absence/add")
        
    except ImportError:
        print("❌ مكتبة urllib غير متاحة")
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")

if __name__ == '__main__':
    # انتظار قليل للتأكد من تشغيل الخادم
    print("⏳ انتظار تشغيل الخادم...")
    time.sleep(2)
    
    test_server()
