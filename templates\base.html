<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة موظفي الجمارك الجزائرية{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- خط عربي جميل -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS خاص بحالات الموظفين -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/employee-status.css') }}">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }
        
        .sidebar-header {
            padding: 1.5rem 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-header h4 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .sidebar-header p {
            margin: 0.5rem 0 0 0;
            font-size: 0.85rem;
            opacity: 0.8;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s;
            border-radius: 0.5rem;
            margin: 0.25rem 1rem;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }
        
        .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .nav-link i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 0.75rem 0.75rem 0 0;
            padding: 1rem 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        
        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
            padding: 0.75rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background-color: #ffffff;
            transform: translateY(-1px);
        }
        
        /* تحسين القوائم المنسدلة - نقل السهم إلى اليسار */
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: left 0.75rem center;
            background-size: 16px 12px;
            padding-left: 2.5rem;
            padding-right: 0.75rem;
        }
        
        /* تحسين الخط في القوائم */
        .form-select option {
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            padding: 0.5rem;
            background-color: #f8f9fa;
            color: #495057;
        }
        
        .form-select option:hover {
            background-color: #e9ecef;
        }
        
        .form-select option:checked {
            background-color: #667eea;
            color: white;
        }
        
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .table thead th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            margin: 0;
            font-weight: 700;
        }
        
        .stats-card p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .mobile-toggle {
                display: block !important;
            }
        }
        
        .mobile-toggle {
            display: none;
        }
        
        .employee-photo {
            width: 120px;
            height: 150px;
            object-fit: cover;
            border-radius: 0.5rem;
            border: 2px solid #dee2e6;
        }
        
        .photo-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .photo-upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .required {
            color: #dc3545;
        }
        
        /* تحسين التسميات والنصوص */
        .form-label {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .form-text {
            font-family: 'Cairo', sans-serif;
            font-weight: 400;
            color: #6c757d;
            font-size: 0.8rem;
        }
        
        /* تحسين النصوص العامة */
        body, .card-body, .card-header {
            font-family: 'Cairo', sans-serif;
        }
        
        .card-header h5 {
            font-weight: 600;
            color: white;
        }
        
        /* تحسين الأزرار */
        .btn {
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* الشريط العلوي */
        .top-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0.75rem 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 280px; /* ترك مساحة للشريط الجانبي */
            z-index: 999; /* أقل من الشريط الجانبي */
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-title {
            display: flex;
            align-items: center;
        }

        .title-text {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .header-datetime {
            text-align: right;
        }

        .current-date, .current-time {
            display: block;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .current-date {
            font-weight: 500;
        }

        .current-time {
            font-weight: 300;
            opacity: 0.9;
        }

        /* تعديل المحتوى الرئيسي */
        .main-content {
            padding-top: 80px;
        }

        @media (max-width: 768px) {
            .top-header {
                right: 0; /* ملء العرض على الهواتف */
            }

            .title-text {
                font-size: 1rem;
            }

            .header-datetime {
                text-align: center;
                margin-top: 0.5rem;
            }

            .current-date, .current-time {
                display: inline-block;
                margin-right: 1rem;
                font-size: 0.8rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="top-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8 col-12">
                    <div class="header-title">
                        <span class="title-text">تسيير مستخدمي الجمارك الجزائرية</span>
                    </div>
                </div>
                <div class="col-md-4 col-12">
                    <div class="header-datetime">
                        <div class="current-date">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <span id="currentDate"></span>
                        </div>
                        <div class="current-time">
                            <i class="fas fa-clock me-2"></i>
                            <span id="currentTime"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-building me-2"></i>الجمارك الجزائرية</h4>
            <p>نظام إدارة الموظفين</p>
        </div>
        
        <nav>
            <a href="{{ url_for('index') }}" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}">
                <i class="fas fa-home"></i>
                لوحة التحكم
            </a>

            <a href="{{ url_for('employees') }}" class="nav-link {% if 'employees' in request.endpoint %}active{% endif %}">
                <i class="fas fa-users"></i>
                الموظفين
            </a>
            

            <a href="{{ url_for('leaves') }}" class="nav-link {% if 'leaves' in request.endpoint %}active{% endif %}">
                <i class="fas fa-calendar-alt"></i>
                العطل والإجازات
            </a>
            <a href="{{ url_for('certificates') }}" class="nav-link {% if 'certificates' in request.endpoint %}active{% endif %}">
                <i class="fas fa-graduation-cap"></i>
                الشهادات والتكوين
            </a>
            <a href="{{ url_for('sanctions') }}" class="nav-link {% if 'sanctions' in request.endpoint %}active{% endif %}">
                <i class="fas fa-gavel"></i>
                العقوبات والمكافآت
            </a>
            <a href="{{ url_for('transfers') }}" class="nav-link {% if 'transfers' in request.endpoint %}active{% endif %}">
                <i class="fas fa-exchange-alt"></i>
                التنقلات والحركات
            </a>
            <a href="{{ url_for('promotions') }}" class="nav-link {% if 'promotions' in request.endpoint %}active{% endif %}">
                <i class="fas fa-arrow-up"></i>
                الترقيات
            </a>

            <a href="{{ url_for('special_status.index') }}" class="nav-link {% if 'special_status' in request.endpoint %}active{% endif %}">
                <i class="fas fa-user-times"></i>
                الحالات الخاصة
            </a>

            <a href="{{ url_for('statistics') }}" class="nav-link {% if 'statistics' in request.endpoint %}active{% endif %}">
                <i class="fas fa-chart-bar"></i>
                الإحصائيات والتقارير
            </a>
            <a href="{{ url_for('settings') }}" class="nav-link {% if 'settings' in request.endpoint %}active{% endif %}">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
        </nav>
    </div>
    
    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- زر القائمة للهواتف -->
        <button class="btn btn-primary mobile-toggle mb-3" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- عنوان الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>{% block page_title %}لوحة التحكم{% endblock %}</h1>
        </div>
        
        <!-- رسائل التنبيه -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- محتوى الصفحة -->
        {% block content %}
        <div class="card">
            <div class="card-header">
                <h3>مرحباً بك</h3>
            </div>
            <div class="card-body">
                <p>مرحباً بك في نظام إدارة موظفي الجمارك الجزائرية</p>
            </div>
        </div>
        {% endblock %}
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث التاريخ والساعة
        function updateDateTime() {
            const now = new Date();

            // تحديث التاريخ
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                calendar: 'gregory'
            };
            const arabicDate = now.toLocaleDateString('ar-DZ', dateOptions);
            document.getElementById('currentDate').textContent = arabicDate;

            // تحديث الساعة
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };
            const arabicTime = now.toLocaleTimeString('ar-DZ', timeOptions);
            document.getElementById('currentTime').textContent = arabicTime;
        }

        // تحديث فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDateTime();
            // تحديث كل ثانية
            setInterval(updateDateTime, 1000);
        });

        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }
        
        // إغلاق الشريط الجانبي عند النقر خارجه على الهواتف
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const toggle = document.querySelector('.mobile-toggle');
                
                if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // تحديث قائمة البلديات عند تغيير الولاية
        function updateCommunes(wilayaSelect, communeSelect) {
            const wilayaId = wilayaSelect.value;
            
            // مسح البلديات الحالية
            communeSelect.innerHTML = '<option value="">اختر البلدية</option>';
            
            if (wilayaId) {
                fetch(`/api/communes/${wilayaId}`)
                    .then(response => response.json())
                    .then(communes => {
                        communes.forEach(commune => {
                            const option = document.createElement('option');
                            option.value = commune.id;
                            option.textContent = commune.name;
                            communeSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('خطأ في جلب البلديات:', error));
            }
        }
        
        // تحديث الرتب عند تغيير السلك
        function updateRanks(corpsSelect, rankSelect) {
            const corpsId = corpsSelect.value;
            
            // مسح الرتب الحالية
            rankSelect.innerHTML = '<option value="">اختر الرتبة</option>';
            
            if (corpsId) {
                fetch(`/api/ranks/${corpsId}`)
                    .then(response => response.json())
                    .then(ranks => {
                        ranks.forEach(rank => {
                            const option = document.createElement('option');
                            option.value = rank.id;
                            option.textContent = rank.name;
                            rankSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('خطأ في جلب الرتب:', error));
            }
        }
        
        // إخفاء/إظهار عدد الأطفال حسب الحالة العائلية
        function toggleChildrenCount() {
            const maritalStatus = document.getElementById('marital_status');
            const childrenGroup = document.getElementById('children_group');
            
            if (maritalStatus && childrenGroup) {
                if (maritalStatus.value === 'أعزب') {
                    childrenGroup.style.display = 'none';
                    document.getElementById('children_count').value = 0;
                } else {
                    childrenGroup.style.display = 'block';
                }
            }
        }
        
        // معاينة الصورة قبل الرفع
        function previewPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const preview = document.getElementById('photo_preview');
                    if (preview) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    }
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
