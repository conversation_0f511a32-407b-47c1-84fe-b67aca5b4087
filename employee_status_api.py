#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة برمجة التطبيقات لوحدة إدارة حالات الموظفين
API interface for Employee Status Management System
"""

from flask import Blueprint, jsonify, request, make_response
from datetime import datetime, timedelta
import json
from employee_status_manager import create_status_manager
from employee_status_reports import create_reports_manager
from employee_status_helpers import create_status_helpers

# إنشاء Blueprint للـ API
api_bp = Blueprint('employee_status_api', __name__, url_prefix='/api')

def create_api_response(data=None, message="", success=True, status_code=200):
    """إنشاء استجابة API موحدة"""
    response = {
        'success': success,
        'message': message,
        'data': data,
        'timestamp': datetime.now().isoformat()
    }
    return jsonify(response), status_code

@api_bp.route('/employee_status/statistics', methods=['GET'])
def get_status_statistics():
    """الحصول على إحصائيات الحالات"""
    try:
        manager = create_status_manager()
        stats = manager.get_status_statistics()
        
        return create_api_response(
            data=stats,
            message="تم الحصول على الإحصائيات بنجاح"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في الحصول على الإحصائيات: {str(e)}",
            status_code=500
        )

@api_bp.route('/employee_status/<int:employee_id>', methods=['GET'])
def get_employee_status(employee_id):
    """الحصول على حالة موظف معين"""
    try:
        manager = create_status_manager()
        
        # الحصول على معلومات الموظف
        employee = manager.get_employee_by_id(employee_id)
        if not employee:
            return create_api_response(
                success=False,
                message="الموظف غير موجود",
                status_code=404
            )
        
        # الحصول على تاريخ الحالات
        history = manager.get_employee_status_history(employee_id)
        
        # الحصول على الحالة الحالية
        current_status = manager.get_current_employee_status(employee_id)
        
        data = {
            'employee': dict(employee),
            'current_status': current_status,
            'history': history
        }
        
        return create_api_response(
            data=data,
            message="تم الحصول على بيانات الموظف بنجاح"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في الحصول على بيانات الموظف: {str(e)}",
            status_code=500
        )

@api_bp.route('/employees/by_status/<status>', methods=['GET'])
def get_employees_by_status(status):
    """الحصول على الموظفين حسب الحالة"""
    try:
        manager = create_status_manager()
        employees = manager.get_employees_by_status(status)
        
        # إضافة معلومات إضافية
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # تطبيق التصفح
        start = (page - 1) * per_page
        end = start + per_page
        paginated_employees = employees[start:end]
        
        data = {
            'employees': [dict(emp) for emp in paginated_employees],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': len(employees),
                'pages': (len(employees) + per_page - 1) // per_page
            },
            'status': status
        }
        
        return create_api_response(
            data=data,
            message=f"تم الحصول على {len(paginated_employees)} موظف بحالة '{status}'"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في الحصول على الموظفين: {str(e)}",
            status_code=500
        )

@api_bp.route('/employee_status/quick_stats', methods=['GET'])
def get_quick_stats():
    """الحصول على إحصائيات سريعة"""
    try:
        manager = create_status_manager()
        
        # الحصول على الإحصائيات السريعة
        stats = {
            'active_leaves': manager.get_active_status_count('long_term_leave'),
            'pending_resignations': manager.get_active_status_count('resignation', 'معلقة'),
            'active_suspensions': manager.get_active_status_count('suspension'),
            'active_assignments': manager.get_active_status_count('assignment'),
            'last_updated': datetime.now().isoformat()
        }
        
        return create_api_response(
            data=stats,
            message="تم الحصول على الإحصائيات السريعة بنجاح"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في الحصول على الإحصائيات السريعة: {str(e)}",
            status_code=500
        )

@api_bp.route('/employee_status/upcoming_events', methods=['GET'])
def get_upcoming_events():
    """الحصول على الأحداث القادمة"""
    try:
        days_ahead = request.args.get('days', 30, type=int)
        
        helpers = create_status_helpers()
        events = helpers.get_upcoming_events(days_ahead)
        
        return create_api_response(
            data={
                'events': events,
                'days_ahead': days_ahead,
                'count': len(events)
            },
            message=f"تم الحصول على {len(events)} حدث قادم"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في الحصول على الأحداث القادمة: {str(e)}",
            status_code=500
        )

@api_bp.route('/employee_status/retirement_eligibility/<int:employee_id>', methods=['GET'])
def check_retirement_eligibility(employee_id):
    """فحص أهلية التقاعد"""
    try:
        helpers = create_status_helpers()
        eligibility = helpers.get_retirement_eligibility(employee_id)
        
        return create_api_response(
            data=eligibility,
            message="تم فحص أهلية التقاعد بنجاح"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في فحص أهلية التقاعد: {str(e)}",
            status_code=500
        )

@api_bp.route('/employee_status/validate_transition', methods=['POST'])
def validate_status_transition():
    """التحقق من صحة انتقال الحالة"""
    try:
        data = request.get_json()
        employee_id = data.get('employee_id')
        from_status = data.get('from_status')
        to_status = data.get('to_status')
        
        if not all([employee_id, from_status, to_status]):
            return create_api_response(
                success=False,
                message="بيانات غير مكتملة",
                status_code=400
            )
        
        helpers = create_status_helpers()
        is_valid, message = helpers.validate_status_transition(employee_id, from_status, to_status)
        
        return create_api_response(
            data={
                'valid': is_valid,
                'validation_message': message,
                'employee_id': employee_id,
                'transition': f"{from_status} -> {to_status}"
            },
            message="تم التحقق من انتقال الحالة"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في التحقق من انتقال الحالة: {str(e)}",
            status_code=500
        )

@api_bp.route('/reports/monthly', methods=['GET'])
def get_monthly_report():
    """الحصول على التقرير الشهري"""
    try:
        year = request.args.get('year', datetime.now().year, type=int)
        month = request.args.get('month', datetime.now().month, type=int)
        
        reports = create_reports_manager()
        report = reports.generate_monthly_report(year, month)
        
        return create_api_response(
            data=report,
            message=f"تم إنشاء التقرير الشهري لـ {month}/{year}"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في إنشاء التقرير الشهري: {str(e)}",
            status_code=500
        )

@api_bp.route('/reports/annual', methods=['GET'])
def get_annual_report():
    """الحصول على التقرير السنوي"""
    try:
        year = request.args.get('year', datetime.now().year, type=int)
        
        reports = create_reports_manager()
        report = reports.generate_annual_report(year)
        
        return create_api_response(
            data=report,
            message=f"تم إنشاء التقرير السنوي لـ {year}"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في إنشاء التقرير السنوي: {str(e)}",
            status_code=500
        )

@api_bp.route('/export_status_data', methods=['GET'])
def export_status_data():
    """تصدير بيانات الحالات"""
    try:
        status_type = request.args.get('type', 'long_term_leaves')
        format_type = request.args.get('format', 'json')
        
        helpers = create_status_helpers()
        data = helpers.export_status_data(status_type, format_type)
        
        # إنشاء الاستجابة
        response = make_response(data)
        
        if format_type == 'csv':
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename="{status_type}_{datetime.now().strftime("%Y%m%d")}.csv"'
        elif format_type == 'json':
            response.headers['Content-Type'] = 'application/json; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename="{status_type}_{datetime.now().strftime("%Y%m%d")}.json"'
        
        return response
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في تصدير البيانات: {str(e)}",
            status_code=500
        )

@api_bp.route('/export_monthly_report', methods=['GET'])
def export_monthly_report():
    """تصدير التقرير الشهري"""
    try:
        year = request.args.get('year', datetime.now().year, type=int)
        month = request.args.get('month', datetime.now().month, type=int)
        format_type = request.args.get('format', 'csv')
        
        reports = create_reports_manager()
        report = reports.generate_monthly_report(year, month)
        
        if format_type == 'csv':
            data = reports.export_report_to_csv(report, 'monthly')
            response = make_response(data)
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename="monthly_report_{year}_{month:02d}.csv"'
        else:
            data = json.dumps(report, ensure_ascii=False, indent=2, default=str)
            response = make_response(data)
            response.headers['Content-Type'] = 'application/json; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename="monthly_report_{year}_{month:02d}.json"'
        
        return response
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في تصدير التقرير الشهري: {str(e)}",
            status_code=500
        )

@api_bp.route('/employee_status/search', methods=['GET'])
def search_employee_status():
    """البحث في حالات الموظفين"""
    try:
        query = request.args.get('q', '')
        status_type = request.args.get('status_type', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        
        manager = create_status_manager()
        results = manager.search_employee_status(
            query=query,
            status_type=status_type,
            date_from=date_from,
            date_to=date_to
        )
        
        return create_api_response(
            data={
                'results': results,
                'count': len(results),
                'search_params': {
                    'query': query,
                    'status_type': status_type,
                    'date_from': date_from,
                    'date_to': date_to
                }
            },
            message=f"تم العثور على {len(results)} نتيجة"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في البحث: {str(e)}",
            status_code=500
        )

@api_bp.route('/employee_status/bulk_update', methods=['POST'])
def bulk_update_status():
    """تحديث حالات متعددة"""
    try:
        data = request.get_json()
        updates = data.get('updates', [])
        
        if not updates:
            return create_api_response(
                success=False,
                message="لا توجد تحديثات للمعالجة",
                status_code=400
            )
        
        manager = create_status_manager()
        results = []
        
        for update in updates:
            try:
                employee_id = update.get('employee_id')
                action = update.get('action')
                params = update.get('params', {})
                
                if action == 'end_status':
                    success, message = manager.end_employee_status(
                        employee_id, 
                        update.get('status_type'),
                        params
                    )
                else:
                    success, message = False, f"إجراء غير مدعوم: {action}"
                
                results.append({
                    'employee_id': employee_id,
                    'action': action,
                    'success': success,
                    'message': message
                })
            except Exception as e:
                results.append({
                    'employee_id': update.get('employee_id'),
                    'action': update.get('action'),
                    'success': False,
                    'message': str(e)
                })
        
        successful_updates = len([r for r in results if r['success']])
        
        return create_api_response(
            data={
                'results': results,
                'summary': {
                    'total': len(results),
                    'successful': successful_updates,
                    'failed': len(results) - successful_updates
                }
            },
            message=f"تم معالجة {successful_updates} من {len(results)} تحديث بنجاح"
        )
    except Exception as e:
        return create_api_response(
            success=False,
            message=f"خطأ في التحديث المجمع: {str(e)}",
            status_code=500
        )

# معالج الأخطاء
@api_bp.errorhandler(404)
def api_not_found(error):
    return create_api_response(
        success=False,
        message="المسار غير موجود",
        status_code=404
    )

@api_bp.errorhandler(405)
def method_not_allowed(error):
    return create_api_response(
        success=False,
        message="الطريقة غير مسموحة",
        status_code=405
    )

@api_bp.errorhandler(500)
def internal_error(error):
    return create_api_response(
        success=False,
        message="خطأ داخلي في الخادم",
        status_code=500
    )

def register_api_routes(app):
    """تسجيل مسارات API"""
    app.register_blueprint(api_bp)
    
    # إضافة CORS headers
    @app.after_request
    def after_request(response):
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

if __name__ == "__main__":
    # اختبار API
    from flask import Flask
    
    app = Flask(__name__)
    register_api_routes(app)
    
    print("🔌 اختبار واجهة برمجة التطبيقات...")
    print("المسارات المتاحة:")
    
    for rule in app.url_map.iter_rules():
        if rule.endpoint.startswith('employee_status_api'):
            print(f"  {rule.methods} {rule.rule}")
    
    print("✅ تم تسجيل جميع مسارات API بنجاح")