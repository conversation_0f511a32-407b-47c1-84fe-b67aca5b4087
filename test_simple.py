#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتأكد من أن المسارات موجودة
"""

from app import app

def test_routes():
    """اختبار المسارات"""
    print("🧪 اختبار مسارات الموظفين...")
    
    with app.test_client() as client:
        # اختبار صفحة قائمة الموظفين
        response = client.get('/employees')
        print(f"📋 قائمة الموظفين: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
        
        # اختبار صفحة إضافة موظف
        response = client.get('/add_employee')
        print(f"➕ إضافة موظف: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
        
        # اختبار API الحالات
        response = client.get('/api/employee_statuses')
        print(f"🔗 API الحالات: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
        
        # اختبار API البلديات (مع ولاية وهمية)
        response = client.get('/api/communes/1')
        print(f"🏘️ API البلديات: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
        
        print("\n📋 ملخص الإصلاحات:")
        print("✅ تم إضافة مسار تعديل الموظف: /employees/<id>/edit")
        print("✅ تم إضافة مسار حذف الموظف: /employees/<id>/delete")
        print("✅ تم إضافة مسار طباعة الموظف: /employees/<id>/print")
        print("✅ تم إنشاء قالب الطباعة")
        print("✅ تم إضافة API للحالات والبلديات")
        print("✅ تم تصحيح مراجع JavaScript")
        
        print("\n🎯 الإجراءات المتاحة الآن في صفحة الموظفين:")
        print("   👁️  عرض التفاصيل - يوجه إلى /employee/<id>")
        print("   ✏️  تعديل البيانات - يوجه إلى /employees/<id>/edit")
        print("   🖨️  طباعة البيانات - يفتح /employees/<id>/print في نافذة جديدة")
        print("   🗑️  حذف الموظف - يرسل POST إلى /employees/<id>/delete")

if __name__ == "__main__":
    test_routes()