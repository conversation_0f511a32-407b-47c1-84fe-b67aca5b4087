#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template_string

app = Flask(__name__)

# قالب HTML مبسط
INDEX_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة موظفي الجمارك الجزائرية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; }
        .hero-section { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 100px 0; 
            text-align: center;
        }
        .hexagon-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 30px; 
            padding: 60px 0; 
        }
        .hexagon-item { 
            text-align: center; 
            cursor: pointer; 
            transition: transform 0.3s; 
        }
        .hexagon-item:hover { transform: translateY(-5px); }
        .hexagon { 
            width: 100px; 
            height: 100px; 
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 50%; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            margin: 0 auto 20px; 
            color: white; 
            font-size: 32px;
        }
    </style>
</head>
<body>
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4">نظام إدارة موظفي الجمارك الجزائرية</h1>
            <p class="lead">منصة إلكترونية شاملة لإدارة شؤون الموظفين</p>
        </div>
    </section>

    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2>الخدمات المتاحة</h2>
                    <p>اختر الخدمة التي تريد الوصول إليها</p>
                </div>
            </div>
            
            <div class="hexagon-grid">
                <div class="hexagon-item" onclick="location.href='/employees'">
                    <div class="hexagon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5>إدارة الموظفين</h5>
                    <p>إضافة وتعديل بيانات الموظفين</p>
                </div>
                
                <div class="hexagon-item" onclick="location.href='/leaves'">
                    <div class="hexagon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h5>العطل والإجازات</h5>
                    <p>إدارة العطل السنوية والمرضية</p>
                </div>
                
                <div class="hexagon-item" onclick="location.href='/certificates'">
                    <div class="hexagon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h5>الشهادات والتكوين</h5>
                    <p>إدارة الشهادات والدورات التدريبية</p>
                </div>
                
                <div class="hexagon-item" onclick="location.href='/employee_statuses'">
                    <div class="hexagon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h5>حالات الموظفين</h5>
                    <p>إدارة حالات الموظفين المختلفة</p>
                </div>
                
                <div class="hexagon-item" onclick="location.href='/settings'">
                    <div class="hexagon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h5>الإعدادات</h5>
                    <p>إعدادات النظام والمستخدمين</p>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
'''

SIMPLE_PAGE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - نظام إدارة موظفي الجمارك الجزائرية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .page-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 30px; 
            border-radius: 15px; 
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="page-header">
            <h1><i class="{{ icon }}"></i> {{ title }}</h1>
            <p>{{ description }}</p>
        </div>
        
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="{{ icon }} fa-5x text-primary mb-4"></i>
                <h3 class="mb-3">{{ title }}</h3>
                <p class="text-muted mb-4">هذه الصفحة جاهزة للتطوير</p>
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i> العودة للرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(INDEX_TEMPLATE)

@app.route('/employees')
def employees():
    return render_template_string(SIMPLE_PAGE_TEMPLATE, 
                                title="إدارة الموظفين", 
                                icon="fas fa-users",
                                description="إدارة شاملة لبيانات موظفي الجمارك الجزائرية")

@app.route('/leaves')
def leaves():
    return render_template_string(SIMPLE_PAGE_TEMPLATE, 
                                title="العطل والإجازات", 
                                icon="fas fa-calendar-alt",
                                description="إدارة العطل السنوية والمرضية وأنواع الإجازات الأخرى")

@app.route('/certificates')
def certificates():
    return render_template_string(SIMPLE_PAGE_TEMPLATE, 
                                title="الشهادات والتكوين", 
                                icon="fas fa-graduation-cap",
                                description="إدارة الشهادات الأكاديمية والدورات التدريبية للموظفين")

@app.route('/employee_statuses')
def employee_statuses():
    return render_template_string(SIMPLE_PAGE_TEMPLATE, 
                                title="حالات الموظفين", 
                                icon="fas fa-user-check",
                                description="إدارة حالات الموظفين المختلفة (استيداع، وفاة، انتداب، تحويل، خدمة وطنية، توقيف)")

@app.route('/settings')
def settings():
    return render_template_string(SIMPLE_PAGE_TEMPLATE, 
                                title="الإعدادات", 
                                icon="fas fa-cog",
                                description="إعدادات النظام والبيانات المرجعية")

if __name__ == '__main__':
    print("🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - نسخة جديدة")
    print("🌐 http://localhost:5000")
    print("✅ النظام يعمل بدون أي مراجع قديمة")
    app.run(debug=True, host='0.0.0.0', port=5000)
