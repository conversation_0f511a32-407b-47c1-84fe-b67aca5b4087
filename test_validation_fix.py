#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة التحقق من رقم الضمان الاجتماعي والحساب الجاري البريدي
"""

from app import validate_social_security_number, validate_postal_account

def test_validation_functions():
    """اختبار دوال التحقق"""
    print("🧪 اختبار دوال التحقق من البيانات")
    print("=" * 60)
    
    # اختبار رقم الضمان الاجتماعي
    print("🔍 اختبار رقم الضمان الاجتماعي:")
    print("-" * 40)
    
    test_cases_ssn = [
        # (رقم الضمان، سنة الميلاد، الجنس، النتيجة المتوقعة، الوصف)
        ('', None, None, True, 'حقل فارغ (يجب أن يكون صحيح)'),
        ('   ', None, None, True, 'حقل بمسافات فقط (يجب أن يكون صحيح)'),
        (None, None, None, True, 'حقل None (يجب أن يكون صحيح)'),
        ('***************', 1985, 'ذكر', False, 'رقم صحيح الطول لكن مفتاح خاطئ'),
        ('***************', 1985, 'ذكر', True, 'رقم صحيح كامل'),
        ('***************', 1985, 'أنثى', True, 'رقم صحيح لأنثى'),
        ('18505*********1', 1985, 'ذكر', False, 'مفتاح خاطئ'),
        ('28505*********0', 1985, 'ذكر', False, 'جنس خاطئ'),
        ('1850512345678', None, None, False, 'رقم قصير'),
        ('***************12', None, None, False, 'رقم طويل'),
        ('18505*********a', None, None, False, 'يحتوي على حروف')
    ]
    
    for ssn, birth_year, gender, expected, description in test_cases_ssn:
        result = validate_social_security_number(ssn, birth_year, gender)
        status = "✅" if result == expected else "❌"
        print(f"{status} {description}")
        if result != expected:
            print(f"   المتوقع: {expected}, الفعلي: {result}")
    
    print(f"\n🔍 اختبار رقم الحساب الجاري البريدي:")
    print("-" * 40)
    
    test_cases_postal = [
        # (رقم الحساب، النتيجة المتوقعة، الوصف)
        ('', True, 'حقل فارغ (يجب أن يكون صحيح)'),
        ('   ', True, 'حقل بمسافات فقط (يجب أن يكون صحيح)'),
        (None, True, 'حقل None (يجب أن يكون صحيح)'),
        ('*********0', False, 'رقم صحيح الطول لكن مفتاح خاطئ'),
        ('**********', True, 'رقم صحيح مع مفتاح صحيح'),
        ('*********', False, 'رقم قصير'),
        ('***********', False, 'رقم طويل'),
        ('*********a', False, 'يحتوي على حروف'),
    ]
    
    for account, expected, description in test_cases_postal:
        result = validate_postal_account(account)
        status = "✅" if result == expected else "❌"
        print(f"{status} {description}")
        if result != expected:
            print(f"   المتوقع: {expected}, الفعلي: {result}")
    
    print(f"\n🎯 ملخص الاختبار:")
    print("✅ الحقول الفارغة الآن تُعتبر صحيحة (اختيارية)")
    print("✅ التحقق يتم فقط عند وجود بيانات")
    print("✅ لا توجد أخطاء عند ترك الحقول فارغة")

def test_edge_cases():
    """اختبار الحالات الحدية"""
    print(f"\n🔬 اختبار الحالات الحدية:")
    print("-" * 40)
    
    # حالات خاصة لرقم الضمان الاجتماعي
    edge_cases = [
        # بدون سنة ميلاد أو جنس
        ('***************', None, None, True, 'رقم صحيح بدون سنة ميلاد أو جنس'),
        ('***************', 1985, None, True, 'رقم صحيح بدون جنس'),
        ('***************', None, 'ذكر', True, 'رقم صحيح بدون سنة ميلاد'),
        
        # سنوات مختلفة
        ('***************', 2085, 'ذكر', False, 'سنة ميلاد مختلفة'),
        ('***************', 1885, 'ذكر', True, 'سنة ميلاد 1885 (85 = 85)'),
    ]
    
    for ssn, birth_year, gender, expected, description in edge_cases:
        result = validate_social_security_number(ssn, birth_year, gender)
        status = "✅" if result == expected else "❌"
        print(f"{status} {description}")
        if result != expected:
            print(f"   المتوقع: {expected}, الفعلي: {result}")

if __name__ == "__main__":
    test_validation_functions()
    test_edge_cases()
    
    print(f"\n🚀 الآن يمكنك إضافة موظف جديد بحقول فارغة دون أخطاء!")
    print(f"🌐 اذهب إلى: http://localhost:5000/employees/add")