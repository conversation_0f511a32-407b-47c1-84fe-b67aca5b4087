#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة القيد الفريد على رقم الضمان الاجتماعي
"""

import sqlite3
import shutil
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"customs_employees_backup_{timestamp}.db"
    
    try:
        shutil.copy2('customs_employees.db', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def recreate_table_without_unique_ssn():
    """إعادة إنشاء الجدول بدون القيد الفريد على رقم الضمان الاجتماعي"""
    print("🔧 إعادة إنشاء الجدول بدون القيد الفريد على رقم الضمان الاجتماعي")
    print("=" * 80)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    try:
        # 1. إنشاء جدول جديد بدون القيد الفريد على social_security_number
        print("📋 إنشاء جدول جديد...")
        cursor.execute('''
            CREATE TABLE employees_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                registration_number TEXT UNIQUE NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                birth_wilaya_id INTEGER,
                birth_commune_id INTEGER,
                gender TEXT CHECK (gender IN ('ذكر', 'أنثى')),
                social_security_number TEXT,  -- إزالة UNIQUE من هنا
                hire_date DATE,
                current_rank_id INTEGER,
                corps_id INTEGER,
                current_service_id INTEGER,
                postal_account TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                photo TEXT,
                status TEXT DEFAULT 'نشط',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                first_name_fr TEXT,
                last_name_fr TEXT,
                marital_status TEXT,
                children_count INTEGER DEFAULT 0,
                dependents_count INTEGER DEFAULT 0,
                blood_type TEXT,
                sport_practiced TEXT,
                phone1 TEXT,
                phone2 TEXT,
                secondary_address TEXT,
                emergency_contact_name TEXT,
                emergency_contact_address TEXT,
                rank_promotion_date DATE,
                current_position_id INTEGER,
                position_assignment_date DATE,
                directorate_id INTEGER,
                assignment_location_id INTEGER,
                initial_rank_id INTEGER,
                professional_card_number TEXT,
                professional_card_issue_date DATE,
                national_id_number TEXT,
                national_id_issue_date DATE,
                national_id_issue_place_id INTEGER,
                driving_license_number TEXT,
                driving_license_category TEXT,
                driving_license_issue_date DATE,
                driving_license_issue_place_id INTEGER,
                mutual_card_number TEXT,
                mutual_card_issue_date DATE,
                FOREIGN KEY (birth_wilaya_id) REFERENCES wilayas (id),
                FOREIGN KEY (birth_commune_id) REFERENCES communes (id),
                FOREIGN KEY (current_rank_id) REFERENCES ranks (id),
                FOREIGN KEY (corps_id) REFERENCES corps (id),
                FOREIGN KEY (current_service_id) REFERENCES services (id)
            )
        ''')
        print("✅ تم إنشاء الجدول الجديد")
        
        # 2. نسخ البيانات من الجدول القديم إلى الجديد
        print("📊 نسخ البيانات...")
        cursor.execute('''
            INSERT INTO employees_new SELECT * FROM employees
        ''')
        print("✅ تم نسخ البيانات")
        
        # 3. حذف الجدول القديم
        print("🗑️  حذف الجدول القديم...")
        cursor.execute('DROP TABLE employees')
        print("✅ تم حذف الجدول القديم")
        
        # 4. إعادة تسمية الجدول الجديد
        print("🔄 إعادة تسمية الجدول الجديد...")
        cursor.execute('ALTER TABLE employees_new RENAME TO employees')
        print("✅ تم إعادة تسمية الجدول")
        
        # 5. إنشاء فهرس فريد على رقم الضمان الاجتماعي للقيم غير الفارغة فقط
        print("🔑 إنشاء فهرس فريد للقيم غير الفارغة...")
        cursor.execute('''
            CREATE UNIQUE INDEX idx_unique_ssn 
            ON employees(social_security_number) 
            WHERE social_security_number IS NOT NULL AND social_security_number != ''
        ''')
        print("✅ تم إنشاء الفهرس الفريد الشرطي")
        
        conn.commit()
        print("🎉 تم إصلاح المشكلة بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def test_fixed_database():
    """اختبار قاعدة البيانات المصلحة"""
    print(f"\n🧪 اختبار قاعدة البيانات المصلحة")
    print("-" * 50)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    try:
        # اختبار 1: إضافة موظف برقم ضمان فارغ
        print("1️⃣  اختبار إضافة موظف برقم ضمان فارغ...")
        cursor.execute('''
            INSERT INTO employees (registration_number, first_name, last_name, social_security_number)
            VALUES (?, ?, ?, ?)
        ''', ('999991', 'اختبار', 'فارغ1', ''))
        print("✅ نجح")
        
        # اختبار 2: إضافة موظف آخر برقم ضمان فارغ
        print("2️⃣  اختبار إضافة موظف آخر برقم ضمان فارغ...")
        cursor.execute('''
            INSERT INTO employees (registration_number, first_name, last_name, social_security_number)
            VALUES (?, ?, ?, ?)
        ''', ('999992', 'اختبار', 'فارغ2', ''))
        print("✅ نجح")
        
        # اختبار 3: إضافة موظف برقم ضمان صحيح
        print("3️⃣  اختبار إضافة موظف برقم ضمان صحيح...")
        cursor.execute('''
            INSERT INTO employees (registration_number, first_name, last_name, social_security_number)
            VALUES (?, ?, ?, ?)
        ''', ('999993', 'اختبار', 'صحيح', '190011234567835'))
        print("✅ نجح")
        
        # اختبار 4: محاولة إضافة موظف برقم ضمان مكرر (يجب أن يفشل)
        print("4️⃣  اختبار إضافة موظف برقم ضمان مكرر (يجب أن يفشل)...")
        try:
            cursor.execute('''
                INSERT INTO employees (registration_number, first_name, last_name, social_security_number)
                VALUES (?, ?, ?, ?)
            ''', ('999994', 'اختبار', 'مكرر', '190011234567835'))
            print("❌ لم يفشل كما متوقع!")
        except sqlite3.IntegrityError:
            print("✅ فشل كما متوقع (رقم ضمان مكرر)")
        
        conn.commit()
        
        # عرض النتائج
        cursor.execute('''
            SELECT registration_number, first_name, last_name, social_security_number
            FROM employees 
            WHERE registration_number LIKE '9999%'
            ORDER BY registration_number
        ''')
        test_employees = cursor.fetchall()
        
        print(f"\n📊 الموظفون التجريبيون المضافون:")
        for emp in test_employees:
            ssn_display = emp[3] if emp[3] else "فارغ"
            print(f"  {emp[0]} - {emp[1]} {emp[2]} - رقم الضمان: '{ssn_display}'")
        
        # حذف البيانات التجريبية
        cursor.execute("DELETE FROM employees WHERE registration_number LIKE '9999%'")
        conn.commit()
        print("🗑️  تم حذف البيانات التجريبية")
        
        print("🎉 جميع الاختبارات نجحت!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        conn.rollback()
    finally:
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 إصلاح مشكلة القيد الفريد على رقم الضمان الاجتماعي")
    print("=" * 80)
    
    # إنشاء نسخة احتياطية
    backup_file = backup_database()
    if not backup_file:
        print("❌ لا يمكن المتابعة بدون نسخة احتياطية")
        return
    
    # إصلاح قاعدة البيانات
    if recreate_table_without_unique_ssn():
        # اختبار الإصلاح
        test_fixed_database()
        
        print(f"\n🎯 الخلاصة:")
        print("✅ تم إصلاح مشكلة القيد الفريد")
        print("✅ يمكن الآن إضافة موظفين برقم ضمان فارغ")
        print("✅ لا يزال هناك حماية من تكرار الأرقام الصحيحة")
        print(f"✅ النسخة الاحتياطية: {backup_file}")
        
        print(f"\n🌐 للاختبار:")
        print("  اذهب إلى: http://localhost:5000/add_employee")
        print("  اترك رقم الضمان الاجتماعي فارغاً")
        print("  يجب أن يعمل بدون أخطاء الآن!")
    else:
        print("❌ فشل في إصلاح قاعدة البيانات")
        print(f"💡 يمكنك استعادة النسخة الاحتياطية: {backup_file}")

if __name__ == "__main__":
    main()