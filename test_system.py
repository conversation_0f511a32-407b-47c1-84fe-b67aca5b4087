import os
import sys

print("=== تشخيص النظام ===")

# فحص Python
print(f"Python: {sys.version}")

# فحص Flask
try:
    import flask
    print(f"Flask: {flask.__version__}")
except:
    print("Flask: غير مثبت")

# فحص الملفات
files = ['simple_main.py', 'templates/base.html', 'templates/index.html']
for f in files:
    if os.path.exists(f):
        print(f"✅ {f}")
    else:
        print(f"❌ {f}")

# فحص قاعدة البيانات
if os.path.exists('customs_employees.db'):
    print("✅ قاعدة البيانات موجودة")
else:
    print("⚠️ قاعدة البيانات غير موجودة")

print("=== انتهى التشخيص ===")
