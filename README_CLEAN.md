# 🏛️ نظام إدارة موظفي الجمارك الجزائرية

نظام شامل لإدارة بيانات الموظفين في الجمارك الجزائرية مع دعم كامل للغة العربية.

## ✨ المميزات

- 👥 **إدارة الموظفين**: إضافة وتعديل وعرض بيانات الموظفين
- 📊 **حالات الموظفين**: تصنيف الموظفين حسب حالاتهم (نشط، منتدب، متقاعد، إلخ)
- 🎯 **واجهة عربية**: تصميم جميل ومتجاوب باللغة العربية
- 💾 **قاعدة بيانات SQLite**: سهلة الاستخدام والنقل
- 🔒 **آمان البيانات**: حماية وتشفير المعلومات

## 🚀 التشغيل السريع

### الطريقة الأولى (الموصى بها):
```bash
python start.py
```

### الطريقة الثانية:
```bash
python clean_app.py
```

ثم افتح المتصفح على: **http://localhost:5000**

## 📋 الصفحات المتاحة

- 🏠 **الصفحة الرئيسية**: http://localhost:5000
- 👥 **قائمة الموظفين**: http://localhost:5000/employees  
- ➕ **إضافة موظف**: http://localhost:5000/add_employee
- 📊 **حالات الموظفين**: http://localhost:5000/employee_statuses

## 🛠️ المتطلبات

- Python 3.7+
- Flask
- SQLite3

## 📦 التثبيت

```bash
pip install flask
python clean_app.py
```

## 📁 هيكل المشروع

```
├── clean_app.py              # التطبيق الرئيسي النظيف
├── start.py                  # ملف التشغيل
├── customs_employees.db      # قاعدة البيانات
├── templates/                # قوالب HTML
│   ├── base.html            # القالب الأساسي
│   ├── index.html           # الصفحة الرئيسية
│   ├── employees/           # صفحات الموظفين
│   └── employee_statuses/   # صفحات حالات الموظفين
└── static/                  # الملفات الثابتة
```

## 🎨 حالات الموظفين

### الحالات النشطة (تدخل في التعداد):
- ✅ **نشط** - الموظفين العاملين حالياً
- 👔 **منتدب** - الموظفين المنتدبين لمهام أخرى  
- 🎓 **في دراسة/تكوين** - الموظفين في برامج التدريب

### الحالات غير النشطة (لا تدخل في التعداد):
- ⏸️ **مستودع** - الموظفين في إجازة استيداع
- 🛑 **موقوف** - الموظفين الموقوفين عن العمل
- 📅 **في عطلة طويلة الأمد** - الموظفين في إجازات طويلة
- 🕰️ **متقاعد** - الموظفين المتقاعدين
- 🚪 **مستقيل** - الموظفين المستقيلين
- 🔄 **محول خارجياً** - الموظفين المحولين لجهات أخرى
- 💔 **متوفى** - الموظفين المتوفين (رحمهم الله)

## 🔧 الاستخدام

1. **شغل النظام**: `python start.py`
2. **افتح المتصفح**: http://localhost:5000
3. **ابدأ بإضافة موظفين** من قائمة الموظفين
4. **تصفح حالات الموظفين** لمراقبة الإحصائيات

## 📞 الدعم

النظام جاهز للاستخدام ومُحسَّن للأداء والاستقرار.

---
**تم تطوير النظام خصيصاً للجمارك الجزائرية 🇩🇿**
