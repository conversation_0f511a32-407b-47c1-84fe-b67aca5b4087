{% extends "base.html" %}

{% block title %}طلب استيداع - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}طلب استيداع{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-pause me-2"></i>طلب استيداع الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الرصيد المتبقي:</strong> 
                            <span class="badge bg-info">{{ remaining_months }} شهر</span>
                        </p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-success">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فترات الاستيداع السابقة -->
{% if previous_leaves %}
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h6 class="mb-0">
            <i class="fas fa-history me-2"></i>فترات الاستيداع السابقة
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>الفترة</th>
                        <th>المدة</th>
                        <th>السبب</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in previous_leaves %}
                    <tr>
                        <td>{{ leave.period_number }}</td>
                        <td>{{ leave.duration_months }} شهر</td>
                        <td>{{ leave.reason }}</td>
                        <td>{{ leave.start_date }}</td>
                        <td>{{ leave.end_date }}</td>
                        <td><span class="badge bg-secondary">{{ leave.status }}</span></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- نموذج طلب الاستيداع -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>بيانات طلب الاستيداع
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="leaveForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="period_number" class="form-label">رقم الفترة <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="period_number" name="period_number" 
                               min="1" max="10" value="{{ (previous_leaves|length) + 1 }}" required>
                        <div class="form-text">الفترة الحالية للاستيداع</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="duration_months" class="form-label">المدة بالأشهر <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="duration_months" name="duration_months" 
                               min="1" max="{{ remaining_months }}" required>
                        <div class="form-text">الحد الأقصى: {{ remaining_months }} شهر</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الاستيداع <span class="text-danger">*</span></label>
                        <select class="form-select" id="reason" name="reason" required>
                            <option value="">اختر سبب الاستيداع</option>
                            {% for reason in leave_reasons %}
                            <option value="{{ reason.reason }}">{{ reason.reason }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">تاريخ بداية الاستيداع <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_number" class="form-label">رقم المقرر/الوثيقة</label>
                        <input type="text" class="form-control" id="decision_number" name="decision_number" 
                               placeholder="أدخل رقم المقرر">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_date" class="form-label">تاريخ المقرر/الوثيقة</label>
                        <input type="date" class="form-control" id="decision_date" name="decision_date">
                    </div>
                </div>
            </div>
            
            <!-- معاينة تاريخ النهاية -->
            <div class="alert alert-info" id="endDatePreview" style="display: none;">
                <i class="fas fa-calendar-alt me-2"></i>
                <strong>تاريخ انتهاء الاستيداع المتوقع:</strong> <span id="calculatedEndDate"></span>
            </div>
            
            <!-- تحذير الرصيد -->
            {% if remaining_months <= 12 %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> الرصيد المتبقي للاستيداع هو {{ remaining_months }} شهر فقط.
            </div>
            {% endif %}
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-warning" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل الاستيداع
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// حساب تاريخ النهاية عند تغيير تاريخ البداية أو المدة
function calculateEndDate() {
    const startDate = document.getElementById('start_date').value;
    const duration = document.getElementById('duration_months').value;
    
    if (startDate && duration) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setMonth(end.getMonth() + parseInt(duration));
        
        const endDateFormatted = end.toISOString().split('T')[0];
        document.getElementById('calculatedEndDate').textContent = endDateFormatted;
        document.getElementById('endDatePreview').style.display = 'block';
    } else {
        document.getElementById('endDatePreview').style.display = 'none';
    }
}

document.getElementById('start_date').addEventListener('change', calculateEndDate);
document.getElementById('duration_months').addEventListener('input', function() {
    const maxMonths = {{ remaining_months }};
    const enteredMonths = parseInt(this.value);
    
    if (enteredMonths > maxMonths) {
        alert(`المدة المدخلة تتجاوز الرصيد المتاح (${maxMonths} شهر)`);
        this.value = maxMonths;
    }
    
    calculateEndDate();
});

document.getElementById('leaveForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const periodNumber = document.getElementById('period_number').value;
    const duration = document.getElementById('duration_months').value;
    const reason = document.getElementById('reason').value;
    const startDate = document.getElementById('start_date').value;
    
    if (!periodNumber || !duration || !reason || !startDate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const maxMonths = {{ remaining_months }};
    if (parseInt(duration) > maxMonths) {
        alert(`المدة المطلوبة تتجاوز الرصيد المتاح (${maxMonths} شهر)`);
        return;
    }
    
    // تأكيد الإرسال
    if (confirm('هل أنت متأكد من تسجيل طلب الاستيداع؟')) {
        // تعطيل الزر وإظهار مؤشر التحميل
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
        
        // إرسال النموذج
        this.submit();
    }
});

// تحديد تاريخ اليوم كحد أدنى لتاريخ البداية
document.getElementById('start_date').min = new Date().toISOString().split('T')[0];
</script>
{% endblock %}