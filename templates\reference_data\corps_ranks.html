{% extends "base.html" %}

{% block title %}إدارة الأسلاك والرتب - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة الأسلاك والرتب{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <a href="{{ url_for('settings') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
        </a>
    </div>
</div>

<!-- الأسلاك -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-users-cog me-2"></i>الأسلاك</h5>
        <button class="btn btn-primary btn-sm" onclick="addCorps()">
            <i class="fas fa-plus me-2"></i>إضافة سلك جديد
        </button>
    </div>
    <div class="card-body">
        {% if corps %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>اسم السلك</th>
                        <th>الوصف</th>
                        <th>عدد الرتب</th>
                        <th>عدد الموظفين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for corps_item in corps %}
                    <tr>
                        <td><strong>{{ corps_item.name }}</strong></td>
                        <td>{{ corps_item.description or 'لا يوجد وصف' }}</td>
                        <td>
                            <span class="badge bg-info">{{ corps_item.ranks_count }}</span>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ corps_item.employees_count }}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض الرتب" onclick="viewRanks({{ corps_item.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل" onclick="editCorps({{ corps_item.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف" onclick="deleteCorps({{ corps_item.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أسلاك مسجلة</h5>
            <button class="btn btn-primary" onclick="addCorps()">
                <i class="fas fa-plus me-2"></i>إضافة سلك جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- الرتب -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-medal me-2"></i>الرتب</h5>
        <button class="btn btn-success btn-sm" onclick="addRank()">
            <i class="fas fa-plus me-2"></i>إضافة رتبة جديدة
        </button>
    </div>
    <div class="card-body">
        {% if ranks %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>اسم الرتبة</th>
                        <th>السلك</th>
                        <th>المستوى</th>
                        <th>عدد الموظفين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rank in ranks %}
                    <tr>
                        <td><strong>{{ rank.name }}</strong></td>
                        <td>{{ rank.corps_name }}</td>
                        <td>
                            <span class="badge bg-primary">{{ rank.level }}</span>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ rank.employees_count }}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-warning" title="تعديل" onclick="editRank({{ rank.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف" onclick="deleteRank({{ rank.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-medal fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد رتب مسجلة</h5>
            <button class="btn btn-success" onclick="addRank()">
                <i class="fas fa-plus me-2"></i>إضافة رتبة جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal إضافة/تعديل سلك -->
<div class="modal fade" id="corpsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="corpsModalTitle">
                    <i class="fas fa-users-cog me-2"></i>إضافة سلك جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="corpsForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم السلك <span class="required">*</span></label>
                        <input type="text" name="name" class="form-control" required placeholder="مثال: سلك المفتشين الرئيسيين للجمارك">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3" placeholder="وصف مختصر للسلك"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ السلك</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة/تعديل رتبة -->
<div class="modal fade" id="rankModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rankModalTitle">
                    <i class="fas fa-medal me-2"></i>إضافة رتبة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rankForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الرتبة <span class="required">*</span></label>
                        <input type="text" name="name" class="form-control" required placeholder="مثال: مفتش رئيسي للجمارك">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">السلك <span class="required">*</span></label>
                        <select name="corps_id" class="form-select" required>
                            <option value="">اختر السلك</option>
                            {% for corps_item in corps %}
                            <option value="{{ corps_item.id }}">{{ corps_item.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المستوى <span class="required">*</span></label>
                        <input type="number" name="level" class="form-control" required min="1" max="20" placeholder="1">
                        <div class="form-text">المستوى الهرمي للرتبة (1 = الأعلى)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ الرتبة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal عرض رتب السلك -->
<div class="modal fade" id="viewRanksModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-medal me-2"></i>رتب السلك
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ranksModalBody">
                <!-- سيتم ملؤه ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addCorps() {
    document.getElementById('corpsModalTitle').innerHTML = '<i class="fas fa-users-cog me-2"></i>إضافة سلك جديد';
    document.getElementById('corpsForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('corpsModal'));
    modal.show();
}

function editCorps(id) {
    document.getElementById('corpsModalTitle').innerHTML = '<i class="fas fa-users-cog me-2"></i>تعديل السلك';
    // يمكن إضافة تحميل بيانات السلك للتعديل
    const modal = new bootstrap.Modal(document.getElementById('corpsModal'));
    modal.show();
}

function deleteCorps(id) {
    if (confirm('هل أنت متأكد من حذف هذا السلك؟ سيتم حذف جميع الرتب المرتبطة به.')) {
        alert('تم حذف السلك بنجاح');
        location.reload();
    }
}

function addRank() {
    document.getElementById('rankModalTitle').innerHTML = '<i class="fas fa-medal me-2"></i>إضافة رتبة جديدة';
    document.getElementById('rankForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('rankModal'));
    modal.show();
}

function editRank(id) {
    document.getElementById('rankModalTitle').innerHTML = '<i class="fas fa-medal me-2"></i>تعديل الرتبة';
    // يمكن إضافة تحميل بيانات الرتبة للتعديل
    const modal = new bootstrap.Modal(document.getElementById('rankModal'));
    modal.show();
}

function deleteRank(id) {
    if (confirm('هل أنت متأكد من حذف هذه الرتبة؟')) {
        alert('تم حذف الرتبة بنجاح');
        location.reload();
    }
}

function viewRanks(corpsId) {
    // جلب رتب السلك وعرضها
    fetch(`/api/ranks/${corpsId}`)
        .then(response => response.json())
        .then(ranks => {
            let content = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>الرتبة</th><th>المستوى</th></tr></thead><tbody>';
            ranks.forEach(rank => {
                content += `<tr><td>${rank.name}</td><td><span class="badge bg-primary">${rank.level || 'غير محدد'}</span></td></tr>`;
            });
            content += '</tbody></table></div>';
            
            if (ranks.length === 0) {
                content = '<div class="text-center py-3"><i class="fas fa-medal fa-2x text-muted mb-2"></i><p class="text-muted">لا توجد رتب في هذا السلك</p></div>';
            }
            
            document.getElementById('ranksModalBody').innerHTML = content;
            const modal = new bootstrap.Modal(document.getElementById('viewRanksModal'));
            modal.show();
        })
        .catch(error => {
            console.error('خطأ في جلب الرتب:', error);
            alert('حدث خطأ في جلب بيانات الرتب');
        });
}

// معالجة نماذج الحفظ
document.getElementById('corpsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ السلك بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('corpsModal')).hide();
    location.reload();
});

document.getElementById('rankForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ الرتبة بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('rankModal')).hide();
    location.reload();
});
</script>
{% endblock %}
