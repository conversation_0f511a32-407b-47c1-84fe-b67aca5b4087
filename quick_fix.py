#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع للنظام
"""

import sqlite3
import os

def create_minimal_tables():
    """إنشاء الجداول الأساسية"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        print("🔧 إنشاء الجداول الأساسية...")
        
        # جدول أسباب الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reason TEXT NOT NULL UNIQUE,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج أسباب افتراضية
        reasons = [
            'رعاية الأطفال',
            'الدراسة', 
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        for reason in reasons:
            cursor.execute('''
                INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء الجداول بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def create_working_app():
    """إنشاء تطبيق يعمل"""
    app_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3

app = Flask(__name__)
app.config['SECRET_KEY'] = 'customs-algeria-2025'

def get_db_connection():
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/special_status/')
def special_status_index():
    """الصفحة الرئيسية للحالات الخاصة"""
    conn = get_db_connection()
    try:
        # إحصائيات بسيطة
        stats = {
            'active_leave_of_absence': 0,
            'pending_resignations': 0,
            'active_suspensions': 0,
            'total_deaths': 0,
            'resignations': 0,
            'deaths': 0,
            'retirements': 0,
            'external_transfers': 0,
            'leave_of_absence': 0,
            'suspensions': 0
        }
    except:
        stats = {}
    finally:
        conn.close()
    
    return render_template('special_status/index.html', stats=stats)

@app.route('/special_status/leave_reasons_settings')
def leave_reasons_settings():
    """إعدادات أسباب الاستيداع"""
    conn = get_db_connection()
    try:
        reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        reasons = []
    finally:
        conn.close()
    
    return render_template('special_status/leave_reasons_settings.html', reasons=reasons)

@app.route('/special_status/add_leave_reason', methods=['POST'])
def add_leave_reason():
    """إضافة سبب استيداع جديد"""
    reason = request.form.get('reason')
    if reason:
        conn = get_db_connection()
        try:
            conn.execute('''
                INSERT INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
            conn.commit()
            flash('تم إضافة السبب بنجاح', 'success')
        except:
            flash('خطأ في إضافة السبب', 'error')
        finally:
            conn.close()
    
    return redirect(url_for('leave_reasons_settings'))

@app.route('/special_status/leave_of_absence/add', methods=['GET', 'POST'])
def add_leave_of_absence():
    """إضافة استيداع جديد"""
    if request.method == 'POST':
        flash('تم إضافة الاستيداع بنجاح', 'success')
        return redirect(url_for('special_status_index'))
    
    # الحصول على قائمة الموظفين
    conn = get_db_connection()
    try:
        employees = conn.execute('''
            SELECT id, registration_number, first_name, last_name
            FROM employees 
            ORDER BY registration_number
        ''').fetchall()
    except:
        employees = []
    
    try:
        leave_reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        leave_reasons = []
    finally:
        conn.close()
    
    return render_template('special_status/add_leave_of_absence.html', 
                         employees=employees, leave_reasons=leave_reasons)

@app.route('/special_status/api/employee/<int:employee_id>')
def api_employee_info(employee_id):
    """API للحصول على معلومات الموظف"""
    conn = get_db_connection()
    try:
        employee = conn.execute('''
            SELECT e.*, r.name as rank_name, s.name as service_name
            FROM employees e
            LEFT JOIN ranks r ON e.current_rank_id = r.id
            LEFT JOIN services s ON e.current_service_id = s.id
            WHERE e.id = ?
        ''', (employee_id,)).fetchone()
        
        if employee:
            return jsonify({
                'id': employee['id'],
                'registration_number': employee['registration_number'],
                'first_name': employee['first_name'],
                'last_name': employee['last_name'],
                'rank_name': employee['rank_name'] if employee['rank_name'] else 'غير محدد',
                'service_name': employee['service_name'] if employee['service_name'] else 'غير محدد',
                'status': employee['status'] if employee['status'] else 'نشط'
            })
        else:
            return jsonify({'error': 'الموظف غير موجود'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/special_status/api/employee/<int:employee_id>/leave_balance')
def api_employee_leave_balance(employee_id):
    """API للحصول على رصيد الاستيداع للموظف"""
    # رصيد افتراضي
    balance = {
        'total_used_months': 0,
        'total_used_years': 0,
        'remaining_months': 60,
        'remaining_years': 5,
        'max_allowed_years': 5
    }
    return jsonify(balance)

if __name__ == '__main__':
    print("🚀 تشغيل التطبيق...")
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("📋 الحالات الخاصة: http://localhost:5000/special_status/")
    app.run(debug=True, host='0.0.0.0', port=5000)
'''
    
    try:
        with open('working_app.py', 'w', encoding='utf-8') as f:
            f.write(app_code)
        print("✅ تم إنشاء التطبيق العامل")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح سريع للنظام")
    print("=" * 40)
    
    # إنشاء الجداول
    if not create_minimal_tables():
        print("❌ فشل في إنشاء الجداول")
        return
    
    # إنشاء التطبيق العامل
    if not create_working_app():
        print("❌ فشل في إنشاء التطبيق")
        return
    
    print("\n🎉 تم الإصلاح بنجاح!")
    print("🚀 لتشغيل التطبيق:")
    print("   python working_app.py")

if __name__ == '__main__':
    main()
