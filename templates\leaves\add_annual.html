{% extends "base.html" %}

{% block title %}إضافة عطلة سنوية - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إضافة عطلة سنوية{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <a href="{{ url_for('leaves') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للعطل والإجازات
        </a>
    </div>
</div>

<!-- نموذج إضافة العطلة السنوية -->
<form method="POST" id="annualLeaveForm">
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-calendar-check me-2"></i>بيانات العطلة السنوية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الموظف <span class="required">*</span></label>
                        <select name="employee_id" class="form-select" required id="employee_select" onchange="updateLeaveBalance()">
                            <option value="">اختر الموظف</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}">
                                {{ employee.registration_number }} - {{ employee.first_name }} {{ employee.last_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">سنة العطلة <span class="required">*</span></label>
                        <select name="year" class="form-select" required id="year_select" onchange="updateLeaveBalance()">
                            {% for year in range(2020, 2030) %}
                            <option value="{{ year }}" {% if year == 2025 %}selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">الرصيد المتاح</label>
                        <div class="form-control bg-light" id="available_balance">
                            <span class="text-muted">اختر الموظف والسنة</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ بداية العطلة <span class="required">*</span></label>
                        <input type="date" name="start_date" class="form-control" required id="start_date" onchange="calculateEndDate()">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">عدد أيام العطلة <span class="required">*</span></label>
                        <input type="number" name="days_count" class="form-control" required min="1" max="50" id="days_count" onchange="calculateEndDate()">
                        <div class="form-text">الحد الأقصى 50 يوم في السنة</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ نهاية العطلة</label>
                        <input type="date" name="end_date" class="form-control" readonly id="end_date">
                        <div class="form-text">محسوب تلقائياً</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المكان المقصود</label>
                        <input type="text" name="destination" class="form-control" placeholder="الوجهة أو المكان المقصود">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم مقرر العطلة</label>
                        <input type="text" name="decision_number" class="form-control" placeholder="رقم المقرر">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ مقرر العطلة</label>
                        <input type="date" name="decision_date" class="form-control">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المتبقي بعد هذه العطلة</label>
                        <div class="form-control bg-light" id="remaining_after">
                            <span class="text-muted">سيتم حسابه تلقائياً</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار الحفظ -->
    <div class="card mt-4">
        <div class="card-body text-center">
            <button type="submit" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-save me-2"></i>حفظ العطلة السنوية
            </button>
            <a href="{{ url_for('leaves') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </div>
</form>
{% endblock %}

{% block scripts %}
<script>
let currentBalance = 0;

// تحديث رصيد العطل عند تغيير الموظف أو السنة
function updateLeaveBalance() {
    const employeeId = document.getElementById('employee_select').value;
    const year = document.getElementById('year_select').value;
    const balanceDiv = document.getElementById('available_balance');
    
    if (employeeId && year) {
        fetch(`/api/employee/${employeeId}/leave-balance/${year}`)
            .then(response => response.json())
            .then(data => {
                currentBalance = data.remaining;
                balanceDiv.innerHTML = `
                    <div class="d-flex justify-content-between">
                        <span>المجموع: <strong>${data.total}</strong></span>
                        <span>المستخدم: <strong class="text-danger">${data.used}</strong></span>
                        <span>المتبقي: <strong class="text-success">${data.remaining}</strong></span>
                    </div>
                `;
                calculateEndDate();
            })
            .catch(error => {
                console.error('خطأ في جلب رصيد العطل:', error);
                balanceDiv.innerHTML = '<span class="text-danger">خطأ في جلب البيانات</span>';
            });
    } else {
        balanceDiv.innerHTML = '<span class="text-muted">اختر الموظف والسنة</span>';
        currentBalance = 0;
    }
}

// حساب تاريخ النهاية والمتبقي
function calculateEndDate() {
    const startDate = document.getElementById('start_date').value;
    const daysCount = parseInt(document.getElementById('days_count').value) || 0;
    const endDateInput = document.getElementById('end_date');
    const remainingDiv = document.getElementById('remaining_after');
    
    if (startDate && daysCount > 0) {
        // حساب تاريخ النهاية
        const start = new Date(startDate);
        const end = new Date(start);
        end.setDate(start.getDate() + daysCount - 1); // -1 لأن يوم البداية محسوب
        
        const endDateStr = end.toISOString().split('T')[0];
        endDateInput.value = endDateStr;
        
        // حساب المتبقي
        const remaining = currentBalance - daysCount;
        if (remaining >= 0) {
            remainingDiv.innerHTML = `<strong class="text-success">${remaining} يوم</strong>`;
        } else {
            remainingDiv.innerHTML = `<strong class="text-danger">${remaining} يوم (تجاوز الرصيد!)</strong>`;
        }
    } else {
        endDateInput.value = '';
        remainingDiv.innerHTML = '<span class="text-muted">سيتم حسابه تلقائياً</span>';
    }
}

// التحقق من صحة النموذج
document.getElementById('annualLeaveForm').addEventListener('submit', function(e) {
    const employeeId = document.getElementById('employee_select').value;
    const startDate = document.getElementById('start_date').value;
    const daysCount = parseInt(document.getElementById('days_count').value) || 0;
    
    if (!employeeId || !startDate || daysCount <= 0) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    if (daysCount > currentBalance) {
        e.preventDefault();
        alert(`عدد الأيام المطلوب (${daysCount}) أكبر من الرصيد المتاح (${currentBalance})`);
        return false;
    }
    
    if (daysCount > 50) {
        e.preventDefault();
        alert('الحد الأقصى للعطلة السنوية هو 50 يوم');
        return false;
    }
    
    // التحقق من أن تاريخ البداية ليس في الماضي
    const today = new Date();
    const start = new Date(startDate);
    today.setHours(0, 0, 0, 0);
    start.setHours(0, 0, 0, 0);
    
    if (start < today) {
        if (!confirm('تاريخ بداية العطلة في الماضي. هل تريد المتابعة؟')) {
            e.preventDefault();
            return false;
        }
    }
});

// تحديد تاريخ اليوم كحد أدنى
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').min = today;
});
</script>
{% endblock %}
