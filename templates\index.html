{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}لوحة التحكم{% endblock %}

{% block content %}
<!-- الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>الإحصائيات الرئيسية</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="stat-number text-primary">{{ stats.total_employees or 0 }}</h3>
                            <p class="stat-label">إجمالي الموظفين</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <h3 class="stat-number text-success">{{ stats.active_employees or 0 }}</h3>
                            <p class="stat-label">الموظفين النشطين</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-icon bg-warning">
                                <i class="fas fa-calendar-times"></i>
                            </div>
                            <h3 class="stat-number text-warning">{{ stats.total_leaves or 0 }}</h3>
                            <p class="stat-label">إجمالي العطل</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-icon bg-info">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h3 class="stat-number text-info">{{ stats.active_leaves or 0 }}</h3>
                            <p class="stat-label">العطل الحالية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الوصول السريع -->
<div class="card mb-4 border-0 shadow-sm">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>الوصول السريع</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <a href="{{ url_for('add_employee') }}" class="btn btn-primary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                    <span>إضافة موظف جديد</span>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <a href="{{ url_for('employees') }}" class="btn btn-success btn-lg w-100 quick-action-btn">
                    <i class="fas fa-list fa-2x d-block mb-2"></i>
                    <span>قائمة الموظفين</span>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <a href="{{ url_for('leaves') }}" class="btn btn-warning btn-lg w-100 quick-action-btn">
                    <i class="fas fa-calendar-plus fa-2x d-block mb-2"></i>
                    <span>إدارة العطل</span>
                </a>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <a href="/status/" class="btn btn-secondary btn-lg w-100 quick-action-btn">
                    <i class="fas fa-user-cog fa-2x d-block mb-2"></i>
                    <span>حالات الموظفين</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات التفصيلية -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-light">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>إحصائيات الموارد البشرية</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center mb-3">
                        <div class="mini-stat">
                            <i class="fas fa-certificate text-secondary fa-2x mb-2"></i>
                            <h5 class="text-secondary">{{ stats.total_certificates or 0 }}</h5>
                            <small class="text-muted">الشهادات</small>
                        </div>
                    </div>
                    <div class="col-6 text-center mb-3">
                        <div class="mini-stat">
                            <i class="fas fa-graduation-cap text-dark fa-2x mb-2"></i>
                            <h5 class="text-dark">{{ stats.total_training or 0 }}</h5>
                            <small class="text-muted">التكوين</small>
                        </div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="mini-stat">
                            <i class="fas fa-exchange-alt text-info fa-2x mb-2"></i>
                            <h5 class="text-info">{{ stats.total_transfers or 0 }}</h5>
                            <small class="text-muted">التنقلات</small>
                        </div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="mini-stat">
                            <i class="fas fa-arrow-up text-primary fa-2x mb-2"></i>
                            <h5 class="text-primary">{{ stats.total_promotions or 0 }}</h5>
                            <small class="text-muted">الترقيات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-light">
                <h6 class="mb-0"><i class="fas fa-balance-scale me-2"></i>العقوبات والمكافآت</h6>
            </div>
            <div class="card-body">
                <div class="row h-100 align-items-center">
                    <div class="col-6 text-center">
                        <div class="penalty-reward-stat">
                            <div class="stat-circle bg-danger text-white mb-3">
                                <i class="fas fa-gavel fa-2x"></i>
                            </div>
                            <h4 class="text-danger">{{ stats.total_sanctions or 0 }}</h4>
                            <p class="text-muted mb-0">العقوبات</p>
                        </div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="penalty-reward-stat">
                            <div class="stat-circle bg-success text-white mb-3">
                                <i class="fas fa-award fa-2x"></i>
                            </div>
                            <h4 class="text-success">{{ stats.total_rewards or 0 }}</h4>
                            <p class="text-muted mb-0">المكافآت</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آخر النشاطات -->
<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-clock me-2"></i>آخر النشاطات</h4>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            مرحباً بك في نظام إدارة موظفي الجمارك الجزائرية. يمكنك البدء بإضافة الموظفين وإدارة بياناتهم من خلال القوائم الجانبية.
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">إضافة موظف جديد</h6>
                        <small class="text-muted">ابدأ بإضافة بيانات الموظفين</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                        <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">إدارة العطل</h6>
                        <small class="text-muted">تسجيل وإدارة عطل الموظفين</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">الشهادات والتكوين</h6>
                        <small class="text-muted">إدارة شهادات وتكوين الموظفين</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم التنبيهات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-bell me-2"></i>التنبيهات والإشعارات</h5>
                <div>
                    <span class="badge bg-primary">{{ notifications|length }}</span>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="refreshNotifications()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if notifications %}
                <div class="notifications-container">
                    {% for notification in notifications %}
                    <div class="notification-item alert alert-{{ notification.type }} alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon me-3">
                                <i class="{{ notification.icon }} fa-lg"></i>
                            </div>
                            <div class="notification-content flex-grow-1">
                                <h6 class="notification-title mb-1">{{ notification.title }}</h6>
                                <p class="notification-message mb-1">{{ notification.message }}</p>
                                <small class="notification-time text-muted">{{ notification.time }}</small>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">لا توجد تنبيهات جديدة</h6>
                    <p class="text-muted">سيتم عرض التنبيهات المهمة هنا</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أحدث الأنشطة -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-user-plus me-2"></i>أحدث الموظفين</h6>
            </div>
            <div class="card-body">
                {% if recent_employees %}
                <div class="list-group list-group-flush">
                    {% for employee in recent_employees %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ employee.first_name }} {{ employee.last_name }}</h6>
                            <small class="text-muted">رقم التسجيل: {{ employee.registration_number }}</small>
                        </div>
                        <span class="badge bg-primary">جديد</span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد بيانات موظفين جدد</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-calendar-times me-2"></i>العطل الحالية</h6>
            </div>
            <div class="card-body">
                {% if current_leaves %}
                <div class="list-group list-group-flush">
                    {% for leave in current_leaves %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ leave.first_name }} {{ leave.last_name }}</h6>
                            <small class="text-muted">{{ leave.type }} - من {{ leave.start_date }} إلى {{ leave.end_date }}</small>
                        </div>
                        <span class="badge bg-warning">جارية</span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد عطل جارية حالياً</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* تحسين الإحصائيات */
.stat-item {
    padding: 1rem;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-header h4, .card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* تحسين الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.quick-action-btn {
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.quick-action-btn span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* تحسين الإحصائيات المصغرة */
.mini-stat {
    padding: 1rem 0.5rem;
    transition: all 0.3s ease;
}

.mini-stat:hover {
    transform: scale(1.05);
}

.stat-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.penalty-reward-stat {
    transition: all 0.3s ease;
}

.penalty-reward-stat:hover {
    transform: translateY(-5px);
}

/* تحسين التنبيهات */
.notifications-container {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    border-left: 4px solid;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.notification-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.notification-item.alert-primary {
    border-left-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.notification-item.alert-success {
    border-left-color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.notification-item.alert-warning {
    border-left-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.notification-item.alert-danger {
    border-left-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.notification-item.alert-info {
    border-left-color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
}

.notification-icon {
    width: 40px;
    text-align: center;
}

.notification-title {
    font-weight: 600;
    color: #495057;
}

.notification-message {
    color: #6c757d;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-item {
    animation: slideInRight 0.5s ease;
}

/* تحسين شريط التمرير */
.notifications-container::-webkit-scrollbar {
    width: 6px;
}

.notifications-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.notifications-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.notifications-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function refreshNotifications() {
    location.reload();
}

// تحديث التنبيهات كل 5 دقائق
setInterval(function() {
    // يمكن إضافة AJAX لتحديث التنبيهات دون إعادة تحميل الصفحة
}, 300000); // 5 دقائق

// إضافة تأثير صوتي للتنبيهات الجديدة (اختياري)
document.addEventListener('DOMContentLoaded', function() {
    const notifications = document.querySelectorAll('.notification-item');
    notifications.forEach((notification, index) => {
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(30px)';
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
        }, index * 200);
    });
});
</script>
{% endblock %}
