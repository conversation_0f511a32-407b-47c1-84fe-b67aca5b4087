{% extends "base.html" %}

{% block title %}نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">نظام إدارة موظفي الجمارك الجزائرية</h1>
            <p class="hero-subtitle">منصة إلكترونية شاملة لإدارة شؤون الموظفين</p>
            <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-tachometer-alt"></i> دخول لوحة التحكم
            </a>
        </div>
    </div>
</section>

<!-- Services Grid -->
<section class="services-section">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="section-title">الخدمات المتاحة</h2>
                <p class="section-subtitle">اختر الخدمة التي تريد الوصول إليها</p>
            </div>
        </div>
        
        <div class="hexagon-grid">
            <!-- إدارة الموظفين -->
            <div class="hexagon-item" onclick="location.href='{{ url_for('employees') }}'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-users"></i>
                </div>
                <h5>إدارة الموظفين</h5>
                <p>إضافة وتعديل بيانات الموظفين</p>
            </div>
            
            <!-- العطل والإجازات -->
            <div class="hexagon-item" onclick="location.href='{{ url_for('leaves') }}'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-calendar-alt"></i>
                </div>
                <h5>العطل والإجازات</h5>
                <p>إدارة العطل السنوية والمرضية</p>
            </div>
            
            <!-- الشهادات والتكوين -->
            <div class="hexagon-item" onclick="location.href='{{ url_for('certificates') }}'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-graduation-cap"></i>
                </div>
                <h5>الشهادات والتكوين</h5>
                <p>إدارة الشهادات والدورات التدريبية</p>
            </div>
            
            <!-- العقوبات والمكافآت -->
            <div class="hexagon-item" onclick="location.href='#'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-balance-scale"></i>
                </div>
                <h5>العقوبات والمكافآت</h5>
                <p>إدارة العقوبات والمكافآت</p>
            </div>
            
            <!-- التنقلات والحركات -->
            <div class="hexagon-item" onclick="location.href='#'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-exchange-alt"></i>
                </div>
                <h5>التنقلات والحركات</h5>
                <p>إدارة تنقلات الموظفين</p>
            </div>
            
            <!-- الترقيات -->
            <div class="hexagon-item" onclick="location.href='#'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-arrow-up"></i>
                </div>
                <h5>الترقيات</h5>
                <p>إدارة ترقيات الموظفين</p>
            </div>
            
            <!-- حالات الموظفين -->
            <div class="hexagon-item" onclick="location.href='{{ url_for('employee_statuses') }}'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-user-check"></i>
                </div>
                <h5>حالات الموظفين</h5>
                <p>إدارة حالات الموظفين المختلفة</p>
            </div>
            
            <!-- الإعدادات -->
            <div class="hexagon-item" onclick="location.href='{{ url_for('settings') }}'">
                <div class="hexagon">
                    <i class="hexagon-icon fas fa-cog"></i>
                </div>
                <h5>الإعدادات</h5>
                <p>إعدادات النظام والمستخدمين</p>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-shield-alt fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">أمان عالي</h5>
                        <p class="card-text">نظام آمن ومحمي لحفظ بيانات الموظفين بسرية تامة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-chart-line fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">تقارير شاملة</h5>
                        <p class="card-text">تقارير مفصلة وإحصائيات دقيقة لجميع بيانات الموظفين</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-mobile-alt fa-3x text-info"></i>
                        </div>
                        <h5 class="card-title">سهولة الاستخدام</h5>
                        <p class="card-text">واجهة سهلة ومتجاوبة تعمل على جميع الأجهزة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
