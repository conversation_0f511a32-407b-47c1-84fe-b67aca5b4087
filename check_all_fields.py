#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جميع حقول الموظف في قاعدة البيانات
"""

import sqlite3

def check_all_employee_fields():
    """فحص جميع الحقول المتاحة"""
    print("🔍 فحص جميع حقول الموظف...")
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    # الحصول على معلومات الأعمدة
    cursor.execute("PRAGMA table_info(employees)")
    columns = cursor.fetchall()
    
    print("📋 جميع الحقول المتاحة في جدول employees:")
    print("=" * 60)
    
    for i, col in enumerate(columns, 1):
        col_name = col[1]
        col_type = col[2]
        is_nullable = "NULL" if col[3] == 0 else "NOT NULL"
        default_val = f"DEFAULT {col[4]}" if col[4] else ""
        
        print(f"{i:2d}. {col_name:<25} {col_type:<10} {is_nullable} {default_val}")
    
    # الحصول على بيانات الموظف التجريبي
    cursor.execute("SELECT * FROM employees WHERE id = 11")
    employee = cursor.fetchone()
    
    if employee:
        print(f"\n📊 بيانات الموظف التجريبي (ID: 11):")
        print("=" * 60)
        
        for i, col in enumerate(columns):
            col_name = col[1]
            value = employee[i] if i < len(employee) else None
            status = "✅ موجود" if value else "❌ فارغ"
            print(f"{col_name:<25} = {value} {status}")
    
    conn.close()
    
    print(f"\n📈 إجمالي الحقول: {len(columns)} حقل")

if __name__ == "__main__":
    check_all_employee_fields()