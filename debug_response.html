<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الموظفين - نظام إدارة موظفي الجمارك الجزائرية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- خط عربي جميل -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS خاص بحالات الموظفين -->
    <link rel="stylesheet" href="/static/css/employee-status.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }
        
        .sidebar-header {
            padding: 1.5rem 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-header h4 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .sidebar-header p {
            margin: 0.5rem 0 0 0;
            font-size: 0.85rem;
            opacity: 0.8;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s;
            border-radius: 0.5rem;
            margin: 0.25rem 1rem;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }
        
        .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .nav-link i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 0.75rem 0.75rem 0 0;
            padding: 1rem 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        
        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
            padding: 0.75rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background-color: #ffffff;
            transform: translateY(-1px);
        }
        
        /* تحسين القوائم المنسدلة - نقل السهم إلى اليسار */
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: left 0.75rem center;
            background-size: 16px 12px;
            padding-left: 2.5rem;
            padding-right: 0.75rem;
        }
        
        /* تحسين الخط في القوائم */
        .form-select option {
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            padding: 0.5rem;
            background-color: #f8f9fa;
            color: #495057;
        }
        
        .form-select option:hover {
            background-color: #e9ecef;
        }
        
        .form-select option:checked {
            background-color: #667eea;
            color: white;
        }
        
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .table thead th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            margin: 0;
            font-weight: 700;
        }
        
        .stats-card p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .mobile-toggle {
                display: block !important;
            }
        }
        
        .mobile-toggle {
            display: none;
        }
        
        .employee-photo {
            width: 120px;
            height: 150px;
            object-fit: cover;
            border-radius: 0.5rem;
            border: 2px solid #dee2e6;
        }
        
        .photo-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .photo-upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .required {
            color: #dc3545;
        }
        
        /* تحسين التسميات والنصوص */
        .form-label {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .form-text {
            font-family: 'Cairo', sans-serif;
            font-weight: 400;
            color: #6c757d;
            font-size: 0.8rem;
        }
        
        /* تحسين النصوص العامة */
        body, .card-body, .card-header {
            font-family: 'Cairo', sans-serif;
        }
        
        .card-header h5 {
            font-weight: 600;
            color: white;
        }
        
        /* تحسين الأزرار */
        .btn {
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* الشريط العلوي */
        .top-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0.75rem 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 280px; /* ترك مساحة للشريط الجانبي */
            z-index: 999; /* أقل من الشريط الجانبي */
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-title {
            display: flex;
            align-items: center;
        }

        .title-text {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .header-datetime {
            text-align: right;
        }

        .current-date, .current-time {
            display: block;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .current-date {
            font-weight: 500;
        }

        .current-time {
            font-weight: 300;
            opacity: 0.9;
        }

        /* تعديل المحتوى الرئيسي */
        .main-content {
            padding-top: 80px;
        }

        @media (max-width: 768px) {
            .top-header {
                right: 0; /* ملء العرض على الهواتف */
            }

            .title-text {
                font-size: 1rem;
            }

            .header-datetime {
                text-align: center;
                margin-top: 0.5rem;
            }

            .current-date, .current-time {
                display: inline-block;
                margin-right: 1rem;
                font-size: 0.8rem;
            }
        }
    </style>
    
    
<style>
    /* تحسين تصميم الجدول */
    .table th {
        font-weight: 600;
        font-size: 0.9rem;
        text-align: center;
        vertical-align: middle;
        border-bottom: 2px solid #667eea;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .table td {
        vertical-align: middle;
        text-align: center;
        font-size: 0.85rem;
    }
    
    /* تحسين الصورة الصغيرة */
    .employee-photo-small {
        transition: all 0.2s ease;
        cursor: pointer;
    }
    
    .employee-photo-small:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    /* تحسين أزرار الإجراءات */
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.25rem;
        margin: 0 1px;
    }
    
    .btn-outline-primary:hover {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .btn-outline-warning:hover {
        background-color: #ffc107;
        border-color: #ffc107;
    }
    
    .btn-outline-info:hover {
        background-color: #17a2b8;
        border-color: #17a2b8;
    }
    
    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    /* تحسين مجموعة الأزرار */
    .btn-group-sm {
        gap: 2px;
    }
    
    .btn-group-sm .btn {
        transition: all 0.2s ease;
        border-radius: 0.25rem !important;
    }
    
    .btn-group-sm .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    /* تحسين شارات الجنس */
    .badge {
        font-size: 0.7rem;
        padding: 0.35rem 0.6rem;
    }
    
    /* تحسين الصف عند التمرير */
    .table-hover tbody tr:hover {
        background-color: #f8f9ff;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }
    
    /* تحسين النصوص */
    .text-primary {
        font-weight: 600;
    }
    
    .text-info {
        font-weight: 500;
    }
    
    /* تحسين عرض الجدول على الشاشات الصغيرة */
    @media (max-width: 768px) {
        .table-responsive {
            font-size: 0.75rem;
        }
        
        .btn-group-sm .btn {
            padding: 0.2rem 0.3rem;
            font-size: 0.7rem;
            margin: 1px;
        }
        
        .employee-photo-small {
            width: 30px !important;
            height: 40px !important;
        }
        
        .table th, .table td {
            padding: 0.5rem 0.25rem;
        }
        
        /* تكديس الأزرار عمودياً على الشاشات الصغيرة جداً */
        @media (max-width: 576px) {
            .btn-group-sm {
                flex-direction: column;
                gap: 1px;
            }
            
            .btn-group-sm .btn {
                width: 100%;
                margin: 0;
            }
        }
    }
    
    /* إضافة تأثيرات بصرية جميلة */
    .table {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .table thead th {
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    /* تحسين شارة الجنس */
    .badge.bg-pink {
        background-color: #e91e63 !important;
    }
    
    /* تأثير التمرير على الصورة */
    .employee-photo-small {
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .employee-photo-small:hover {
        border-color: #667eea;
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
    }
    
    /* تحسين النصوص */
    .text-dark {
        font-weight: 500;
    }
    
    /* تحسين الأزرار */
    .btn {
        font-weight: 500;
        letter-spacing: 0.5px;
    }
    
    /* تحسين القوائم المنسدلة في البحث */
    .form-select {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        font-weight: 500;
    }
    
    .form-select:focus {
        background-color: #ffffff;
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    /* تحسين حقول البحث */
    .form-control {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        font-weight: 500;
    }
    
    .form-control:focus {
        background-color: #ffffff;
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>

</head>
<body>
    <!-- الشريط العلوي -->
    <div class="top-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8 col-12">
                    <div class="header-title">
                        <span class="title-text">تسيير مستخدمي الجمارك الجزائرية</span>
                    </div>
                </div>
                <div class="col-md-4 col-12">
                    <div class="header-datetime">
                        <div class="current-date">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <span id="currentDate"></span>
                        </div>
                        <div class="current-time">
                            <i class="fas fa-clock me-2"></i>
                            <span id="currentTime"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-building me-2"></i>الجمارك الجزائرية</h4>
            <p>نظام إدارة الموظفين</p>
        </div>
        
        <nav>
            <a href="/" class="nav-link ">
                <i class="fas fa-home"></i>
                لوحة التحكم
            </a>

            <a href="/employees" class="nav-link active">
                <i class="fas fa-users"></i>
                الموظفين
            </a>
            <a href="/leaves" class="nav-link ">
                <i class="fas fa-calendar-alt"></i>
                العطل والإجازات
            </a>
            <a href="/certificates" class="nav-link ">
                <i class="fas fa-graduation-cap"></i>
                الشهادات والتكوين
            </a>
            <a href="/sanctions" class="nav-link ">
                <i class="fas fa-gavel"></i>
                العقوبات والمكافآت
            </a>
            <a href="/transfers" class="nav-link ">
                <i class="fas fa-exchange-alt"></i>
                التنقلات والحركات
            </a>
            <a href="/promotions" class="nav-link ">
                <i class="fas fa-arrow-up"></i>
                الترقيات
            </a>
            <a href="/employee_statuses" class="nav-link ">
                <i class="fas fa-user-cog"></i>
                حالات الموظف
            </a>
            <a href="/statistics" class="nav-link ">
                <i class="fas fa-chart-bar"></i>
                الإحصائيات والتقارير
            </a>
            <a href="/settings" class="nav-link ">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
        </nav>
    </div>
    
    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- زر القائمة للهواتف -->
        <button class="btn btn-primary mobile-toggle mb-3" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- عنوان الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>إدارة الموظفين</h1>
        </div>
        
        <!-- رسائل التنبيه -->
        
            
                
                    <div class="alert alert-success alert-dismissible fade show">
                        تم إضافة الموظف بنجاح
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                
            
        
        
        <!-- محتوى الصفحة -->
        
<!-- شريط البحث والتصفية -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث برقم التسجيل، الاسم، الرتبة أو المصلحة..." 
                       value="">
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    
                    <option value="" selected>
                        
                    </option>
                    
                    <option value="نشط" >
                        نشط
                    </option>
                    
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="/employees" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <a href="/add_employee" class="btn btn-success w-100">
                    <i class="fas fa-plus"></i> إضافة موظف
                </a>
            </div>
        </form>
    </div>
</div>

<!-- جدول الموظفين -->
<div class="card">
    <div class="card-header">
        <h4>
            <i class="fas fa-users me-2"></i>قائمة الموظفين
            <span class="badge bg-light text-dark ms-2">6 موظف</span>
        </h4>
    </div>
    <div class="card-body p-0">
        
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>الصورة</th>
                        <th>رقم التسجيل</th>
                        <th>اللقب</th>
                        <th>الاسم</th>
                        <th>الجنس</th>
                        <th>الحالة</th>
                        <th>الرتبة</th>
                        <th>مصلحة التعيين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    
                    <tr>
                        <!-- الترقيم -->
                        <td>
                            <span class="badge bg-light text-dark">1</span>
                        </td>
                        
                        <!-- الصورة الصغيرة -->
                        <td>
                            
                            <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 50px; border-radius: 0.25rem; font-size: 0.8rem;">
                                <i class="fas fa-user"></i>
                            </div>
                            
                        </td>
                        
                        <!-- رقم التسجيل -->
                        <td>
                            <strong class="text-primary">777777</strong>
                        </td>
                        
                        <!-- اللقب -->
                        <td>
                            <strong>تشخيص</strong>
                            
                        </td>
                        
                        <!-- الاسم -->
                        <td>
                            <strong>تجربة</strong>
                            
                        </td>
                        
                        <!-- الجنس -->
                        <td>
                            
                                <span class="text-muted">غير محدد</span>
                            
                        </td>
                        
                        <!-- الحالة -->
                        <td>
                            <span class="badge employee-status-badge" data-status="نشط">
                                نشط
                            </span>
                        </td>
                        
                        <!-- الرتبة -->
                        <td>
                            
                                <span class="text-muted">غير محدد</span>
                            
                        </td>
                        
                        <!-- مصلحة التعيين -->
                        <td>
                            
                                <span class="text-muted">غير محدد</span>
                            
                        </td>
                        
                        <!-- الإجراءات -->
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewEmployee(15)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="editEmployee(15)" title="تعديل البيانات">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="printEmployee(15)" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteEmployee(15)" title="حذف الموظف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <!-- الترقيم -->
                        <td>
                            <span class="badge bg-light text-dark">2</span>
                        </td>
                        
                        <!-- الصورة الصغيرة -->
                        <td>
                            
                            <img src="data:image/jpeg;base64,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" alt="صورة الموظف" class="employee-photo-small" 
                                 style="width: 40px; height: 50px; object-fit: cover; border-radius: 0.25rem; border: 1px solid #dee2e6;"
                                 onclick="showPhotoModal('data:image/jpeg;base64,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', 'أحمد بن علي')">
                            
                        </td>
                        
                        <!-- رقم التسجيل -->
                        <td>
                            <strong class="text-primary">123456</strong>
                        </td>
                        
                        <!-- اللقب -->
                        <td>
                            <strong>بن علي</strong>
                            
                            <br><small class="text-muted">Ben Ali</small>
                            
                        </td>
                        
                        <!-- الاسم -->
                        <td>
                            <strong>أحمد</strong>
                            
                            <br><small class="text-muted">Ahmed</small>
                            
                        </td>
                        
                        <!-- الجنس -->
                        <td>
                            
                                <span class="badge bg-primary">
                                    <i class="fas fa-mars me-1"></i>ذكر
                                </span>
                            
                        </td>
                        
                        <!-- الحالة -->
                        <td>
                            <span class="badge employee-status-badge" data-status="غير محدد">
                                غير محدد
                            </span>
                        </td>
                        
                        <!-- الرتبة -->
                        <td>
                            
                                <span class="text-muted">غير محدد</span>
                            
                        </td>
                        
                        <!-- مصلحة التعيين -->
                        <td>
                            
                                <span class="text-dark">الإدارة العامة</span>
                            
                        </td>
                        
                        <!-- الإجراءات -->
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewEmployee(11)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="editEmployee(11)" title="تعديل البيانات">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="printEmployee(11)" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteEmployee(11)" title="حذف الموظف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <!-- الترقيم -->
                        <td>
                            <span class="badge bg-light text-dark">3</span>
                        </td>
                        
                        <!-- الصورة الصغيرة -->
                        <td>
                            
                            <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 50px; border-radius: 0.25rem; font-size: 0.8rem;">
                                <i class="fas fa-user"></i>
                            </div>
                            
                        </td>
                        
                        <!-- رقم التسجيل -->
                        <td>
                            <strong class="text-primary">123457</strong>
                        </td>
                        
                        <!-- اللقب -->
                        <td>
                            <strong>بن علي</strong>
                            
                        </td>
                        
                        <!-- الاسم -->
                        <td>
                            <strong>فاطمة</strong>
                            
                        </td>
                        
                        <!-- الجنس -->
                        <td>
                            
                                <span class="badge bg-pink" style="background-color: #e91e63;">
                                    <i class="fas fa-venus me-1"></i>أنثى
                                </span>
                            
                        </td>
                        
                        <!-- الحالة -->
                        <td>
                            <span class="badge employee-status-badge" data-status="نشط">
                                نشط
                            </span>
                        </td>
                        
                        <!-- الرتبة -->
                        <td>
                            
                                <strong class="text-info">مساعد إداري رئيسي</strong>
                            
                        </td>
                        
                        <!-- مصلحة التعيين -->
                        <td>
                            
                                <span class="text-dark">المحاسبة</span>
                            
                        </td>
                        
                        <!-- الإجراءات -->
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewEmployee(2)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="editEmployee(2)" title="تعديل البيانات">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="printEmployee(2)" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteEmployee(2)" title="حذف الموظف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <!-- الترقيم -->
                        <td>
                            <span class="badge bg-light text-dark">4</span>
                        </td>
                        
                        <!-- الصورة الصغيرة -->
                        <td>
                            
                            <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 50px; border-radius: 0.25rem; font-size: 0.8rem;">
                                <i class="fas fa-user"></i>
                            </div>
                            
                        </td>
                        
                        <!-- رقم التسجيل -->
                        <td>
                            <strong class="text-primary">123458</strong>
                        </td>
                        
                        <!-- اللقب -->
                        <td>
                            <strong>بن حسن</strong>
                            
                        </td>
                        
                        <!-- الاسم -->
                        <td>
                            <strong>محمد</strong>
                            
                        </td>
                        
                        <!-- الجنس -->
                        <td>
                            
                                <span class="badge bg-primary">
                                    <i class="fas fa-mars me-1"></i>ذكر
                                </span>
                            
                        </td>
                        
                        <!-- الحالة -->
                        <td>
                            <span class="badge employee-status-badge" data-status="نشط">
                                نشط
                            </span>
                        </td>
                        
                        <!-- الرتبة -->
                        <td>
                            
                                <strong class="text-info">ملحق إدارة رئيسي</strong>
                            
                        </td>
                        
                        <!-- مصلحة التعيين -->
                        <td>
                            
                                <span class="text-dark">الموارد البشرية</span>
                            
                        </td>
                        
                        <!-- الإجراءات -->
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewEmployee(3)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="editEmployee(3)" title="تعديل البيانات">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="printEmployee(3)" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteEmployee(3)" title="حذف الموظف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <!-- الترقيم -->
                        <td>
                            <span class="badge bg-light text-dark">5</span>
                        </td>
                        
                        <!-- الصورة الصغيرة -->
                        <td>
                            
                            <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 50px; border-radius: 0.25rem; font-size: 0.8rem;">
                                <i class="fas fa-user"></i>
                            </div>
                            
                        </td>
                        
                        <!-- رقم التسجيل -->
                        <td>
                            <strong class="text-primary">123459</strong>
                        </td>
                        
                        <!-- اللقب -->
                        <td>
                            <strong>بن يوسف</strong>
                            
                        </td>
                        
                        <!-- الاسم -->
                        <td>
                            <strong>خديجة</strong>
                            
                        </td>
                        
                        <!-- الجنس -->
                        <td>
                            
                                <span class="badge bg-pink" style="background-color: #e91e63;">
                                    <i class="fas fa-venus me-1"></i>أنثى
                                </span>
                            
                        </td>
                        
                        <!-- الحالة -->
                        <td>
                            <span class="badge employee-status-badge" data-status="نشط">
                                نشط
                            </span>
                        </td>
                        
                        <!-- الرتبة -->
                        <td>
                            
                                <strong class="text-info">مفتش</strong>
                            
                        </td>
                        
                        <!-- مصلحة التعيين -->
                        <td>
                            
                                <span class="text-dark">التفتيش</span>
                            
                        </td>
                        
                        <!-- الإجراءات -->
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewEmployee(4)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="editEmployee(4)" title="تعديل البيانات">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="printEmployee(4)" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteEmployee(4)" title="حذف الموظف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <!-- الترقيم -->
                        <td>
                            <span class="badge bg-light text-dark">6</span>
                        </td>
                        
                        <!-- الصورة الصغيرة -->
                        <td>
                            
                            <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 50px; border-radius: 0.25rem; font-size: 0.8rem;">
                                <i class="fas fa-user"></i>
                            </div>
                            
                        </td>
                        
                        <!-- رقم التسجيل -->
                        <td>
                            <strong class="text-primary">123460</strong>
                        </td>
                        
                        <!-- اللقب -->
                        <td>
                            <strong>بن أحمد</strong>
                            
                        </td>
                        
                        <!-- الاسم -->
                        <td>
                            <strong>عبد الرحمن</strong>
                            
                        </td>
                        
                        <!-- الجنس -->
                        <td>
                            
                                <span class="badge bg-primary">
                                    <i class="fas fa-mars me-1"></i>ذكر
                                </span>
                            
                        </td>
                        
                        <!-- الحالة -->
                        <td>
                            <span class="badge employee-status-badge" data-status="نشط">
                                نشط
                            </span>
                        </td>
                        
                        <!-- الرتبة -->
                        <td>
                            
                                <strong class="text-info">ملحق إدارة</strong>
                            
                        </td>
                        
                        <!-- مصلحة التعيين -->
                        <td>
                            
                                <span class="text-dark">الأمن</span>
                            
                        </td>
                        
                        <!-- الإجراءات -->
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewEmployee(5)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="editEmployee(5)" title="تعديل البيانات">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="printEmployee(5)" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteEmployee(5)" title="حذف الموظف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                </tbody>
            </table>
        </div>
        
    </div>
</div>

<!-- نافذة منبثقة لعرض الصورة -->
<div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">صورة الموظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalPhoto" src="" alt="صورة الموظف" class="img-fluid" style="max-height: 400px; border-radius: 0.5rem;">
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->

<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">6</h4>
                <p class="mb-0">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">5</h4>
                <p class="mb-0">الموظفين النشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">0</h4>
                <p class="mb-0">الموظفين المعلقين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">0</h4>
                <p class="mb-0">المتقاعدين</p>
            </div>
        </div>
    </div>
</div>


    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث التاريخ والساعة
        function updateDateTime() {
            const now = new Date();

            // تحديث التاريخ
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                calendar: 'gregory'
            };
            const arabicDate = now.toLocaleDateString('ar-DZ', dateOptions);
            document.getElementById('currentDate').textContent = arabicDate;

            // تحديث الساعة
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };
            const arabicTime = now.toLocaleTimeString('ar-DZ', timeOptions);
            document.getElementById('currentTime').textContent = arabicTime;
        }

        // تحديث فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateDateTime();
            // تحديث كل ثانية
            setInterval(updateDateTime, 1000);
        });

        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }
        
        // إغلاق الشريط الجانبي عند النقر خارجه على الهواتف
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const toggle = document.querySelector('.mobile-toggle');
                
                if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // تحديث قائمة البلديات عند تغيير الولاية
        function updateCommunes(wilayaSelect, communeSelect) {
            const wilayaId = wilayaSelect.value;
            
            // مسح البلديات الحالية
            communeSelect.innerHTML = '<option value="">اختر البلدية</option>';
            
            if (wilayaId) {
                fetch(`/api/communes/${wilayaId}`)
                    .then(response => response.json())
                    .then(communes => {
                        communes.forEach(commune => {
                            const option = document.createElement('option');
                            option.value = commune.id;
                            option.textContent = commune.name;
                            communeSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('خطأ في جلب البلديات:', error));
            }
        }
        
        // تحديث الرتب عند تغيير السلك
        function updateRanks(corpsSelect, rankSelect) {
            const corpsId = corpsSelect.value;
            
            // مسح الرتب الحالية
            rankSelect.innerHTML = '<option value="">اختر الرتبة</option>';
            
            if (corpsId) {
                fetch(`/api/ranks/${corpsId}`)
                    .then(response => response.json())
                    .then(ranks => {
                        ranks.forEach(rank => {
                            const option = document.createElement('option');
                            option.value = rank.id;
                            option.textContent = rank.name;
                            rankSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('خطأ في جلب الرتب:', error));
            }
        }
        
        // إخفاء/إظهار عدد الأطفال حسب الحالة العائلية
        function toggleChildrenCount() {
            const maritalStatus = document.getElementById('marital_status');
            const childrenGroup = document.getElementById('children_group');
            
            if (maritalStatus && childrenGroup) {
                if (maritalStatus.value === 'أعزب') {
                    childrenGroup.style.display = 'none';
                    document.getElementById('children_count').value = 0;
                } else {
                    childrenGroup.style.display = 'block';
                }
            }
        }
        
        // معاينة الصورة قبل الرفع
        function previewPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const preview = document.getElementById('photo_preview');
                    if (preview) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    }
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
    
    
<script>
// عرض تفاصيل الموظف
function viewEmployee(id) {
    window.location.href = `/employee/${id}`;
}

// تعديل الموظف
function editEmployee(id) {
    window.location.href = `/employees/${id}/edit`;
}

// حذف الموظف
function deleteEmployee(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        fetch(`/employees/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ أثناء الحذف');
            console.error('Error:', error);
        });
    }
}

// طباعة بيانات الموظف
function printEmployee(id) {
    // فتح نافذة جديدة لطباعة بيانات الموظف
    window.open(`/employees/${id}/print`, '_blank', 'width=800,height=600');
}

// عرض الصورة في نافذة منبثقة
function showPhotoModal(photoSrc, employeeName) {
    document.getElementById('modalPhoto').src = photoSrc;
    document.getElementById('photoModalLabel').textContent = `صورة الموظف: ${employeeName}`;
    
    const photoModal = new bootstrap.Modal(document.getElementById('photoModal'));
    photoModal.show();
}

// تحميل ألوان حالات الموظفين وتطبيقها
function loadEmployeeStatusColors() {
    fetch('/api/employee_statuses')
        .then(response => response.json())
        .then(statuses => {
            // إنشاء خريطة للحالات والألوان
            const statusColorMap = {};
            statuses.forEach(status => {
                statusColorMap[status.name] = status.color;
            });
            
            // تطبيق الألوان على جميع عناصر الحالة
            document.querySelectorAll('.employee-status-badge').forEach(badge => {
                const statusName = badge.getAttribute('data-status');
                const color = statusColorMap[statusName];
                
                if (color && statusName !== 'غير محدد') {
                    badge.style.backgroundColor = color;
                    badge.style.color = 'white';
                    badge.style.fontWeight = '600';
                } else {
                    // لون افتراضي للحالات غير المحددة
                    badge.style.backgroundColor = '#6c757d';
                    badge.style.color = 'white';
                }
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل ألوان الحالات:', error);
            // تطبيق ألوان افتراضية
            document.querySelectorAll('.employee-status-badge').forEach(badge => {
                const statusName = badge.getAttribute('data-status');
                
                switch(statusName) {
                    case 'نشط':
                        badge.style.backgroundColor = '#28a745';
                        break;
                    case 'معلق':
                        badge.style.backgroundColor = '#ffc107';
                        break;
                    case 'متقاعد':
                        badge.style.backgroundColor = '#17a2b8';
                        break;
                    case 'مستقيل':
                        badge.style.backgroundColor = '#6c757d';
                        break;
                    case 'متوفي':
                        badge.style.backgroundColor = '#343a40';
                        break;
                    default:
                        badge.style.backgroundColor = '#6c757d';
                }
                badge.style.color = 'white';
                badge.style.fontWeight = '600';
            });
        });
}

// تحميل الألوان عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadEmployeeStatusColors();
});
</script>

</body>
</html>