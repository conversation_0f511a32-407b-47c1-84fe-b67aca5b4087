<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .section-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .section-header.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #333;
        }
        
        .status-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            margin-bottom: 30px;
            border: 2px solid transparent;
            height: 100%;
        }
        
        .status-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            border-color: #667eea;
        }
        
        .status-icon {
            font-size: 4.5rem;
            margin-bottom: 25px;
            display: block;
        }
        
        .status-name {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .status-number {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .status-desc {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .container {
            max-width: 1200px;
        }
        
        @media (max-width: 768px) {
            .status-card {
                padding: 30px 20px;
            }
            
            .status-icon {
                font-size: 3rem;
            }
            
            .status-number {
                font-size: 2.5rem;
            }
            
            .status-name {
                font-size: 1.4rem;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- رأس الصفحة -->
        <div class="main-header">
            <h1><i class="fas fa-users-cog me-3"></i>إدارة حالات الموظفين</h1>
            <p class="mb-0 fs-5">عرض إحصائيات حالات الموظفين مصنفة حسب دخولها في التعداد الحقيقي</p>
        </div>
        
        <!-- الحالات النشطة -->
        <div class="section-header">
            <h3><i class="fas fa-users me-2"></i>الحالات التي تدخل في التعداد الحقيقي للموظفين</h3>
            <small class="fs-6">هذه الحالات تحسب ضمن العدد الفعلي للموظفين العاملين</small>
        </div>
        
        <div class="row mb-5">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-check-circle status-icon text-success"></i>
                    <div class="status-name text-success">نشط</div>
                    <div class="status-number text-success">15</div>
                    <div class="status-desc">موظف نشط</div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-user-tie status-icon text-info"></i>
                    <div class="status-name text-info">منتدب</div>
                    <div class="status-number text-info">3</div>
                    <div class="status-desc">موظف منتدب</div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-graduation-cap status-icon text-primary"></i>
                    <div class="status-name text-primary">في دراسة/تكوين</div>
                    <div class="status-number text-primary">2</div>
                    <div class="status-desc">موظف في دراسة</div>
                </div>
            </div>
        </div>
        
        <!-- الحالات غير النشطة -->
        <div class="section-header warning">
            <h3><i class="fas fa-exclamation-triangle me-2"></i>الحالات التي لا تدخل في التعداد الحقيقي للموظفين</h3>
            <small class="fs-6">هذه الحالات لا تحسب ضمن العدد الفعلي للموظفين العاملين</small>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-pause status-icon text-secondary"></i>
                    <div class="status-name text-secondary">مستودع</div>
                    <div class="status-number text-secondary">1</div>
                    <div class="status-desc">موظف مستودع</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-stop-circle status-icon text-danger"></i>
                    <div class="status-name text-danger">موقوف</div>
                    <div class="status-number text-danger">0</div>
                    <div class="status-desc">موظف موقوف</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-calendar-times status-icon text-warning"></i>
                    <div class="status-name text-warning">في عطلة طويلة الأمد</div>
                    <div class="status-number text-warning">0</div>
                    <div class="status-desc">موظف في عطلة طويلة</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-user-clock status-icon text-info"></i>
                    <div class="status-name text-info">متقاعد</div>
                    <div class="status-number text-info">5</div>
                    <div class="status-desc">موظف متقاعد</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-sign-out-alt status-icon text-secondary"></i>
                    <div class="status-name text-secondary">مستقيل</div>
                    <div class="status-number text-secondary">2</div>
                    <div class="status-desc">موظف مستقيل</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-exchange-alt status-icon text-primary"></i>
                    <div class="status-name text-primary">محول خارجياً</div>
                    <div class="status-number text-primary">1</div>
                    <div class="status-desc">موظف محول خارجياً</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="status-card">
                    <i class="fas fa-heart status-icon text-dark"></i>
                    <div class="status-name text-dark">متوفى</div>
                    <div class="status-number text-dark">0</div>
                    <div class="status-desc">موظف متوفى (رحمهم الله)</div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="row mt-5">
            <div class="col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>20</h3>
                        <p class="mb-0">إجمالي الموظفين النشطين</p>
                        <small>(يدخلون في التعداد)</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h3>9</h3>
                        <p class="mb-0">إجمالي الموظفين غير النشطين</p>
                        <small>(لا يدخلون في التعداد)</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تذييل -->
        <div class="text-center mt-5 mb-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="text-primary">🏛️ نظام إدارة موظفي الجمارك الجزائرية</h5>
                    <p class="text-muted mb-0">تم تصميم هذه الصفحة لعرض إحصائيات حالات الموظفين بشكل واضح ومنظم</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
