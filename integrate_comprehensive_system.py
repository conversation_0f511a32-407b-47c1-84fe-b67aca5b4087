#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دمج النظام الشامل لحالات الموظفين مع التطبيق الرئيسي
Integration of Comprehensive Employee Status System with Main App
"""

import sqlite3
from datetime import datetime

def integrate_with_main_app():
    """دمج النظام مع التطبيق الرئيسي"""
    print("🔗 دمج النظام الشامل مع التطبيق الرئيسي")
    print("=" * 60)
    
    # 1. إنشاء النظام
    print("1️⃣  إنشاء النظام الشامل...")
    from comprehensive_employee_status_system import create_comprehensive_status_system
    system = create_comprehensive_status_system()
    
    # 2. تحديث التطبيق الرئيسي
    print("2️⃣  تحديث app.py...")
    update_main_app()
    
    # 3. إضافة أزرار الحالات لقائمة الموظفين
    print("3️⃣  تحديث قوالب الموظفين...")
    update_employee_templates()
    
    # 4. تحديث الإحصائيات الرئيسية
    print("4️⃣  تحديث الإحصائيات...")
    update_main_statistics()
    
    print("✅ تم الدمج بنجاح!")

def update_main_app():
    """تحديث ملف app.py الرئيسي"""
    
    # قراءة ملف app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة الاستيراد
        import_line = "from employee_status_routes import register_employee_status_routes\n"
        
        if import_line not in content:
            # البحث عن مكان الاستيراد
            import_position = content.find("from flask import Flask")
            if import_position != -1:
                # إضافة الاستيراد بعد استيراد Flask
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if "from flask import Flask" in line:
                        lines.insert(i + 1, import_line.strip())
                        break
                content = '\n'.join(lines)
        
        # إضافة تسجيل المسارات
        register_line = "register_employee_status_routes(app)"
        
        if register_line not in content:
            # البحث عن مكان التسجيل (قبل if __name__ == "__main__")
            main_position = content.find('if __name__ == "__main__"')
            if main_position != -1:
                content = content[:main_position] + f"\n# تسجيل مسارات حالات الموظفين\n{register_line}\n\n" + content[main_position:]
        
        # حفظ الملف المحدث
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث app.py")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث app.py: {e}")

def update_employee_templates():
    """تحديث قوالب الموظفين لإضافة أزرار الحالات"""
    
    # تحديث قالب قائمة الموظفين
    template_path = 'templates/employees/list.html'
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة أزرار الحالات
        status_buttons = '''
                                    <!-- أزرار الحالات -->
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-secondary dropdown-toggle" 
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fas fa-user-cog"></i> الحالات
                                        </button>
                                        <div class="dropdown-menu">
                                            <h6 class="dropdown-header">الحالات المؤقتة</h6>
                                            <a class="dropdown-item" href="{{ url_for('employee_status.add_leave_of_absence', employee_id=employee.id) }}">
                                                <i class="fas fa-user-clock text-warning"></i> استيداع
                                            </a>
                                            <a class="dropdown-item" href="{{ url_for('employee_status.add_suspension', employee_id=employee.id) }}">
                                                <i class="fas fa-user-times text-danger"></i> توقيف
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <h6 class="dropdown-header">الحالات النهائية</h6>
                                            <a class="dropdown-item" href="{{ url_for('employee_status.add_death', employee_id=employee.id) }}">
                                                <i class="fas fa-cross text-dark"></i> وفاة
                                            </a>
                                            <a class="dropdown-item" href="{{ url_for('employee_status.add_external_transfer', employee_id=employee.id) }}">
                                                <i class="fas fa-exchange-alt text-warning"></i> تحويل خارجي
                                            </a>
                                        </div>
                                    </div>
        '''
        
        # البحث عن مكان إضافة الأزرار (بعد أزرار التحرير والحذف)
        if 'أزرار الحالات' not in content:
            # البحث عن أزرار الإجراءات الموجودة
            action_pattern = '<a href="{{ url_for(\'edit_employee\', id=employee.id) }}"'
            if action_pattern in content:
                content = content.replace(action_pattern, status_buttons + '\n                                    ' + action_pattern)
        
        # حفظ الملف المحدث
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث قالب قائمة الموظفين")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث القالب: {e}")

def update_main_statistics():
    """تحديث الإحصائيات الرئيسية"""
    
    # تحديث دالة get_updated_employee_stats في app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة الإحصائيات وتحديثها
        new_stats_function = '''
def get_comprehensive_employee_stats():
    """إحصائيات شاملة للموظفين مع النظام الجديد"""
    from comprehensive_employee_status_system import ComprehensiveEmployeeStatusSystem
    
    system = ComprehensiveEmployeeStatusSystem()
    stats = system.get_employee_statistics()
    
    # إضافة إحصائيات إضافية
    conn = get_db_connection()
    
    # الموظفين حسب الجنس
    stats['male_employees'] = conn.execute(
        "SELECT COUNT(*) FROM employees WHERE gender = 'ذكر'"
    ).fetchone()[0]
    
    stats['female_employees'] = conn.execute(
        "SELECT COUNT(*) FROM employees WHERE gender = 'أنثى'"
    ).fetchone()[0]
    
    conn.close()
    return stats
'''
        
        # إضافة الدالة الجديدة إذا لم تكن موجودة
        if 'get_comprehensive_employee_stats' not in content:
            # البحث عن مكان إضافة الدالة
            stats_position = content.find('def get_updated_employee_stats():')
            if stats_position != -1:
                content = content[:stats_position] + new_stats_function + '\n\n' + content[stats_position:]
        
        # حفظ الملف
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث دالة الإحصائيات")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإحصائيات: {e}")

def add_navigation_menu():
    """إضافة قائمة التنقل للنظام الجديد"""
    
    template_path = 'templates/base.html'
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة عنصر قائمة جديد
        menu_item = '''
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="statusDropdown" role="button" 
                               data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-users-cog"></i>
                                حالات الموظفين
                            </a>
                            <div class="dropdown-menu" aria-labelledby="statusDropdown">
                                <a class="dropdown-item" href="{{ url_for('employee_status.index') }}">
                                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                                </a>
                                <div class="dropdown-divider"></div>
                                <h6 class="dropdown-header">الحالات النهائية</h6>
                                <a class="dropdown-item" href="{{ url_for('employee_status.deceased_employees') }}">
                                    <i class="fas fa-cross"></i> المتوفين
                                </a>
                                <a class="dropdown-item" href="{{ url_for('employee_status.external_transfers') }}">
                                    <i class="fas fa-exchange-alt"></i> المحولين خارجياً
                                </a>
                                <a class="dropdown-item" href="{{ url_for('employee_status.retired_employees') }}">
                                    <i class="fas fa-user-check"></i> المتقاعدين
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url_for('employee_status.manage_leave_reasons') }}">
                                    <i class="fas fa-cog"></i> إعدادات الاستيداع
                                </a>
                            </div>
                        </li>
        '''
        
        # البحث عن مكان إضافة القائمة
        if 'حالات الموظفين' not in content:
            # البحث عن قائمة الموظفين الموجودة
            employees_menu = '<a class="nav-link" href="{{ url_for(\'employees\') }}">'
            if employees_menu in content:
                # إضافة القائمة الجديدة بعد قائمة الموظفين
                content = content.replace('</li>\n                    </ul>', menu_item + '\n                    </ul>')
        
        # حفظ الملف
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة قائمة التنقل")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة قائمة التنقل: {e}")

def create_integration_summary():
    """إنشاء ملخص التكامل"""
    
    summary = """
# 🎯 ملخص دمج النظام الشامل لحالات الموظفين

## ✅ ما تم إنجازه:

### 1. 📊 النظام الأساسي:
- ✅ إنشاء 11 جدول جديد لإدارة الحالات
- ✅ تصنيف الحالات إلى مؤقتة ونهائية
- ✅ نظام تتبع تاريخي شامل

### 2. 🔄 الحالات المؤقتة (تبقى في جدول الموظفين):
- ✅ الاستيداع (مع حساب الرصيد 5 سنوات)
- ✅ التوقيف
- ✅ الاستقالة
- ✅ الخدمة الوطنية
- ✅ عطلة طويلة الأمد
- ✅ الانتداب
- ✅ الدراسة/التكوين

### 3. 🗑️ الحالات النهائية (تُنقل لجداول منفصلة):
- ✅ الوفاة → جدول deceased_employees
- ✅ التحويل الخارجي → جدول external_transfer_employees
- ✅ العزل → جدول dismissed_employees
- ✅ التقاعد → جدول retired_employees

### 4. 🌐 الواجهات:
- ✅ لوحة تحكم شاملة
- ✅ نماذج إضافة الحالات
- ✅ عرض الحالات المحذوفة
- ✅ إدارة أسباب الاستيداع

### 5. 📈 الإحصائيات:
- ✅ إحصائيات مفصلة لكل حالة
- ✅ فصل بين النشطين والمحذوفين
- ✅ API للبيانات

## 🚀 للاستخدام:

### 1. تشغيل النظام:
```bash
python integrate_comprehensive_system.py
python app.py
```

### 2. الوصول للنظام:
- الصفحة الرئيسية: http://localhost:5000/
- لوحة الحالات: http://localhost:5000/employee_status/
- المتوفين: http://localhost:5000/employee_status/deceased
- المحولين: http://localhost:5000/employee_status/external_transfers

### 3. الميزات الرئيسية:
- ✅ حساب رصيد الاستيداع تلقائياً
- ✅ نقل الموظفين المحذوفين لجداول منفصلة
- ✅ الاحتفاظ بجميع البيانات
- ✅ تتبع تاريخ التغييرات
- ✅ إحصائيات دقيقة

## 📋 الملفات المنشأة:
1. comprehensive_employee_status_system.py - النظام الأساسي
2. employee_status_routes.py - المسارات والواجهات
3. templates/employee_status/ - القوالب
4. integrate_comprehensive_system.py - ملف التكامل

## 🎉 النظام جاهز للاستخدام الكامل!
"""
    
    with open('COMPREHENSIVE_STATUS_INTEGRATION.md', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print("✅ تم إنشاء ملخص التكامل")

def main():
    """الدالة الرئيسية للتكامل"""
    print("🌟 دمج النظام الشامل لحالات الموظفين")
    print("=" * 70)
    
    try:
        # تنفيذ التكامل
        integrate_with_main_app()
        
        # إضافة قائمة التنقل
        add_navigation_menu()
        
        # إنشاء ملخص التكامل
        create_integration_summary()
        
        print(f"\n🎉 تم الدمج بنجاح!")
        print(f"🚀 لتشغيل النظام:")
        print(f"   python app.py")
        
        print(f"\n🌐 الروابط المهمة:")
        print(f"   - لوحة الحالات: http://localhost:5000/employee_status/")
        print(f"   - المتوفين: http://localhost:5000/employee_status/deceased")
        print(f"   - المحولين خارجياً: http://localhost:5000/employee_status/external_transfers")
        print(f"   - إعدادات الاستيداع: http://localhost:5000/employee_status/settings/leave_reasons")
        
    except Exception as e:
        print(f"❌ خطأ في التكامل: {e}")

if __name__ == "__main__":
    main()
"""