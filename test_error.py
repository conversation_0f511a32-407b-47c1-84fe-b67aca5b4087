#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار للعثور على خطأ UndefinedError
"""

from app import app

def test_view_error():
    """اختبار صفحة المعاينة للعثور على الخطأ"""
    print("🔍 البحث عن خطأ UndefinedError...")
    
    with app.test_client() as client:
        try:
            # اختبار صفحة عرض التفاصيل
            response = client.get('/employee/1')
            
            if response.status_code == 200:
                print("✅ صفحة المعاينة تعمل بدون أخطاء")
                content = response.data.decode('utf-8')
                print(f"📄 حجم المحتوى: {len(content)} حرف")
            elif response.status_code == 302:
                print("✅ صفحة المعاينة تعمل (توجيه لعدم وجود الموظف)")
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في التطبيق: {e}")
            print(f"نوع الخطأ: {type(e).__name__}")
            
            # إذا كان الخطأ UndefinedError، سنحاول معرفة المتغير المفقود
            if "UndefinedError" in str(e):
                print(f"🔍 تفاصيل خطأ UndefinedError: {e}")

if __name__ == "__main__":
    test_view_error()