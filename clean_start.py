#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بداية نظيفة - نظام إدارة موظفي الجمارك الجزائرية
Clean Start - Algerian Customs Employee Management System
"""

import os
import sys
import shutil

def clean_cache():
    """تنظيف ملفات التخزين المؤقت"""
    print("🧹 تنظيف ملفات التخزين المؤقت...")
    
    # حذف مجلد __pycache__
    if os.path.exists('__pycache__'):
        try:
            shutil.rmtree('__pycache__')
            print("✅ تم حذف __pycache__")
        except Exception as e:
            print(f"❌ خطأ في حذف __pycache__: {e}")
    
    # حذف ملفات .pyc
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                    print(f"✅ تم حذف {file}")
                except Exception as e:
                    print(f"❌ خطأ في حذف {file}: {e}")

def create_minimal_app():
    """إنشاء تطبيق مبسط"""
    print("🔧 إنشاء تطبيق مبسط...")
    
    app_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية - نسخة مبسطة
Simplified Algerian Customs Employee Management System
"""

from flask import Flask, render_template
import sqlite3
import os

app = Flask(__name__)
app.secret_key = 'simple_customs_2025'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    stats = {'total_employees': 0, 'active_leaves': 0, 'pending_certificates': 0, 'recent_training': 0}
    return render_template('dashboard.html', stats=stats)

@app.route('/employees')
def employees():
    return render_template('employees/index.html', employees=[])

@app.route('/leaves')
def leaves():
    return render_template('leaves/index.html')

@app.route('/certificates')
def certificates():
    return render_template('certificates/index.html')

@app.route('/employee_statuses')
def employee_statuses():
    return render_template('employee_statuses/index.html')

@app.route('/settings')
def settings():
    return render_template('settings/index.html')

@app.route('/promotions')
def promotions():
    return render_template('promotions/index.html')

@app.route('/transfers')
def transfers():
    return render_template('transfers/index.html')

@app.route('/statistics')
def statistics():
    return render_template('statistics/index.html')

@app.route('/sanctions')
def sanctions():
    return render_template('sanctions/index.html')

if __name__ == '__main__':
    print("🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - نسخة مبسطة")
    print("🌐 http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
'''
    
    with open('simple_app.py', 'w', encoding='utf-8') as f:
        f.write(app_content)
    
    print("✅ تم إنشاء simple_app.py")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧹 أداة البداية النظيفة")
    print("🧹 Clean Start Tool")
    print("=" * 60)
    
    # تنظيف ملفات التخزين المؤقت
    clean_cache()
    
    # إنشاء تطبيق مبسط
    create_minimal_app()
    
    print("\n✅ تم الانتهاء من التنظيف")
    print("✅ Cleanup completed")
    
    print("\n🚀 لتشغيل النظام:")
    print("🚀 To run the system:")
    print("   python simple_app.py")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    main()
