#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد وتفعيل نظام الحالات الخاصة
"""

import sqlite3
import os
from datetime import datetime

def create_special_status_tables():
    """إنشاء جداول الحالات الخاصة"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        print("🔧 إنشاء جداول الحالات الخاصة...")
        
        # جدول الاستقالات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_resignations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                resignation_date DATE NOT NULL,
                reason TEXT,
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                notes TEXT,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIG<PERSON> KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول الوفيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_deaths (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                death_date DATE NOT NULL,
                death_place TEXT,
                death_cause TEXT,
                certificate_number TEXT,
                burial_place TEXT,
                family_contact TEXT,
                decision_number TEXT,
                decision_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول التقاعد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_retirements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                retirement_date DATE NOT NULL,
                retirement_type TEXT NOT NULL,
                years_of_service INTEGER,
                pension_amount DECIMAL(10,2),
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول التحويل الخارجي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_external_transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                transfer_date DATE NOT NULL,
                destination_organization TEXT NOT NULL,
                destination_position TEXT,
                reason TEXT,
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_leave_of_absence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE,
                duration_months INTEGER,
                reason TEXT NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                status TEXT DEFAULT 'active',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول التوقيف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_suspensions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                suspension_date DATE NOT NULL,
                end_date DATE,
                reason TEXT NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                status TEXT DEFAULT 'active',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول الانتداب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_assignments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                assignment_date DATE NOT NULL,
                end_date DATE,
                destination TEXT NOT NULL,
                position TEXT,
                decision_number TEXT,
                decision_date DATE,
                status TEXT DEFAULT 'active',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء جداول الحالات الخاصة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def check_tables():
    """فحص الجداول المنشأة"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        tables = [
            'employee_resignations',
            'employee_deaths', 
            'employee_retirements',
            'employee_external_transfers',
            'employee_leave_of_absence',
            'employee_suspensions',
            'employee_assignments'
        ]
        
        print("\n📋 فحص جداول الحالات الخاصة:")
        print("=" * 50)
        
        all_exist = True
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            exists = cursor.fetchone()
            
            if exists:
                print(f"✅ {table}")
                # عد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   📊 عدد السجلات: {count}")
            else:
                print(f"❌ {table} - غير موجود")
                all_exist = False
        
        conn.close()
        return all_exist
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

def test_system():
    """اختبار النظام"""
    try:
        print("\n🧪 اختبار النظام...")
        
        # اختبار استيراد الوحدات
        try:
            from special_status_manager import SpecialStatusManager
            from special_status_routes import special_status_bp
            print("✅ تم استيراد الوحدات بنجاح")
        except ImportError as e:
            print(f"❌ خطأ في استيراد الوحدات: {e}")
            return False
        
        # اختبار إنشاء المدير
        try:
            manager = SpecialStatusManager()
            stats = manager.get_statistics()
            print("✅ تم إنشاء مدير الحالات الخاصة بنجاح")
            print(f"📊 الإحصائيات: {stats}")
        except Exception as e:
            print(f"❌ خطأ في إنشاء المدير: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد نظام الحالات الخاصة للموظفين")
    print("=" * 60)
    
    # إنشاء الجداول
    if not create_special_status_tables():
        print("❌ فشل في إنشاء الجداول")
        return
    
    # فحص الجداول
    if not check_tables():
        print("❌ فشل في فحص الجداول")
        return
    
    # اختبار النظام
    if not test_system():
        print("❌ فشل في اختبار النظام")
        return
    
    print("\n🎉 تم إعداد نظام الحالات الخاصة بنجاح!")
    print("=" * 60)
    print("🌐 يمكنك الآن الوصول إلى النظام من خلال:")
    print("   - الرابط: http://localhost:5000/special_status/")
    print("   - الشريط الجانبي: الحالات الخاصة")
    print("\n📋 الحالات المتاحة:")
    print("   - الاستقالات")
    print("   - الوفيات") 
    print("   - التقاعد")
    print("   - التحويل الخارجي")
    print("   - الاستيداع")
    print("   - التوقيف")
    print("   - الانتداب")

if __name__ == '__main__':
    main()
