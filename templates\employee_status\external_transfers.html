{% extends "base.html" %}

{% block title %}التحويلات الخارجية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        التحويلات الخارجية
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ transfers|length }} تحويل</span>
                    </div>
                </div>
                <div class="card-body">
                    {% if transfers %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> الموظفون المحولون خارجياً لا يظهرون في تعداد المؤسسة الأساسي.
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="transfersTable">
                            <thead>
                                <tr>
                                    <th>رقم التسجيل</th>
                                    <th>الاسم الكامل</th>
                                    <th>الرتبة</th>
                                    <th>تاريخ التحويل</th>
                                    <th>الجهة المحول إليها</th>
                                    <th>القسم</th>
                                    <th>السبب</th>
                                    <th>رقم القرار</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in transfers %}
                                <tr>
                                    <td>{{ transfer.registration_number }}</td>
                                    <td>{{ transfer.first_name }} {{ transfer.last_name }}</td>
                                    <td>{{ transfer.rank_name or 'غير محدد' }}</td>
                                    <td>{{ transfer.transfer_date|format_arabic_date }}</td>
                                    <td>{{ transfer.destination_organization }}</td>
                                    <td>{{ transfer.destination_department or 'غير محدد' }}</td>
                                    <td>{{ transfer.reason or 'غير محدد' }}</td>
                                    <td>{{ transfer.decision_number or 'غير محدد' }}</td>
                                    <td>
                                        <a href="{{ url_for('employee_status_history', employee_id=transfer.employee_id) }}" 
                                           class="btn btn-sm btn-info" title="عرض تاريخ الحالات">
                                            <i class="fas fa-history"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>لا توجد تحويلات خارجية</h4>
                        <p>لم يتم تسجيل أي تحويلات خارجية حتى الآن.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#transfersTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 3, "desc" ]], // ترتيب حسب تاريخ التحويل
        "pageLength": 25
    });
});
</script>
{% endblock %}