<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة خيارات التصميم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .design-option { margin: 30px 0; padding: 20px; border-radius: 15px; cursor: pointer; transition: all 0.3s; }
        .design-option:hover { transform: scale(1.02); }
        
        /* الخيار الأول: عصري زاهي */
        .option-1 {
            background: linear-gradient(135deg, #74b9ff 0%, #00cec9 50%, #fd79a8 100%);
            color: white;
        }
        .option-1 .preview-card {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin: 10px;
        }
        
        /* الخيار الثاني: داكن أنيق */
        .option-2 {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 50%, #ddd 100%);
            color: #fdcb6e;
        }
        .option-2 .preview-card {
            background: rgba(0,0,0,0.7);
            border: 1px solid #fdcb6e;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 0 20px rgba(253, 203, 110, 0.3);
        }
        
        /* الخيار الثالث: متدرج ملون */
        .option-3 {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
            background-size: 400% 400%;
            animation: gradientShift 4s ease infinite;
            color: white;
        }
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .option-3 .preview-card {
            background: rgba(255,255,255,0.15);
            border-radius: 25px;
            padding: 20px;
            margin: 10px;
            backdrop-filter: blur(15px);
        }
        
        /* الخيار الرابع: مينيمال */
        .option-4 {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            border: 2px solid #dee2e6;
        }
        .option-4 .preview-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* الخيار الخامس: جزائري تراثي */
        .option-5 {
            background: linear-gradient(135deg, #00b894 0%, #ffffff 50%, #e17055 100%);
            color: #2d3436;
            position: relative;
        }
        .option-5::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="islamic" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,0 20,10 10,20 0,10" fill="rgba(0,0,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23islamic)"/></svg>');
            opacity: 0.3;
        }
        .option-5 .preview-card {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            position: relative;
            z-index: 2;
        }
        
        .select-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.5);
            color: inherit;
            padding: 10px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s;
        }
        .select-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1>🎨 اختر التصميم المفضل لديك</h1>
            <p class="lead">انقر على التصميم الذي تريده لنظام إدارة موظفي الجمارك الجزائرية</p>
        </div>
        
        <!-- الخيار الأول -->
        <div class="design-option option-1" onclick="selectDesign(1)">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-palette"></i> التصميم العصري الزاهي</h3>
                    <p>ألوان زاهية ومشرقة مع تأثيرات عصرية وأنيقة</p>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h6>الموظفين</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <h6>العطل</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                                <h6>الشهادات</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <button class="select-btn">اختيار هذا التصميم</button>
                </div>
            </div>
        </div>
        
        <!-- الخيار الثاني -->
        <div class="design-option option-2" onclick="selectDesign(2)">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-moon"></i> التصميم الداكن الأنيق</h3>
                    <p>تصميم داكن مع لمسات ذهبية وتأثيرات نيون</p>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h6>الموظفين</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <h6>العطل</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                                <h6>الشهادات</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <button class="select-btn">اختيار هذا التصميم</button>
                </div>
            </div>
        </div>
        
        <!-- الخيار الثالث -->
        <div class="design-option option-3" onclick="selectDesign(3)">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-rainbow"></i> التصميم المتدرج الملون</h3>
                    <p>تدرجات ملونة متحركة مع تأثيرات ثلاثية الأبعاد</p>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h6>الموظفين</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <h6>العطل</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                                <h6>الشهادات</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <button class="select-btn">اختيار هذا التصميم</button>
                </div>
            </div>
        </div>
        
        <!-- الخيار الرابع -->
        <div class="design-option option-4" onclick="selectDesign(4)">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-feather"></i> التصميم المينيمال الأنيق</h3>
                    <p>بساطة وأناقة مع مساحات بيضاء وخطوط نظيفة</p>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-users fa-2x mb-2 text-primary"></i>
                                <h6>الموظفين</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-calendar fa-2x mb-2 text-success"></i>
                                <h6>العطل</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-graduation-cap fa-2x mb-2 text-info"></i>
                                <h6>الشهادات</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <button class="select-btn">اختيار هذا التصميم</button>
                </div>
            </div>
        </div>
        
        <!-- الخيار الخامس -->
        <div class="design-option option-5" onclick="selectDesign(5)">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3><i class="fas fa-mosque"></i> التصميم الجزائري التراثي</h3>
                    <p>مستوحى من التراث الجزائري مع ألوان العلم والزخارف الإسلامية</p>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-users fa-2x mb-2 text-success"></i>
                                <h6>الموظفين</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-calendar fa-2x mb-2 text-danger"></i>
                                <h6>العطل</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-card text-center">
                                <i class="fas fa-graduation-cap fa-2x mb-2 text-dark"></i>
                                <h6>الشهادات</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <button class="select-btn">اختيار هذا التصميم</button>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-5">
            <p class="text-muted">أو يمكنك طلب تصميم مخصص حسب رغبتك</p>
            <button class="btn btn-outline-primary" onclick="customDesign()">
                <i class="fas fa-paint-brush"></i> تصميم مخصص
            </button>
        </div>
    </div>
    
    <script>
        function selectDesign(option) {
            const designs = {
                1: 'التصميم العصري الزاهي',
                2: 'التصميم الداكن الأنيق', 
                3: 'التصميم المتدرج الملون',
                4: 'التصميم المينيمال الأنيق',
                5: 'التصميم الجزائري التراثي'
            };
            
            if(confirm('هل تريد تطبيق ' + designs[option] + ' على النظام؟')) {
                alert('ممتاز! سيتم تطبيق ' + designs[option] + ' على نظام إدارة موظفي الجمارك الجزائرية');
                // هنا سيتم تطبيق التصميم المختار
            }
        }
        
        function customDesign() {
            alert('يمكنك وصف التصميم الذي تريده وسأقوم بإنشائه لك!');
        }
    </script>
</body>
</html>
