{% extends "base.html" %}

{% block title %}تسجيل وفاة - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cross"></i>
                        تسجيل وفاة موظف
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- تحذير مهم -->
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم:</h6>
                        <p class="mb-1">• سيتم نقل الموظف من جدول الموظفين النشطين إلى جدول الوفيات</p>
                        <p class="mb-1">• لن يُحسب الموظف في إجمالي عدد الموظفين النشطين</p>
                        <p class="mb-0">• سيتم الاحتفاظ بجميع بياناته في سجل منفصل</p>
                    </div>

                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user"></i> معلومات الموظف:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                                <p class="mb-1"><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الحالة الحالية:</strong> {{ employee.status }}</p>
                                {% if employee.birth_date %}
                                <p class="mb-0"><strong>تاريخ الميلاد:</strong> {{ employee.birth_date }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <form method="POST" onsubmit="return confirmDeath()">
                        <div class="row">
                            <!-- تاريخ الوفاة -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="death_date">
                                        <i class="fas fa-calendar-alt"></i>
                                        تاريخ الوفاة <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="death_date" 
                                           name="death_date" required max="{{ today }}">
                                    <small class="form-text text-muted">
                                        يجب أن يكون التاريخ في الماضي أو اليوم
                                    </small>
                                </div>
                            </div>

                            <!-- سبب الوفاة -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="death_cause">
                                        <i class="fas fa-question-circle"></i>
                                        سبب الوفاة <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="death_cause" name="death_cause" required>
                                        <option value="">اختر السبب...</option>
                                        <option value="عادية">
                                            <i class="fas fa-heart"></i> وفاة عادية
                                        </option>
                                        <option value="حادث عمل">
                                            <i class="fas fa-exclamation-triangle"></i> حادث عمل
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- رقم شهادة الوفاة -->
                        <div class="form-group">
                            <label for="death_certificate_number">
                                <i class="fas fa-file-alt"></i>
                                رقم شهادة الوفاة <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="death_certificate_number" 
                                   name="death_certificate_number" required
                                   placeholder="أدخل رقم شهادة الوفاة الرسمية">
                            <small class="form-text text-muted">
                                رقم شهادة الوفاة الصادرة من الجهات الرسمية
                            </small>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-group">
                            <label for="notes">
                                <i class="fas fa-sticky-note"></i>
                                ملاحظات إضافية (اختياري)
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="أي ملاحظات إضافية حول الوفاة..."></textarea>
                        </div>

                        <!-- تأكيد العملية -->
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_death" required>
                                <label class="custom-control-label text-danger" for="confirm_death">
                                    <strong>أؤكد أن المعلومات المدخلة صحيحة وأن الموظف قد توفي فعلاً</strong>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-cross"></i>
                                تسجيل الوفاة ونقل السجل
                            </button>
                            <a href="{{ url_for('employees') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تعيين التاريخ الحالي كحد أقصى
document.getElementById('death_date').max = new Date().toISOString().split('T')[0];

function confirmDeath() {
    const employeeName = "{{ employee.first_name }} {{ employee.last_name }}";
    const deathDate = document.getElementById('death_date').value;
    const deathCause = document.getElementById('death_cause').value;
    const certificateNumber = document.getElementById('death_certificate_number').value;
    
    const confirmMessage = `
هل أنت متأكد من تسجيل وفاة الموظف؟

الموظف: ${employeeName}
تاريخ الوفاة: ${deathDate}
السبب: ${deathCause}
رقم الشهادة: ${certificateNumber}

تحذير: سيتم نقل الموظف لجدول الوفيات وحذفه من قائمة الموظفين النشطين.
هذا الإجراء لا يمكن التراجع عنه بسهولة.
    `;
    
    return confirm(confirmMessage);
}

// التحقق من صحة التاريخ
document.getElementById('death_date').addEventListener('change', function() {
    const selectedDate = new Date(this.value);
    const today = new Date();
    
    if (selectedDate > today) {
        alert('تاريخ الوفاة لا يمكن أن يكون في المستقبل');
        this.value = '';
    }
});

// تحسين عرض سبب الوفاة
document.getElementById('death_cause').addEventListener('change', function() {
    const causeDiv = document.createElement('div');
    causeDiv.className = 'mt-2';
    
    // إزالة أي رسالة سابقة
    const existingDiv = this.parentNode.querySelector('.cause-info');
    if (existingDiv) {
        existingDiv.remove();
    }
    
    if (this.value === 'حادث عمل') {
        causeDiv.className += ' alert alert-warning cause-info';
        causeDiv.innerHTML = `
            <small>
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> في حالة حادث العمل، قد تكون هناك إجراءات إضافية مطلوبة من قبل التأمين والجهات المختصة.
            </small>
        `;
        this.parentNode.appendChild(causeDiv);
    }
});
</script>

<style>
.custom-control-label {
    font-weight: bold;
}

.alert-danger {
    border-left: 4px solid #dc3545;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}
</style>
{% endblock %}