{% extends "base.html" %}

{% block title %}إدارة حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة حالات الموظفين{% endblock %}

{% block content %}
<!-- أزرار العمليات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button type="button" class="btn btn-primary" onclick="showAddStatusModal()">
                    <i class="fas fa-plus me-2"></i>إضافة حالة جديدة
                </button>
                <button class="btn btn-success" onclick="exportData()">
                    <i class="fas fa-file-excel me-2"></i>تصدير البيانات
                </button>
            </div>
            <div class="text-muted">
                <small>إجمالي الحالات: {{ statuses|length }}</small>
            </div>
        </div>
    </div>
</div>

<!-- قائمة حالات الموظفين -->
<div class="row">
    {% for status in statuses %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-{{ status.color_class }}">
            <div class="card-header bg-{{ status.color_class }} text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-{{ status.icon }} me-2"></i>{{ status.name }}
                    </h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="editStatus({{ status.id }}, '{{ status.name }}', '{{ status.description }}', '{{ status.color_class }}', '{{ status.icon }}')">
                                <i class="fas fa-edit me-2"></i>تعديل
                            </a></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteStatus({{ status.id }}, '{{ status.name }}')">
                                <i class="fas fa-trash me-2"></i>حذف
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="text-center py-3">
                    <h3 class="text-{{ status.color_class }}">{{ status_stats.get(status.name, 0) }}</h3>
                    <p class="text-muted">موظف بهذه الحالة</p>
                </div>
                {% if status.description %}
                <p class="card-text small text-muted">{{ status.description }}</p>
                {% endif %}
                <div class="d-grid">
                    <a href="{{ url_for('employees') }}?status={{ status.name }}" class="btn btn-outline-{{ status.color_class }} btn-sm">
                        <i class="fas fa-users me-2"></i>عرض الموظفين
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Modal إضافة/تعديل حالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">
                    <i class="fas fa-plus me-2"></i>إضافة حالة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <input type="hidden" id="status_id" name="status_id">
                    
                    <div class="mb-3">
                        <label class="form-label">اسم الحالة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="status_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="status_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اللون</label>
                                <select class="form-select" id="status_color" name="color_class">
                                    <option value="primary">أزرق (Primary)</option>
                                    <option value="success">أخضر (Success)</option>
                                    <option value="warning">أصفر (Warning)</option>
                                    <option value="danger">أحمر (Danger)</option>
                                    <option value="info">فيروزي (Info)</option>
                                    <option value="secondary">رمادي (Secondary)</option>
                                    <option value="dark">أسود (Dark)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الأيقونة</label>
                                <select class="form-select" id="status_icon" name="icon">
                                    <option value="check-circle">✓ نشط</option>
                                    <option value="pause-circle">⏸ معلق</option>
                                    <option value="exchange-alt">⇄ تحويل</option>
                                    <option value="user-tie">👔 منتدب</option>
                                    <option value="pause">⏸ استيداع</option>
                                    <option value="times-circle">✗ متوفي</option>
                                    <option value="user-times">👤✗ مفصول</option>
                                    <option value="sign-out-alt">🚪 مستقيل</option>
                                    <option value="stop-circle">⏹ موقف</option>
                                    <option value="info-circle">ℹ معلومات</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معاينة الحالة -->
                    <div class="mb-3">
                        <label class="form-label">معاينة</label>
                        <div class="p-3 border rounded">
                            <span class="badge fs-6 px-3 py-2" id="status_preview">
                                <i class="fas fa-info-circle me-2"></i>معاينة الحالة
                            </span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="statusSubmitBtn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف حالة "<span id="delete_status_name"></span>"؟</p>
                <p class="text-danger small">تنبيه: لا يمكن حذف الحالة إذا كانت مستخدمة من قبل أي موظف.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentStatusId = null;

// إظهار modal إضافة حالة جديدة
function showAddStatusModal() {
    currentStatusId = null;
    document.getElementById('statusModalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>إضافة حالة جديدة';
    document.getElementById('statusSubmitBtn').textContent = 'إضافة';
    document.getElementById('statusForm').reset();
    document.getElementById('status_id').value = '';
    updatePreview();
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

// تعديل حالة
function editStatus(id, name, description, colorClass, icon) {
    currentStatusId = id;
    document.getElementById('statusModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل الحالة';
    document.getElementById('statusSubmitBtn').textContent = 'تحديث';
    
    document.getElementById('status_id').value = id;
    document.getElementById('status_name').value = name;
    document.getElementById('status_description').value = description || '';
    document.getElementById('status_color').value = colorClass;
    document.getElementById('status_icon').value = icon;
    
    updatePreview();
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

// حذف حالة
function deleteStatus(id, name) {
    currentStatusId = id;
    document.getElementById('delete_status_name').textContent = name;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// تحديث معاينة الحالة
function updatePreview() {
    const name = document.getElementById('status_name').value || 'معاينة الحالة';
    const color = document.getElementById('status_color').value;
    const icon = document.getElementById('status_icon').value;
    
    const preview = document.getElementById('status_preview');
    preview.className = `badge bg-${color} fs-6 px-3 py-2`;
    preview.innerHTML = `<i class="fas fa-${icon} me-2"></i>${name}`;
}

// معالجة إرسال النموذج
document.getElementById('statusForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    const url = currentStatusId ? `/employee_statuses/edit/${currentStatusId}` : '/employee_statuses/add';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في العملية');
    });
});

// تأكيد الحذف
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (currentStatusId) {
        fetch(`/employee_statuses/delete/${currentStatusId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                location.reload();
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في العملية');
        });
    }
});

// تحديث المعاينة عند تغيير القيم
document.getElementById('status_name').addEventListener('input', updatePreview);
document.getElementById('status_color').addEventListener('change', updatePreview);
document.getElementById('status_icon').addEventListener('change', updatePreview);

// تصدير البيانات
function exportData() {
    window.open('/api/employee_statuses?export=excel', '_blank');
}

// تحديث المعاينة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
});
</script>
{% endblock %}
