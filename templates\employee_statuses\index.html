{% extends "base.html" %}

{% block title %}إدارة حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة حالات الموظفين{% endblock %}

{% block content %}
<style>
/* تصميم جميل ومرتب */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.main-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.section-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
}

.section-header.inactive {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.status-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 3px solid transparent;
}

.status-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0,0,0,0.2);
    border-color: #667eea;
}

.status-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    display: block;
}

.status-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 15px;
}

.status-count {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.status-desc {
    color: #6c757d;
    font-size: 1rem;
}

/* ألوان الحالات */
.status-active { color: #28a745; }
.status-assignment { color: #17a2b8; }
.status-study { color: #007bff; }
.status-leave { color: #6c757d; }
.status-suspension { color: #dc3545; }
.status-long-leave { color: #ffc107; }
.status-retirement { color: #17a2b8; }
.status-resignation { color: #6c757d; }
.status-transfer { color: #007bff; }
.status-death { color: #343a40; }
</style>

<div class="main-container">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1><i class="fas fa-users-cog"></i> إدارة حالات الموظفين</h1>
        <p>انقر على أي حالة للدخول إلى صفحة إدارتها والاطلاع على التفاصيل</p>
    </div>

    <!-- الحالات النشطة -->
    <div class="section-header">
        <h3><i class="fas fa-users"></i> الحالات التي تدخل في التعداد الحقيقي للموظفين</h3>
        <p>هذه الحالات تحسب ضمن العدد الفعلي للموظفين العاملين</p>
    </div>

    <div class="status-grid">
        <!-- نشط -->
        <a href="/employee_status/active" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-check-circle status-icon status-active"></i>
                <div class="status-title status-active">نشط</div>
                <div class="status-count status-active">{{ active_count or 0 }}</div>
                <div class="status-desc">موظف نشط</div>
            </div>
        </a>

        <!-- منتدب -->
        <a href="/employee_status/assignment" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-user-tie status-icon status-assignment"></i>
                <div class="status-title status-assignment">منتدب</div>
                <div class="status-count status-assignment">{{ assignment_count or 0 }}</div>
                <div class="status-desc">موظف منتدب</div>
            </div>
        </a>

        <!-- في دراسة/تكوين -->
        <a href="/employee_status/study" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-graduation-cap status-icon status-study"></i>
                <div class="status-title status-study">في دراسة/تكوين</div>
                <div class="status-count status-study">{{ study_count or 0 }}</div>
                <div class="status-desc">موظف في دراسة</div>
            </div>
        </a>
    </div>

    <!-- الحالات غير النشطة -->
    <div class="section-header inactive">
        <h3><i class="fas fa-exclamation-triangle"></i> الحالات التي لا تدخل في التعداد الحقيقي للموظفين</h3>
        <p>هذه الحالات لا تحسب ضمن العدد الفعلي للموظفين العاملين</p>
    </div>

    <div class="status-grid">
        <!-- مستودع -->
        <a href="/employee_status/leave_of_absence" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-pause status-icon status-leave"></i>
                <div class="status-title status-leave">مستودع</div>
                <div class="status-count status-leave">{{ leave_of_absence_count or 0 }}</div>
                <div class="status-desc">موظف مستودع</div>
            </div>
        </a>

        <!-- موقوف -->
        <a href="/employee_status/suspension" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-stop-circle status-icon status-suspension"></i>
                <div class="status-title status-suspension">موقوف</div>
                <div class="status-count status-suspension">{{ suspension_count or 0 }}</div>
                <div class="status-desc">موظف موقوف</div>
            </div>
        </a>

        <!-- في عطلة طويلة الأمد -->
        <a href="/employee_status/long_term_leave" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-calendar-times status-icon status-long-leave"></i>
                <div class="status-title status-long-leave">في عطلة طويلة الأمد</div>
                <div class="status-count status-long-leave">{{ long_term_leave_count or 0 }}</div>
                <div class="status-desc">موظف في عطلة طويلة</div>
            </div>
        </a>

        <!-- متقاعد -->
        <a href="/employee_status/retirement" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-user-clock status-icon status-retirement"></i>
                <div class="status-title status-retirement">متقاعد</div>
                <div class="status-count status-retirement">{{ retirement_count or 0 }}</div>
                <div class="status-desc">موظف متقاعد</div>
            </div>
        </a>

        <!-- مستقيل -->
        <a href="/employee_status/resignation" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-sign-out-alt status-icon status-resignation"></i>
                <div class="status-title status-resignation">مستقيل</div>
                <div class="status-count status-resignation">{{ resignation_count or 0 }}</div>
                <div class="status-desc">موظف مستقيل</div>
            </div>
        </a>

        <!-- محول خارجياً -->
        <a href="/employee_status/external_transfer" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-exchange-alt status-icon status-transfer"></i>
                <div class="status-title status-transfer">محول خارجياً</div>
                <div class="status-count status-transfer">{{ external_transfer_count or 0 }}</div>
                <div class="status-desc">موظف محول خارجياً</div>
            </div>
        </a>

        <!-- متوفى -->
        <a href="/employee_status/death" class="text-decoration-none">
            <div class="status-card">
                <i class="fas fa-heart status-icon status-death"></i>
                <div class="status-title status-death">متوفى</div>
                <div class="status-count status-death">{{ death_count or 0 }}</div>
                <div class="status-desc">موظف متوفى</div>
            </div>
        </a>
    </div>
</div>

{% endblock %}
