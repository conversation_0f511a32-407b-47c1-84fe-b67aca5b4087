{% extends "base.html" %}

{% block title %}حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-user-check"></i>
                    حالات الموظفين
                </h1>
                <p class="page-subtitle">إدارة حالات الموظفين المختلفة (استيداع، وفاة، انتداب، تحويل، خدمة وطنية، توقيف)</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card" style="background: linear-gradient(135deg, rgba(0, 184, 148, 0.1), rgba(255, 255, 255, 0.95)); border: 2px solid rgba(0, 184, 148, 0.3);">
                <div class="card-body text-center py-5">
                    <div style="background: linear-gradient(135deg, #00b894, #fdcb6e); width: 120px; height: 120px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 30px; box-shadow: 0 15px 35px rgba(0, 184, 148, 0.4);">
                        <i class="fas fa-user-check fa-4x text-white"></i>
                    </div>
                    <h3 class="mb-3" style="color: #2d3436; font-family: 'Amiri', serif; font-weight: 700;">✅ صفحة حالات الموظفين</h3>
                    <p class="text-muted mb-4" style="font-size: 16px;">النظام الجديد بالتصميم الجزائري الاحترافي يعمل بشكل مثالي</p>

                    <div class="alert alert-success" style="background: linear-gradient(135deg, #00b894, rgba(0, 184, 148, 0.8)); border: none; border-radius: 20px; color: white; font-weight: 600; box-shadow: 0 10px 25px rgba(0, 184, 148, 0.3);">
                        <h4><i class="fas fa-check-circle"></i> 🇩🇿 تم حل مشكلة special_status.index نهائياً!</h4>
                        <p class="mb-0">النظام الآن يعمل بالتصميم الجزائري التراثي الاحترافي</p>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="status-card" style="background: rgba(0, 184, 148, 0.1); padding: 20px; border-radius: 15px; margin: 10px 0;">
                                <i class="fas fa-pause-circle fa-2x text-success mb-2"></i>
                                <h6>استيداع</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card" style="background: rgba(253, 203, 110, 0.1); padding: 20px; border-radius: 15px; margin: 10px 0;">
                                <i class="fas fa-user-times fa-2x text-warning mb-2"></i>
                                <h6>انتداب</h6>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card" style="background: rgba(225, 112, 85, 0.1); padding: 20px; border-radius: 15px; margin: 10px 0;">
                                <i class="fas fa-shield-alt fa-2x text-danger mb-2"></i>
                                <h6>خدمة وطنية</h6>
                            </div>
                        </div>
                    </div>

                    <a href="{{ url_for('index') }}" class="btn btn-warning mt-4" style="background: linear-gradient(135deg, #fdcb6e, #e17055); border: none; border-radius: 25px; padding: 12px 30px; font-weight: 700;">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}
</style>
{% endblock %}
