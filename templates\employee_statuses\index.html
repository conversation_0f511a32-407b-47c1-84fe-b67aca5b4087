{% extends "base.html" %}

{% block title %}حالات الموظفين{% endblock %}

{% block page_title %}حالات الموظفين{% endblock %}

{% block content %}
<style>
.status-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.main-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 15px;
}

.section-title {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 25px;
    margin: 0;
    font-weight: bold;
    text-align: center;
}

.section-title.warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    padding: 40px;
}

.status-card {
    background: white;
    border-radius: 15px;
    padding: 35px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    border: 2px solid transparent;
    color: inherit;
}

.status-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    border-color: #667eea;
    text-decoration: none;
    color: inherit;
}

.status-icon {
    font-size: 4.5rem;
    margin-bottom: 25px;
    display: block;
}

.status-name {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 20px;
}

.status-number {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 15px;
}

.status-desc {
    color: #6c757d;
    font-size: 1.1rem;
}

/* ألوان الحالات */
.text-active { color: #28a745 !important; }
.text-assignment { color: #17a2b8 !important; }
.text-study { color: #007bff !important; }
.text-leave { color: #6c757d !important; }
.text-suspension { color: #dc3545 !important; }
.text-long-leave { color: #ffc107 !important; }
.text-retirement { color: #17a2b8 !important; }
.text-resignation { color: #6c757d !important; }
.text-transfer { color: #007bff !important; }
.text-death { color: #343a40 !important; }

@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .header-section {
        padding: 30px 20px;
    }
    
    .header-section h1 {
        font-size: 2rem;
    }
}
</style>

<div class="status-container">
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="main-card">
            <div class="header-section">
                <h1><i class="fas fa-users-cog"></i> إدارة حالات الموظفين</h1>
                <p class="mb-0">عرض إحصائيات حالات الموظفين مصنفة حسب دخولها في التعداد الحقيقي</p>
            </div>
        </div>

        <!-- الحالات النشطة -->
        <div class="main-card">
            <div class="section-title">
                <h3><i class="fas fa-users me-3"></i>الحالات التي تدخل في التعداد الحقيقي للموظفين</h3>
                <small>هذه الحالات تحسب ضمن العدد الفعلي للموظفين العاملين</small>
            </div>
            
            <div class="status-grid">
                <!-- نشط -->
                <div class="status-card">
                    <i class="fas fa-check-circle status-icon text-active"></i>
                    <div class="status-name text-active">نشط</div>
                    <div class="status-number text-active">{{ active_count or 0 }}</div>
                    <div class="status-desc">موظف نشط</div>
                </div>

                <!-- منتدب -->
                <div class="status-card">
                    <i class="fas fa-user-tie status-icon text-assignment"></i>
                    <div class="status-name text-assignment">منتدب</div>
                    <div class="status-number text-assignment">{{ assignment_count or 0 }}</div>
                    <div class="status-desc">موظف منتدب</div>
                </div>

                <!-- في دراسة/تكوين -->
                <div class="status-card">
                    <i class="fas fa-graduation-cap status-icon text-study"></i>
                    <div class="status-name text-study">في دراسة/تكوين</div>
                    <div class="status-number text-study">{{ study_count or 0 }}</div>
                    <div class="status-desc">موظف في دراسة</div>
                </div>
            </div>
        </div>

        <!-- الحالات غير النشطة -->
        <div class="main-card">
            <div class="section-title warning">
                <h3><i class="fas fa-exclamation-triangle me-3"></i>الحالات التي لا تدخل في التعداد الحقيقي للموظفين</h3>
                <small>هذه الحالات لا تحسب ضمن العدد الفعلي للموظفين العاملين</small>
            </div>
            
            <div class="status-grid">
                <!-- مستودع -->
                <div class="status-card">
                    <i class="fas fa-pause status-icon text-leave"></i>
                    <div class="status-name text-leave">مستودع</div>
                    <div class="status-number text-leave">{{ leave_of_absence_count or 0 }}</div>
                    <div class="status-desc">موظف مستودع</div>
                </div>

                <!-- موقوف -->
                <div class="status-card">
                    <i class="fas fa-stop-circle status-icon text-suspension"></i>
                    <div class="status-name text-suspension">موقوف</div>
                    <div class="status-number text-suspension">{{ suspension_count or 0 }}</div>
                    <div class="status-desc">موظف موقوف</div>
                </div>

                <!-- في عطلة طويلة الأمد -->
                <div class="status-card">
                    <i class="fas fa-calendar-times status-icon text-long-leave"></i>
                    <div class="status-name text-long-leave">في عطلة طويلة الأمد</div>
                    <div class="status-number text-long-leave">{{ long_term_leave_count or 0 }}</div>
                    <div class="status-desc">موظف في عطلة طويلة</div>
                </div>

                <!-- متقاعد -->
                <div class="status-card">
                    <i class="fas fa-user-clock status-icon text-retirement"></i>
                    <div class="status-name text-retirement">متقاعد</div>
                    <div class="status-number text-retirement">{{ retirement_count or 0 }}</div>
                    <div class="status-desc">موظف متقاعد</div>
                </div>

                <!-- مستقيل -->
                <div class="status-card">
                    <i class="fas fa-sign-out-alt status-icon text-resignation"></i>
                    <div class="status-name text-resignation">مستقيل</div>
                    <div class="status-number text-resignation">{{ resignation_count or 0 }}</div>
                    <div class="status-desc">موظف مستقيل</div>
                </div>

                <!-- محول خارجياً -->
                <div class="status-card">
                    <i class="fas fa-exchange-alt status-icon text-transfer"></i>
                    <div class="status-name text-transfer">محول خارجياً</div>
                    <div class="status-number text-transfer">{{ external_transfer_count or 0 }}</div>
                    <div class="status-desc">موظف محول خارجياً</div>
                </div>

                <!-- متوفى -->
                <div class="status-card">
                    <i class="fas fa-heart status-icon text-death"></i>
                    <div class="status-name text-death">متوفى</div>
                    <div class="status-number text-death">{{ death_count or 0 }}</div>
                    <div class="status-desc">موظف متوفى</div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
