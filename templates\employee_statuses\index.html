{% extends "base.html" %}

{% block title %}إدارة حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة حالات الموظفين{% endblock %}

{% block content %}
<!-- معلومات الحالات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>إدارة حالات الموظفين
                </h5>
                <p class="text-muted mb-0">يتم إضافة الحالات من خلال تعديل بيانات الموظف</p>
            </div>
            <div class="text-muted">
                <small>إجمالي الحالات المتاحة: 10</small>
            </div>
        </div>
    </div>
</div>

<!-- الحالات التي تدخل في التعداد الحقيقي للموظفين -->
<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>الحالات التي تدخل في التعداد الحقيقي للموظفين
        </h5>
        <small>هذه الحالات تحسب ضمن العدد الفعلي للموظفين العاملين</small>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- نشط -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-success">
                    <div class="card-header bg-success text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-check-circle me-2"></i>نشط
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('نشط', 'success', 'check-circle')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=نشط">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-success">{{ active_count or 0 }}</h3>
                        <p class="text-muted">موظف نشط</p>
                    </div>
                </div>
            </div>

            <!-- منتدب -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-info">
                    <div class="card-header bg-info text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-user-tie me-2"></i>منتدب
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('منتدب', 'info', 'user-tie')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=منتدب">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-info">{{ assignment_count or 0 }}</h3>
                        <p class="text-muted">موظف منتدب</p>
                    </div>
                </div>
            </div>

            <!-- في دراسة/تكوين -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-primary">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-graduation-cap me-2"></i>في دراسة/تكوين
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('في دراسة/تكوين', 'primary', 'graduation-cap')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=في دراسة/تكوين">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-primary">{{ study_count or 0 }}</h3>
                        <p class="text-muted">موظف في دراسة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الحالات التي لا تدخل في التعداد الحقيقي للموظفين -->
<div class="card mb-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>الحالات التي لا تدخل في التعداد الحقيقي للموظفين
        </h5>
        <small>هذه الحالات لا تحسب ضمن العدد الفعلي للموظفين العاملين</small>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- مستودع -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-secondary">
                    <div class="card-header bg-secondary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-pause me-2"></i>مستودع
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('مستودع', 'secondary', 'pause')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=مستودع">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-secondary">{{ leave_of_absence_count or 0 }}</h3>
                        <p class="text-muted">موظف مستودع</p>
                    </div>
                </div>
            </div>

            <!-- موقوف -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-danger">
                    <div class="card-header bg-danger text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-stop-circle me-2"></i>موقوف
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('موقوف', 'danger', 'stop-circle')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=موقوف">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-danger">{{ suspension_count or 0 }}</h3>
                        <p class="text-muted">موظف موقوف</p>
                    </div>
                </div>
            </div>

            <!-- في عطلة طويلة الأمد -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-times me-2"></i>في عطلة طويلة الأمد
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('في عطلة طويلة الأمد', 'warning', 'calendar-times')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=في عطلة طويلة الأمد">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-warning">{{ long_term_leave_count or 0 }}</h3>
                        <p class="text-muted">موظف في عطلة طويلة</p>
                    </div>
                </div>
            </div>

            <!-- متقاعد -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-info">
                    <div class="card-header bg-info text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-user-clock me-2"></i>متقاعد
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('متقاعد', 'info', 'user-clock')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=متقاعد">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-info">{{ retirement_count or 0 }}</h3>
                        <p class="text-muted">موظف متقاعد</p>
                    </div>
                </div>
            </div>

            <!-- مستقيل -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-secondary">
                    <div class="card-header bg-secondary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-sign-out-alt me-2"></i>مستقيل
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('مستقيل', 'secondary', 'sign-out-alt')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=مستقيل">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-secondary">{{ resignation_count or 0 }}</h3>
                        <p class="text-muted">موظف مستقيل</p>
                    </div>
                </div>
            </div>

            <!-- محول خارجياً -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-exchange-alt me-2"></i>محول خارجياً
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('محول خارجياً', 'warning', 'exchange-alt')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=محول خارجياً">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-warning">{{ external_transfer_count or 0 }}</h3>
                        <p class="text-muted">موظف محول خارجياً</p>
                    </div>
                </div>
            </div>

            <!-- متوفى -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-dark">
                    <div class="card-header bg-dark text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-heart me-2"></i>متوفى
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editStatus('متوفى', 'dark', 'heart')">
                                        <i class="fas fa-edit me-2"></i>تعديل
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('employees') }}?status=متوفى">
                                        <i class="fas fa-eye me-2"></i>معاينة الموظفين
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-dark">{{ death_count or 0 }}</h3>
                        <p class="text-muted">موظف متوفى</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة/تعديل حالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">
                    <i class="fas fa-edit me-2"></i>تعديل الحالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <input type="hidden" id="status_id" name="status_id">
                    
                    <div class="mb-3">
                        <label class="form-label">اسم الحالة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="status_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="status_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اللون</label>
                                <select class="form-select" id="status_color" name="color_class">
                                    <option value="primary">أزرق (Primary)</option>
                                    <option value="success">أخضر (Success)</option>
                                    <option value="warning">أصفر (Warning)</option>
                                    <option value="danger">أحمر (Danger)</option>
                                    <option value="info">فيروزي (Info)</option>
                                    <option value="secondary">رمادي (Secondary)</option>
                                    <option value="dark">أسود (Dark)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الأيقونة</label>
                                <select class="form-select" id="status_icon" name="icon">
                                    <option value="check-circle">✓ نشط</option>
                                    <option value="pause-circle">⏸ معلق</option>
                                    <option value="exchange-alt">⇄ تحويل</option>
                                    <option value="user-tie">👔 منتدب</option>
                                    <option value="pause">⏸ استيداع</option>
                                    <option value="times-circle">✗ متوفي</option>
                                    <option value="user-times">👤✗ مفصول</option>
                                    <option value="sign-out-alt">🚪 مستقيل</option>
                                    <option value="stop-circle">⏹ موقف</option>
                                    <option value="info-circle">ℹ معلومات</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معاينة الحالة -->
                    <div class="mb-3">
                        <label class="form-label">معاينة</label>
                        <div class="p-3 border rounded">
                            <span class="badge fs-6 px-3 py-2" id="status_preview">
                                <i class="fas fa-info-circle me-2"></i>معاينة الحالة
                            </span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="statusSubmitBtn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
let currentStatusId = null;



// تعديل حالة
function editStatus(id, name, description, colorClass, icon) {
    currentStatusId = id;
    document.getElementById('statusModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>تعديل الحالة';
    document.getElementById('statusSubmitBtn').textContent = 'تحديث';

    document.getElementById('status_id').value = id;
    document.getElementById('status_name').value = name;
    document.getElementById('status_description').value = description || '';
    document.getElementById('status_color').value = colorClass;
    document.getElementById('status_icon').value = icon;

    updatePreview();

    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

// تحديث معاينة الحالة
function updatePreview() {
    const name = document.getElementById('status_name').value || 'معاينة الحالة';
    const color = document.getElementById('status_color').value;
    const icon = document.getElementById('status_icon').value;
    
    const preview = document.getElementById('status_preview');
    preview.className = `badge bg-${color} fs-6 px-3 py-2`;
    preview.innerHTML = `<i class="fas fa-${icon} me-2"></i>${name}`;
}

// معالجة إرسال النموذج (تعديل فقط)
document.getElementById('statusForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!currentStatusId) {
        alert('خطأ: لا يمكن إضافة حالات جديدة من هنا. يتم إضافة الحالات من خلال تعديل بيانات الموظف.');
        return;
    }

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    fetch(`/employee_statuses/edit/${currentStatusId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في العملية');
    });
});

// تحديث المعاينة عند تغيير القيم
document.getElementById('status_name').addEventListener('input', updatePreview);
document.getElementById('status_color').addEventListener('change', updatePreview);
document.getElementById('status_icon').addEventListener('change', updatePreview);



// تحديث المعاينة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
});
</script>
{% endblock %}
