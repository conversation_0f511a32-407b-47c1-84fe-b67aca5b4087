{% extends "base.html" %}

{% block title %}إدارة حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة حالات الموظفين{% endblock %}

{% block content %}
<!-- معلومات الحالات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>إدارة حالات الموظفين
                </h5>
                <p class="text-muted mb-0">انقر على أي حالة للدخول إلى صفحة إدارتها</p>
            </div>
            <div class="text-muted">
                <small>إجمالي الحالات المتاحة: 10</small>
            </div>
        </div>
    </div>
</div>

<!-- الحالات التي تدخل في التعداد الحقيقي للموظفين -->
<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>الحالات التي تدخل في التعداد الحقيقي للموظفين
        </h5>
        <small>هذه الحالات تحسب ضمن العدد الفعلي للموظفين العاملين</small>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- نشط -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/active" class="text-decoration-none">
                    <div class="card h-100 border-success status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h4 class="text-success">نشط</h4>
                            <h2 class="text-success">{{ active_count or 0 }}</h2>
                            <p class="text-muted">موظف نشط</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- منتدب -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/assignment" class="text-decoration-none">
                    <div class="card h-100 border-info status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-user-tie fa-3x text-info mb-3"></i>
                            <h4 class="text-info">منتدب</h4>
                            <h2 class="text-info">{{ assignment_count or 0 }}</h2>
                            <p class="text-muted">موظف منتدب</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- في دراسة/تكوين -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/study" class="text-decoration-none">
                    <div class="card h-100 border-primary status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-graduation-cap fa-3x text-primary mb-3"></i>
                            <h4 class="text-primary">في دراسة/تكوين</h4>
                            <h2 class="text-primary">{{ study_count or 0 }}</h2>
                            <p class="text-muted">موظف في دراسة</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- الحالات التي لا تدخل في التعداد الحقيقي للموظفين -->
<div class="card mb-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>الحالات التي لا تدخل في التعداد الحقيقي للموظفين
        </h5>
        <small>هذه الحالات لا تحسب ضمن العدد الفعلي للموظفين العاملين</small>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- مستودع -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/leave_of_absence" class="text-decoration-none">
                    <div class="card h-100 border-secondary status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-pause fa-3x text-secondary mb-3"></i>
                            <h4 class="text-secondary">مستودع</h4>
                            <h2 class="text-secondary">{{ leave_of_absence_count or 0 }}</h2>
                            <p class="text-muted">موظف مستودع</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- موقوف -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/suspension" class="text-decoration-none">
                    <div class="card h-100 border-danger status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-stop-circle fa-3x text-danger mb-3"></i>
                            <h4 class="text-danger">موقوف</h4>
                            <h2 class="text-danger">{{ suspension_count or 0 }}</h2>
                            <p class="text-muted">موظف موقوف</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- في عطلة طويلة الأمد -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/long_term_leave" class="text-decoration-none">
                    <div class="card h-100 border-warning status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-times fa-3x text-warning mb-3"></i>
                            <h4 class="text-warning">في عطلة طويلة الأمد</h4>
                            <h2 class="text-warning">{{ long_term_leave_count or 0 }}</h2>
                            <p class="text-muted">موظف في عطلة طويلة</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- متقاعد -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/retirement" class="text-decoration-none">
                    <div class="card h-100 border-info status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-user-clock fa-3x text-info mb-3"></i>
                            <h4 class="text-info">متقاعد</h4>
                            <h2 class="text-info">{{ retirement_count or 0 }}</h2>
                            <p class="text-muted">موظف متقاعد</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- مستقيل -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/resignation" class="text-decoration-none">
                    <div class="card h-100 border-secondary status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-sign-out-alt fa-3x text-secondary mb-3"></i>
                            <h4 class="text-secondary">مستقيل</h4>
                            <h2 class="text-secondary">{{ resignation_count or 0 }}</h2>
                            <p class="text-muted">موظف مستقيل</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- محول خارجياً -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/external_transfer" class="text-decoration-none">
                    <div class="card h-100 border-warning status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-exchange-alt fa-3x text-warning mb-3"></i>
                            <h4 class="text-warning">محول خارجياً</h4>
                            <h2 class="text-warning">{{ external_transfer_count or 0 }}</h2>
                            <p class="text-muted">موظف محول خارجياً</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- متوفى -->
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="/employee_status/death" class="text-decoration-none">
                    <div class="card h-100 border-dark status-card">
                        <div class="card-body text-center">
                            <i class="fas fa-heart fa-3x text-dark mb-3"></i>
                            <h4 class="text-dark">متوفى</h4>
                            <h2 class="text-dark">{{ death_count or 0 }}</h2>
                            <p class="text-muted">موظف متوفى</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- CSS للتأثيرات التفاعلية -->
<style>
.status-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.status-card .card-body {
    padding: 2rem;
}

.status-card i {
    transition: all 0.3s ease;
}

.status-card:hover i {
    transform: scale(1.1);
}

.status-card h4 {
    font-weight: bold;
    margin-bottom: 1rem;
}

.status-card h2 {
    font-weight: bold;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.card-header {
    border-bottom: 2px solid rgba(255,255,255,0.2);
}

.card-header h5 {
    font-weight: bold;
}

.card-header small {
    opacity: 0.9;
}
</style>

{% endblock %}
