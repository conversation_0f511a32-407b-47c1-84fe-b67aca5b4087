{% extends "base.html" %}

{% block title %}إدارة حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة حالات الموظفين{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body bg-gradient-primary text-white rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-2 fw-bold">
                            <i class="fas fa-users-cog me-3"></i>إدارة حالات الموظفين
                        </h3>
                        <p class="mb-0 opacity-75">انقر على أي حالة للدخول إلى صفحة إدارتها والاطلاع على التفاصيل</p>
                    </div>
                    <div class="text-end">
                        <div class="bg-white bg-opacity-20 rounded p-3">
                            <h4 class="mb-0 fw-bold">10</h4>
                            <small>حالة متاحة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Active Status Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white border-0">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                        <i class="fas fa-users fa-lg"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">الحالات التي تدخل في التعداد الحقيقي للموظفين</h5>
                        <small class="opacity-75">هذه الحالات تحسب ضمن العدد الفعلي للموظفين العاملين</small>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="row g-4">
                    <!-- نشط -->
                    <div class="col-xl-4 col-lg-6 col-md-6">
                        <a href="/employee_status/active" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-check-circle fa-4x text-success"></i>
                                    </div>
                                    <h4 class="fw-bold text-success mb-2">نشط</h4>
                                    <h1 class="display-4 fw-bold text-success mb-2">{{ active_count or 0 }}</h1>
                                    <p class="text-muted mb-0">موظف نشط</p>
                                    <div class="status-indicator bg-success"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- منتدب -->
                    <div class="col-xl-4 col-lg-6 col-md-6">
                        <a href="/employee_status/assignment" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-user-tie fa-4x text-info"></i>
                                    </div>
                                    <h4 class="fw-bold text-info mb-2">منتدب</h4>
                                    <h1 class="display-4 fw-bold text-info mb-2">{{ assignment_count or 0 }}</h1>
                                    <p class="text-muted mb-0">موظف منتدب</p>
                                    <div class="status-indicator bg-info"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- في دراسة/تكوين -->
                    <div class="col-xl-4 col-lg-6 col-md-6">
                        <a href="/employee_status/study" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-graduation-cap fa-4x text-primary"></i>
                                    </div>
                                    <h4 class="fw-bold text-primary mb-2">في دراسة/تكوين</h4>
                                    <h1 class="display-4 fw-bold text-primary mb-2">{{ study_count or 0 }}</h1>
                                    <p class="text-muted mb-0">موظف في دراسة</p>
                                    <div class="status-indicator bg-primary"></div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Inactive Status Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-warning text-dark border-0">
                <div class="d-flex align-items-center">
                    <div class="bg-dark bg-opacity-20 rounded-circle p-2 me-3">
                        <i class="fas fa-exclamation-triangle fa-lg text-dark"></i>
                    </div>
                    <div>
                        <h5 class="mb-1 fw-bold">الحالات التي لا تدخل في التعداد الحقيقي للموظفين</h5>
                        <small class="opacity-75">هذه الحالات لا تحسب ضمن العدد الفعلي للموظفين العاملين</small>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="row g-4">
                    <!-- مستودع -->
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <a href="/employee_status/leave_of_absence" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-pause fa-3x text-secondary"></i>
                                    </div>
                                    <h5 class="fw-bold text-secondary mb-2">مستودع</h5>
                                    <h2 class="display-6 fw-bold text-secondary mb-2">{{ leave_of_absence_count or 0 }}</h2>
                                    <p class="text-muted mb-0 small">موظف مستودع</p>
                                    <div class="status-indicator bg-secondary"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- موقوف -->
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <a href="/employee_status/suspension" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-stop-circle fa-3x text-danger"></i>
                                    </div>
                                    <h5 class="fw-bold text-danger mb-2">موقوف</h5>
                                    <h2 class="display-6 fw-bold text-danger mb-2">{{ suspension_count or 0 }}</h2>
                                    <p class="text-muted mb-0 small">موظف موقوف</p>
                                    <div class="status-indicator bg-danger"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- في عطلة طويلة الأمد -->
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <a href="/employee_status/long_term_leave" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-calendar-times fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="fw-bold text-warning mb-2">في عطلة طويلة الأمد</h5>
                                    <h2 class="display-6 fw-bold text-warning mb-2">{{ long_term_leave_count or 0 }}</h2>
                                    <p class="text-muted mb-0 small">موظف في عطلة طويلة</p>
                                    <div class="status-indicator bg-warning"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- متقاعد -->
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <a href="/employee_status/retirement" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-user-clock fa-3x text-info"></i>
                                    </div>
                                    <h5 class="fw-bold text-info mb-2">متقاعد</h5>
                                    <h2 class="display-6 fw-bold text-info mb-2">{{ retirement_count or 0 }}</h2>
                                    <p class="text-muted mb-0 small">موظف متقاعد</p>
                                    <div class="status-indicator bg-info"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- مستقيل -->
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <a href="/employee_status/resignation" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-sign-out-alt fa-3x text-secondary"></i>
                                    </div>
                                    <h5 class="fw-bold text-secondary mb-2">مستقيل</h5>
                                    <h2 class="display-6 fw-bold text-secondary mb-2">{{ resignation_count or 0 }}</h2>
                                    <p class="text-muted mb-0 small">موظف مستقيل</p>
                                    <div class="status-indicator bg-secondary"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- محول خارجياً -->
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <a href="/employee_status/external_transfer" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-exchange-alt fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="fw-bold text-primary mb-2">محول خارجياً</h5>
                                    <h2 class="display-6 fw-bold text-primary mb-2">{{ external_transfer_count or 0 }}</h2>
                                    <p class="text-muted mb-0 small">موظف محول خارجياً</p>
                                    <div class="status-indicator bg-primary"></div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- متوفى -->
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <a href="/employee_status/death" class="text-decoration-none">
                            <div class="card border-0 shadow-sm status-card h-100">
                                <div class="card-body text-center p-4">
                                    <div class="status-icon-wrapper mb-3">
                                        <i class="fas fa-heart fa-3x text-dark"></i>
                                    </div>
                                    <h5 class="fw-bold text-dark mb-2">متوفى</h5>
                                    <h2 class="display-6 fw-bold text-dark mb-2">{{ death_count or 0 }}</h2>
                                    <p class="text-muted mb-0 small">موظف متوفى</p>
                                    <div class="status-indicator bg-dark"></div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advanced CSS Styling -->
<style>
/* Global Styles */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Gradient */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Status Cards */
.status-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    border-radius: 15px !important;
    overflow: hidden;
    position: relative;
}

.status-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.status-card:hover::before {
    opacity: 1;
}

/* Status Icon Wrapper */
.status-icon-wrapper {
    position: relative;
    display: inline-block;
}

.status-icon-wrapper::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(0,0,0,0.05);
    z-index: -1;
    transition: all 0.3s ease;
}

.status-card:hover .status-icon-wrapper::before {
    transform: translate(-50%, -50%) scale(1.2);
    background: rgba(0,0,0,0.1);
}

.status-card:hover .status-icon-wrapper i {
    transform: scale(1.1) rotate(5deg);
}

/* Status Indicator */
.status-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 0 0 15px 15px;
}

/* Card Headers */
.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none !important;
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.card:hover .card-header::before {
    left: 100%;
}

/* Typography */
.display-4, .display-6 {
    font-weight: 800 !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .status-card:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .display-4, .display-6 {
        font-size: 2rem !important;
    }
}

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.status-card {
    animation: fadeInUp 0.6s ease forwards;
}

.status-card:nth-child(1) { animation-delay: 0.1s; }
.status-card:nth-child(2) { animation-delay: 0.2s; }
.status-card:nth-child(3) { animation-delay: 0.3s; }
.status-card:nth-child(4) { animation-delay: 0.4s; }

/* Hover effects for numbers */
.status-card:hover .display-4,
.status-card:hover .display-6 {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

/* Shadow effects */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.card:hover {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
</style>

{% endblock %}
