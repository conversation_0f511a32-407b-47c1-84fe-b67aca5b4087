{% extends "base.html" %}

{% block title %}حالات الموظفين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-user-check"></i>
                    حالات الموظفين
                </h1>
                <p class="page-subtitle">إدارة حالات الموظفين المختلفة (استيداع، وفاة، انتداب، تحويل، خدمة وطنية، توقيف)</p>
            </div>
        </div>
    </div>

    <!-- Status Categories -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-4">
                        <i class="fas fa-list"></i> أنواع حالات الموظفين
                    </h5>
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="status-card" data-status="leave_of_absence">
                                <div class="status-icon bg-warning">
                                    <i class="fas fa-pause"></i>
                                </div>
                                <h6>استيداع</h6>
                                <p class="text-muted">إجازة بدون راتب</p>
                                <span class="badge bg-warning">0</span>
                            </div>
                        </div>
                        
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="status-card" data-status="death">
                                <div class="status-icon bg-dark">
                                    <i class="fas fa-heart-broken"></i>
                                </div>
                                <h6>وفاة</h6>
                                <p class="text-muted">وفاة الموظف</p>
                                <span class="badge bg-dark">0</span>
                            </div>
                        </div>
                        
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="status-card" data-status="assignment">
                                <div class="status-icon bg-info">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <h6>انتداب</h6>
                                <p class="text-muted">انتداب لجهة أخرى</p>
                                <span class="badge bg-info">0</span>
                            </div>
                        </div>
                        
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="status-card" data-status="transfer">
                                <div class="status-icon bg-primary">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <h6>تحويل</h6>
                                <p class="text-muted">تحويل لقسم آخر</p>
                                <span class="badge bg-primary">0</span>
                            </div>
                        </div>
                        
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="status-card" data-status="national_service">
                                <div class="status-icon bg-success">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <h6>خدمة وطنية</h6>
                                <p class="text-muted">الخدمة الوطنية</p>
                                <span class="badge bg-success">0</span>
                            </div>
                        </div>
                        
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="status-card" data-status="suspension">
                                <div class="status-icon bg-danger">
                                    <i class="fas fa-ban"></i>
                                </div>
                                <h6>توقيف</h6>
                                <p class="text-muted">توقيف مؤقت</p>
                                <span class="badge bg-danger">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-plus-circle"></i> إضافة حالة جديدة
                    </h5>
                    <div class="row">
                        <div class="col-md-2 mb-2">
                            <button type="button" class="btn btn-warning btn-block" data-bs-toggle="modal" data-bs-target="#addStatusModal" data-status="leave_of_absence">
                                <i class="fas fa-pause"></i><br>
                                إضافة استيداع
                            </button>
                        </div>
                        <div class="col-md-2 mb-2">
                            <button type="button" class="btn btn-dark btn-block" data-bs-toggle="modal" data-bs-target="#addStatusModal" data-status="death">
                                <i class="fas fa-heart-broken"></i><br>
                                إضافة وفاة
                            </button>
                        </div>
                        <div class="col-md-2 mb-2">
                            <button type="button" class="btn btn-info btn-block" data-bs-toggle="modal" data-bs-target="#addStatusModal" data-status="assignment">
                                <i class="fas fa-briefcase"></i><br>
                                إضافة انتداب
                            </button>
                        </div>
                        <div class="col-md-2 mb-2">
                            <button type="button" class="btn btn-primary btn-block" data-bs-toggle="modal" data-bs-target="#addStatusModal" data-status="transfer">
                                <i class="fas fa-exchange-alt"></i><br>
                                إضافة تحويل
                            </button>
                        </div>
                        <div class="col-md-2 mb-2">
                            <button type="button" class="btn btn-success btn-block" data-bs-toggle="modal" data-bs-target="#addStatusModal" data-status="national_service">
                                <i class="fas fa-flag"></i><br>
                                خدمة وطنية
                            </button>
                        </div>
                        <div class="col-md-2 mb-2">
                            <button type="button" class="btn btn-danger btn-block" data-bs-toggle="modal" data-bs-target="#addStatusModal" data-status="suspension">
                                <i class="fas fa-ban"></i><br>
                                إضافة توقيف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Records Table -->
    <div class="row">
        <div class="col-12">
            <div class="table-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-table"></i> سجل حالات الموظفين
                    </h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم التسجيل</th>
                            <th>اسم الموظف</th>
                            <th>نوع الحالة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>رقم القرار</th>
                            <th>تاريخ القرار</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد حالات مسجلة</h5>
                                    <p class="text-muted">ابدأ بإضافة حالة جديدة للموظفين</p>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStatusModal">
                                        <i class="fas fa-plus"></i> إضافة حالة جديدة
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Status Modal -->
<div class="modal fade" id="addStatusModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> إضافة حالة موظف جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addStatusForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الموظف <span class="text-danger">*</span></label>
                            <select class="form-select" name="employee_id" required>
                                <option value="">اختر الموظف</option>
                                <!-- سيتم ملؤها ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الحالة <span class="text-danger">*</span></label>
                            <select class="form-select" name="status_type" required>
                                <option value="">اختر نوع الحالة</option>
                                <option value="leave_of_absence">استيداع</option>
                                <option value="death">وفاة</option>
                                <option value="assignment">انتداب</option>
                                <option value="transfer">تحويل</option>
                                <option value="national_service">خدمة وطنية</option>
                                <option value="suspension">توقيف</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="start_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" name="end_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم القرار</label>
                            <input type="text" class="form-control" name="decision_number">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ القرار</label>
                            <input type="date" class="form-control" name="decision_date">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.status-card {
    text-align: center;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.status-card:hover {
    border-color: #3498db;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.status-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 20px;
}

.status-card h6 {
    font-weight: 600;
    margin-bottom: 5px;
}

.status-card .badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.empty-state {
    padding: 40px 20px;
}
</style>
{% endblock %}
