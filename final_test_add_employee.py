#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لإضافة موظف مع حقول فارغة
"""

from app import app

def test_add_employee_final():
    """اختبار نهائي لإضافة موظف"""
    print("🎯 الاختبار النهائي لإضافة موظف مع حقول فارغة")
    print("=" * 70)
    
    with app.test_client() as client:
        # اختبار 1: موظف مع حقول فارغة
        print("1️⃣  اختبار إضافة موظف مع حقول فارغة...")
        
        form_data_empty = {
            'registration_number': '888881',
            'first_name': 'محمد',
            'last_name': 'علي',
            'first_name_fr': '',
            'last_name_fr': '',
            'birth_date': '',
            'birth_wilaya_id': '',
            'birth_commune_id': '',
            'gender': '',
            'social_security_number': '',  # فارغ
            'hire_date': '',
            'current_rank_id': '',
            'corps_id': '',
            'current_service_id': '',
            'postal_account': '',  # فارغ
            'phone': '',
            'email': '',
            'address': '',
            'marital_status': '',
            'children_count': '',
            'dependents_count': '',
            'blood_type': '',
            'sport_practiced': '',
            'phone1': '',
            'phone2': ''
        }
        
        response = client.post('/add_employee', data=form_data_empty, follow_redirects=True)
        
        if response.status_code == 200:
            content = response.data.decode('utf-8')
            
            # فحص عدم وجود أخطاء
            error_messages = [
                'رقم الضمان الاجتماعي غير صحيح',
                'رقم الضمان الاجتماعي موجود بالفعل',
                'رقم الحساب الجاري البريدي غير صحيح',
                'alert-danger'
            ]
            
            errors_found = []
            for error in error_messages:
                if error in content:
                    errors_found.append(error)
            
            if errors_found:
                print("❌ تم العثور على أخطاء:")
                for error in errors_found:
                    print(f"   - {error}")
            else:
                print("✅ لم يتم العثور على أخطاء - الاختبار نجح!")
            
            # فحص رسائل النجاح
            if 'تم إضافة الموظف بنجاح' in content:
                print("✅ تم إضافة الموظف بنجاح")
            elif 'قائمة الموظفين' in content:
                print("✅ تم إعادة التوجيه لقائمة الموظفين")
            
        else:
            print(f"❌ خطأ في الطلب: {response.status_code}")
        
        # اختبار 2: موظف آخر مع حقول فارغة (للتأكد من عدم تضارب)
        print(f"\n2️⃣  اختبار إضافة موظف آخر مع حقول فارغة...")
        
        form_data_empty2 = form_data_empty.copy()
        form_data_empty2['registration_number'] = '888882'
        form_data_empty2['first_name'] = 'أحمد'
        form_data_empty2['last_name'] = 'محمد'
        
        response2 = client.post('/add_employee', data=form_data_empty2, follow_redirects=True)
        
        if response2.status_code == 200:
            content2 = response2.data.decode('utf-8')
            
            if 'رقم الضمان الاجتماعي موجود بالفعل' in content2:
                print("❌ لا يزال هناك تضارب في رقم الضمان الفارغ")
            else:
                print("✅ لا يوجد تضارب - يمكن إضافة موظفين متعددين برقم ضمان فارغ")
        
        # اختبار 3: موظف مع رقم ضمان صحيح
        print(f"\n3️⃣  اختبار إضافة موظف مع رقم ضمان صحيح...")
        
        form_data_valid = {
            'registration_number': '888883',
            'first_name': 'سارة',
            'last_name': 'أحمد',
            'birth_date': '1990-01-01',
            'gender': 'أنثى',
            'social_security_number': '***************',  # رقم صحيح لأنثى
            'postal_account': '**********',  # رقم صحيح
            'email': '<EMAIL>',
            'marital_status': 'أعزب'
        }
        
        response3 = client.post('/add_employee', data=form_data_valid, follow_redirects=True)
        
        if response3.status_code == 200:
            content3 = response3.data.decode('utf-8')
            
            validation_errors = [
                'رقم الضمان الاجتماعي غير صحيح',
                'رقم الحساب الجاري البريدي غير صحيح'
            ]
            
            validation_errors_found = []
            for error in validation_errors:
                if error in content3:
                    validation_errors_found.append(error)
            
            if validation_errors_found:
                print("❌ أخطاء في التحقق:")
                for error in validation_errors_found:
                    print(f"   - {error}")
            else:
                print("✅ البيانات الصحيحة تم قبولها")

def cleanup_test_data():
    """تنظيف البيانات التجريبية"""
    print(f"\n🗑️  تنظيف البيانات التجريبية...")
    
    import sqlite3
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    try:
        cursor.execute("DELETE FROM employees WHERE registration_number LIKE '8888%'")
        deleted_count = cursor.rowcount
        conn.commit()
        print(f"✅ تم حذف {deleted_count} موظف تجريبي")
    except Exception as e:
        print(f"❌ خطأ في التنظيف: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    test_add_employee_final()
    cleanup_test_data()
    
    print(f"\n🎉 الخلاصة النهائية:")
    print("✅ تم إصلاح مشكلة القيد الفريد على رقم الضمان الاجتماعي")
    print("✅ يمكن الآن إضافة موظفين متعددين برقم ضمان فارغ")
    print("✅ التحقق من صحة البيانات لا يزال يعمل")
    print("✅ لا توجد رسائل خطأ للحقول الفارغة")
    
    print(f"\n🚀 جرب الآن:")
    print("  1. اذهب إلى: http://localhost:5000/add_employee")
    print("  2. املأ رقم التسجيل والاسم واللقب فقط")
    print("  3. اترك رقم الضمان الاجتماعي فارغاً")
    print("  4. انقر حفظ")
    print("  5. يجب أن يعمل بدون أي أخطاء! 🎯")