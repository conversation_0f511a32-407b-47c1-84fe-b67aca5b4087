#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عمل النظام
"""

import requests
import time

def test_system():
    """اختبار النظام"""
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار عمل النظام...")
    print("=" * 50)
    
    # اختبار الصفحة الرئيسية
    try:
        response = requests.get(f"{base_url}/special_status/", timeout=5)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية للحالات الخاصة تعمل")
        else:
            print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بالصفحة الرئيسية: {e}")
    
    # اختبار صفحة إعدادات أسباب الاستيداع
    try:
        response = requests.get(f"{base_url}/special_status/leave_reasons_settings", timeout=5)
        if response.status_code == 200:
            print("✅ صفحة إعدادات أسباب الاستيداع تعمل")
        else:
            print(f"❌ خطأ في صفحة الإعدادات: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بصفحة الإعدادات: {e}")
    
    # اختبار صفحة إضافة الاستيداع
    try:
        response = requests.get(f"{base_url}/special_status/leave_of_absence/add", timeout=5)
        if response.status_code == 200:
            print("✅ صفحة إضافة الاستيداع تعمل")
        else:
            print(f"❌ خطأ في صفحة إضافة الاستيداع: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بصفحة إضافة الاستيداع: {e}")
    
    # اختبار الصفحة الرئيسية للتطبيق
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية للتطبيق تعمل")
        else:
            print(f"❌ خطأ في الصفحة الرئيسية للتطبيق: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بالصفحة الرئيسية للتطبيق: {e}")
    
    print("\n🎉 انتهى الاختبار!")
    print("🌐 يمكنك الآن استخدام النظام من خلال:")
    print(f"   - الصفحة الرئيسية: {base_url}/")
    print(f"   - الحالات الخاصة: {base_url}/special_status/")
    print(f"   - إعدادات الاستيداع: {base_url}/special_status/leave_reasons_settings")

if __name__ == '__main__':
    # انتظار قليل للتأكد من تشغيل الخادم
    print("⏳ انتظار تشغيل الخادم...")
    time.sleep(3)
    
    test_system()
