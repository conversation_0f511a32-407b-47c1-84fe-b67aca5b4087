#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكامل لربط وحدة إدارة حالات الموظفين مع التطبيق الرئيسي
Integration file for connecting Employee Status Management with main app
"""

from flask import request, jsonify, render_template, redirect, url_for, flash
from employee_status_manager import EmployeeStatusManager, create_status_manager
from datetime import datetime, date
import json

class StatusIntegration:
    """فئة التكامل لحالات الموظفين"""
    
    def __init__(self, app, db_path='customs_employees.db'):
        self.app = app
        self.status_manager = create_status_manager(db_path)
        self.register_routes()
    
    def register_routes(self):
        """تسجيل المسارات"""
        
        # ================================
        # المسارات الرئيسية
        # ================================
        
        @self.app.route('/employee_status')
        def employee_status_dashboard():
            """لوحة تحكم حالات الموظفين"""
            stats = self.status_manager.get_status_statistics()
            return render_template('employee_status/dashboard.html', stats=stats)
        
        @self.app.route('/employee_status/<int:employee_id>')
        def employee_status_history(employee_id):
            """تاريخ حالات موظف معين"""
            history = self.status_manager.get_employee_status_history(employee_id)
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('''
                SELECT e.*, r.name as rank_name, s.name as service_name
                FROM employees e
                LEFT JOIN ranks r ON e.current_rank_id = r.id
                LEFT JOIN services s ON e.current_service_id = s.id
                WHERE e.id = ?
            ''', (employee_id,)).fetchone()
            conn.close()
            
            if not employee:
                flash('الموظف غير موجود', 'error')
                return redirect(url_for('employees'))
            
            return render_template('employee_status/history.html', 
                                 employee=employee, history=history)
        
        # ================================
        # العطلة طويلة الأمد
        # ================================
        
        @self.app.route('/employee_status/long_term_leave/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_long_term_leave(employee_id):
            """إضافة عطلة طويلة الأمد"""
            if request.method == 'POST':
                data = {
                    'leave_type': request.form.get('leave_type'),
                    'start_date': request.form.get('start_date'),
                    'expected_end_date': request.form.get('expected_end_date'),
                    'reason': request.form.get('reason'),
                    'decision_number': request.form.get('decision_number'),
                    'decision_date': request.form.get('decision_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_long_term_leave(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('employee_status_history', employee_id=employee_id))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_long_term_leave.html', employee=employee)
        
        @self.app.route('/employee_status/long_term_leave/end/<int:leave_id>', methods=['POST'])
        def end_long_term_leave(leave_id):
            """إنهاء عطلة طويلة الأمد"""
            end_date = request.form.get('end_date')
            notes = request.form.get('notes', '')
            
            success, message = self.status_manager.end_long_term_leave(leave_id, end_date, notes)
            
            if success:
                flash(message, 'success')
            else:
                flash(message, 'error')
            
            return redirect(request.referrer or url_for('employee_status_dashboard'))
        
        # ================================
        # الاستقالة
        # ================================
        
        @self.app.route('/employee_status/resignation/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_resignation(employee_id):
            """إضافة استقالة"""
            if request.method == 'POST':
                data = {
                    'resignation_date': request.form.get('resignation_date'),
                    'reason': request.form.get('reason'),
                    'notice_period_days': int(request.form.get('notice_period_days', 30)),
                    'last_working_day': request.form.get('last_working_day'),
                    'decision_number': request.form.get('decision_number'),
                    'decision_date': request.form.get('decision_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_resignation(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('employee_status_history', employee_id=employee_id))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_resignation.html', employee=employee)
        
        @self.app.route('/employee_status/resignation/approve/<int:resignation_id>', methods=['POST'])
        def approve_resignation(resignation_id):
            """الموافقة على الاستقالة"""
            approval_date = request.form.get('approval_date')
            notes = request.form.get('notes', '')
            
            success, message = self.status_manager.approve_resignation(resignation_id, approval_date, notes)
            
            if success:
                flash(message, 'success')
            else:
                flash(message, 'error')
            
            return redirect(request.referrer or url_for('employee_status_dashboard'))
        
        # ================================
        # الاستيداع
        # ================================
        
        @self.app.route('/employee_status/leave_of_absence/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_leave_of_absence(employee_id):
            """إضافة استيداع"""
            if request.method == 'POST':
                data = {
                    'absence_type': request.form.get('absence_type'),
                    'start_date': request.form.get('start_date'),
                    'expected_duration_months': int(request.form.get('expected_duration_months', 12)),
                    'reason': request.form.get('reason'),
                    'decision_number': request.form.get('decision_number'),
                    'decision_date': request.form.get('decision_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_leave_of_absence(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('employee_status_history', employee_id=employee_id))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_leave_of_absence.html', employee=employee)
        
        # ================================
        # الوفاة
        # ================================
        
        @self.app.route('/employee_status/death/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_death_record(employee_id):
            """تسجيل وفاة"""
            if request.method == 'POST':
                data = {
                    'death_date': request.form.get('death_date'),
                    'death_place': request.form.get('death_place'),
                    'death_cause': request.form.get('death_cause'),
                    'certificate_number': request.form.get('certificate_number'),
                    'certificate_date': request.form.get('certificate_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_death_record(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('employee_status_history', employee_id=employee_id))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_death.html', employee=employee)
        
        # ================================
        # التوقيف
        # ================================
        
        @self.app.route('/employee_status/suspension/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_suspension(employee_id):
            """إضافة توقيف"""
            if request.method == 'POST':
                data = {
                    'suspension_date': request.form.get('suspension_date'),
                    'reason': request.form.get('reason'),
                    'suspension_type': request.form.get('suspension_type'),
                    'duration_days': int(request.form.get('duration_days', 0)) if request.form.get('duration_days') else None,
                    'decision_number': request.form.get('decision_number'),
                    'decision_date': request.form.get('decision_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_suspension(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('employee_status_history', employee_id=employee_id))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_suspension.html', employee=employee)
        
        # ================================
        # التقاعد
        # ================================
        
        @self.app.route('/employee_status/retirement/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_retirement(employee_id):
            """إضافة تقاعد"""
            if request.method == 'POST':
                data = {
                    'retirement_date': request.form.get('retirement_date'),
                    'retirement_type': request.form.get('retirement_type'),
                    'years_of_service': int(request.form.get('years_of_service', 0)),
                    'pension_amount': float(request.form.get('pension_amount', 0)),
                    'decision_number': request.form.get('decision_number'),
                    'decision_date': request.form.get('decision_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_retirement(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('employee_status_history', employee_id=employee_id))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_retirement.html', employee=employee)
        
        # ================================
        # التحويل الخارجي
        # ================================
        
        @self.app.route('/employee_status/external_transfer/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_external_transfer(employee_id):
            """إضافة تحويل خارجي"""
            if request.method == 'POST':
                data = {
                    'transfer_date': request.form.get('transfer_date'),
                    'destination_organization': request.form.get('destination_organization'),
                    'destination_department': request.form.get('destination_department'),
                    'reason': request.form.get('reason'),
                    'decision_number': request.form.get('decision_number'),
                    'decision_date': request.form.get('decision_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_external_transfer(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('external_transfers_list'))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_external_transfer.html', employee=employee)
        
        @self.app.route('/external_transfers')
        def external_transfers_list():
            """قائمة التحويلات الخارجية"""
            transfers = self.status_manager.get_external_transfers()
            return render_template('employee_status/external_transfers.html', transfers=transfers)
        
        # ================================
        # الانتداب
        # ================================
        
        @self.app.route('/employee_status/assignment/add/<int:employee_id>', methods=['GET', 'POST'])
        def add_assignment(employee_id):
            """إضافة انتداب"""
            if request.method == 'POST':
                data = {
                    'assignment_date': request.form.get('assignment_date'),
                    'assignment_location': request.form.get('assignment_location'),
                    'assignment_type': request.form.get('assignment_type'),
                    'expected_duration_months': int(request.form.get('expected_duration_months', 12)),
                    'decision_number': request.form.get('decision_number'),
                    'decision_date': request.form.get('decision_date'),
                    'notes': request.form.get('notes')
                }
                
                success, message = self.status_manager.add_assignment(employee_id, data)
                
                if success:
                    flash(message, 'success')
                    return redirect(url_for('employee_status_history', employee_id=employee_id))
                else:
                    flash(message, 'error')
            
            # الحصول على بيانات الموظف
            conn = self.status_manager.get_db_connection()
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            conn.close()
            
            return render_template('employee_status/add_assignment.html', employee=employee)
        
        # ================================
        # API للحصول على البيانات
        # ================================
        
        @self.app.route('/api/employee_status/<int:employee_id>')
        def api_employee_status(employee_id):
            """API للحصول على حالة الموظف"""
            from employee_status_manager import get_employee_current_status
            status = get_employee_current_status(employee_id)
            
            if status:
                return jsonify(status)
            else:
                return jsonify({'error': 'الموظف غير موجود'}), 404
        
        @self.app.route('/api/employee_status/statistics')
        def api_status_statistics():
            """API لإحصائيات الحالات"""
            stats = self.status_manager.get_status_statistics()
            return jsonify(stats)
        
        @self.app.route('/api/employees/by_status/<status>')
        def api_employees_by_status(status):
            """API للحصول على الموظفين حسب الحالة"""
            employees = self.status_manager.get_employees_by_status(status)
            
            # تحويل النتائج إلى قاموس
            result = []
            for emp in employees:
                result.append({
                    'id': emp['id'],
                    'registration_number': emp['registration_number'],
                    'first_name': emp['first_name'],
                    'last_name': emp['last_name'],
                    'rank_name': emp['rank_name'],
                    'service_name': emp['service_name'],
                    'status': emp['status']
                })
            
            return jsonify(result)


def initialize_status_integration(app, db_path='customs_employees.db'):
    """تهيئة تكامل حالات الموظفين"""
    return StatusIntegration(app, db_path)


# ================================
# وظائف مساعدة للقوالب
# ================================

def register_template_helpers(app):
    """تسجيل الوظائف المساعدة للقوالب"""
    
    @app.template_filter('status_badge_class')
    def status_badge_class(status):
        """فئة CSS لشارة الحالة"""
        status_classes = {
            'نشط': 'badge-success',
            'محول خارجياً': 'badge-warning',
            'متوفى': 'badge-dark',
            'مستقيل': 'badge-secondary',
            'متقاعد': 'badge-info',
            'موقوف': 'badge-danger',
            'مستودع': 'badge-warning',
            'في عطلة طويلة الأمد': 'badge-primary',
            'منتدب': 'badge-info',
            'استقالة معلقة': 'badge-warning'
        }
        return status_classes.get(status, 'badge-secondary')
    
    @app.template_filter('format_arabic_date')
    def format_arabic_date(date_str):
        """تنسيق التاريخ بالعربية"""
        if not date_str:
            return ''
        
        try:
            if isinstance(date_str, str):
                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                date_obj = date_str
            
            months = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ]
            
            return f"{date_obj.day} {months[date_obj.month-1]} {date_obj.year}"
        except:
            return date_str