/* نظام إدارة موظفي الجمارك الجزائرية - التصميم الجزائري التراثي الاحترافي */

/* استيراد خط عربي جميل */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700;900&display=swap');

/* الخطوط والإعدادات الأساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Amiri', sans-serif;
    direction: rtl;
    text-align: right;
    background: linear-gradient(135deg, #00b894 0%, #ffffff 50%, #e17055 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* خلفية بزخارف إسلامية */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 184, 148, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(225, 112, 85, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="islamic-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 18,10 10,18 2,10" fill="none" stroke="rgba(0,184,148,0.1)" stroke-width="0.5"/><circle cx="10" cy="10" r="1" fill="rgba(225,112,85,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23islamic-pattern)"/></svg>');
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-5px); }
    50% { transform: translateX(10px) translateY(5px); }
    75% { transform: translateX(-5px) translateY(10px); }
}

/* الهيدر الرئيسي - تصميم جزائري احترافي */
.main-header {
    background: linear-gradient(135deg, #00b894 0%, #2d3436 50%, #e17055 100%);
    padding: 20px 0;
    box-shadow: 0 8px 32px rgba(0, 184, 148, 0.3);
    position: relative;
    overflow: hidden;
    border-bottom: 3px solid #fdcb6e;
}

.main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><pattern id="islamic-header" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,5 L35,20 L20,35 L5,20 Z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/><circle cx="20" cy="20" r="2" fill="rgba(253,203,110,0.2)"/></pattern></defs><rect width="200" height="200" fill="url(%23islamic-header)"/></svg>');
    opacity: 0.4;
    animation: headerPattern 15s linear infinite;
}

@keyframes headerPattern {
    0% { transform: translateX(0); }
    100% { transform: translateX(40px); }
}

.main-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00b894, #fdcb6e, #e17055, #fdcb6e, #00b894);
    background-size: 200% 100%;
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    z-index: 2;
}

.logo-placeholder {
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, #00b894, #fdcb6e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28px;
    box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
    position: relative;
    border: 3px solid rgba(255, 255, 255, 0.3);
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    0% { box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4); }
    100% { box-shadow: 0 12px 35px rgba(0, 184, 148, 0.6); }
}

.logo-placeholder::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00b894, #fdcb6e, #e17055);
    z-index: -1;
    animation: logoRotate 10s linear infinite;
}

@keyframes logoRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.logo-text h4 {
    color: #ffffff;
    font-weight: 900;
    margin: 0;
    font-size: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Amiri', serif;
}

.logo-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 13px;
    font-weight: 600;
}

/* التنقل الرئيسي */
.main-nav {
    position: relative;
    z-index: 2;
}

.nav-list {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    padding: 15px 25px;
    border-radius: 30px;
    transition: all 0.4s ease;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    font-size: 16px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(253, 203, 110, 0.3), transparent);
    transition: left 0.6s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background: linear-gradient(45deg, rgba(253, 203, 110, 0.2), rgba(255, 255, 255, 0.1));
    color: #fdcb6e;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(253, 203, 110, 0.3);
    border: 1px solid rgba(253, 203, 110, 0.5);
}

/* القوائم المنسدلة */
.dropdown-menu {
    background: rgba(44, 62, 80, 0.95);
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    margin-top: 10px;
    padding: 10px 0;
}

.dropdown-menu li a {
    color: #ecf0f1;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.dropdown-menu li a:hover {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

/* معلومات المستخدم */
.user-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
    position: relative;
    z-index: 2;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ecf0f1;
    font-weight: 500;
}

.user-info i {
    font-size: 24px;
    color: #3498db;
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-links a {
    width: 35px;
    height: 35px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ecf0f1;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #3498db;
    transform: translateY(-2px);
}

/* المحتوى الرئيسي */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 40px 0;
}

/* الصفحة الرئيسية - تصميم جزائري احترافي */
.hero-section {
    background: linear-gradient(135deg, #00b894 0%, rgba(0, 184, 148, 0.8) 50%, #e17055 100%);
    position: relative;
    padding: 120px 0;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><defs><pattern id="hero-pattern" width="80" height="80" patternUnits="userSpaceOnUse"><path d="M40,10 L70,40 L40,70 L10,40 Z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/><circle cx="40" cy="40" r="3" fill="rgba(253,203,110,0.3)"/><path d="M20,20 L60,20 L60,60 L20,60 Z" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="400" height="400" fill="url(%23hero-pattern)"/></svg>');
    animation: heroPattern 25s linear infinite;
}

@keyframes heroPattern {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
    100% { transform: translateX(0) translateY(0); }
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(253, 203, 110, 0.1) 0%, transparent 70%);
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 25px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
    font-family: 'Amiri', serif;
    line-height: 1.2;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4); }
    100% { text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4), 0 0 20px rgba(253, 203, 110, 0.5); }
}

.hero-subtitle {
    font-size: 1.8rem;
    margin-bottom: 50px;
    opacity: 0.95;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* الأيقونات السداسية - تصميم جزائري احترافي */
.hexagon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    padding: 80px 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.hexagon-item {
    position: relative;
    text-align: center;
    cursor: pointer;
    transition: all 0.5s ease;
    padding: 20px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hexagon-item:hover {
    transform: translateY(-15px) scale(1.05);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px rgba(0, 184, 148, 0.3);
}

.hexagon {
    width: 140px;
    height: 140px;
    background: linear-gradient(135deg, #00b894, #fdcb6e, #e17055);
    margin: 0 auto 25px;
    position: relative;
    border-radius: 50%;
    transition: all 0.5s ease;
    box-shadow: 0 15px 35px rgba(0, 184, 148, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.hexagon::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00b894, #fdcb6e, #e17055, #00b894);
    z-index: -1;
    animation: hexagonRotate 8s linear infinite;
}

@keyframes hexagonRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hexagon-icon {
    font-size: 48px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    z-index: 2;
    animation: iconFloat 3s ease-in-out infinite alternate;
}

@keyframes iconFloat {
    0% { transform: translateY(0); }
    100% { transform: translateY(-5px); }
}

.hexagon-item:hover .hexagon {
    transform: scale(1.1);
    box-shadow: 0 20px 45px rgba(0, 184, 148, 0.6);
}

.hexagon-item h5 {
    color: #2d3436;
    font-weight: 700;
    margin-bottom: 15px;
    font-size: 18px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    font-family: 'Amiri', serif;
}

.hexagon-item p {
    color: #636e72;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.5;
}

/* الفوتر */
.main-footer {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 30px 0;
    margin-top: 60px;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    margin: 30px 0;
}

.table {
    margin: 0;
}

.table thead th {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 15px;
    font-weight: 600;
}

.table tbody tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* الأزرار - تصميم جزائري احترافي */
.btn-primary {
    background: linear-gradient(135deg, #00b894, #fdcb6e);
    border: none;
    border-radius: 30px;
    padding: 15px 40px;
    font-weight: 700;
    font-size: 16px;
    transition: all 0.4s ease;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 184, 148, 0.5);
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.btn-success {
    background: linear-gradient(135deg, #00b894, #00a085);
    border: none;
    border-radius: 30px;
    padding: 15px 40px;
    font-weight: 700;
    transition: all 0.4s ease;
    color: white;
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 184, 148, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    border: none;
    border-radius: 30px;
    padding: 15px 40px;
    font-weight: 700;
    transition: all 0.4s ease;
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
    border: none;
    border-radius: 30px;
    padding: 15px 40px;
    font-weight: 700;
    transition: all 0.4s ease;
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #636e72, #2d3436);
    border: none;
    border-radius: 30px;
    padding: 15px 40px;
    font-weight: 700;
    transition: all 0.4s ease;
    color: white;
}

/* الكروت */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* تحسينات إضافية للتصميم الجزائري */
.hero-badges .badge {
    font-size: 14px;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    margin: 0 5px;
    animation: badgeFloat 2s ease-in-out infinite alternate;
}

@keyframes badgeFloat {
    0% { transform: translateY(0); }
    100% { transform: translateY(-3px); }
}

.features-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 30px;
    margin: 40px 20px;
    padding: 60px 20px;
    box-shadow: 0 20px 40px rgba(0, 184, 148, 0.1);
}

.feature-icon {
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* تأثيرات خاصة للنصوص */
.section-title {
    font-family: 'Amiri', serif;
    font-weight: 700;
    color: #2d3436;
    text-shadow: 2px 2px 4px rgba(0, 184, 148, 0.1);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-list {
        flex-direction: column;
        gap: 10px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hexagon-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        padding: 40px 10px;
    }

    .logo-section {
        flex-direction: column;
        text-align: center;
    }

    .hexagon {
        width: 100px;
        height: 100px;
    }

    .hexagon-icon {
        font-size: 36px;
    }

    .hero-badges {
        flex-direction: column;
        gap: 10px;
    }

    .hero-badges .badge {
        display: block;
        margin: 5px auto;
        width: fit-content;
    }
}

/* تأثيرات تحميل الصفحة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hexagon-item {
    animation: fadeInUp 0.6s ease-out;
}

.hexagon-item:nth-child(1) { animation-delay: 0.1s; }
.hexagon-item:nth-child(2) { animation-delay: 0.2s; }
.hexagon-item:nth-child(3) { animation-delay: 0.3s; }
.hexagon-item:nth-child(4) { animation-delay: 0.4s; }
.hexagon-item:nth-child(5) { animation-delay: 0.5s; }
.hexagon-item:nth-child(6) { animation-delay: 0.6s; }
.hexagon-item:nth-child(7) { animation-delay: 0.7s; }
.hexagon-item:nth-child(8) { animation-delay: 0.8s; }
