/* نظام إدارة موظفي الجمارك الجزائرية - تصميم مستوحى من موقع بريد الجزائر */

/* الخطوط والإعدادات الأساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* الهيدر الرئيسي */
.main-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    padding: 15px 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    z-index: 2;
}

.logo-placeholder {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.logo-text h4 {
    color: #ecf0f1;
    font-weight: 700;
    margin: 0;
    font-size: 18px;
}

.logo-text p {
    color: #bdc3c7;
    margin: 0;
    font-size: 12px;
}

/* التنقل الرئيسي */
.main-nav {
    position: relative;
    z-index: 2;
}

.nav-list {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: #ecf0f1;
    text-decoration: none;
    padding: 12px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: #3498db;
    transform: translateY(-2px);
}

/* القوائم المنسدلة */
.dropdown-menu {
    background: rgba(44, 62, 80, 0.95);
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    margin-top: 10px;
    padding: 10px 0;
}

.dropdown-menu li a {
    color: #ecf0f1;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.dropdown-menu li a:hover {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

/* معلومات المستخدم */
.user-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
    position: relative;
    z-index: 2;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ecf0f1;
    font-weight: 500;
}

.user-info i {
    font-size: 24px;
    color: #3498db;
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-links a {
    width: 35px;
    height: 35px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ecf0f1;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #3498db;
    transform: translateY(-2px);
}

/* المحتوى الرئيسي */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 40px 0;
}

/* الصفحة الرئيسية */
.hero-section {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23667eea;stop-opacity:1" /><stop offset="100%" style="stop-color:%23764ba2;stop-opacity:1" /></linearGradient></defs><rect width="1200" height="600" fill="url(%23bg)"/></svg>') center/cover;
    position: relative;
    padding: 100px 0;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

/* الأيقونات السداسية */
.hexagon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    padding: 60px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.hexagon-item {
    position: relative;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hexagon {
    width: 120px;
    height: 104px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    margin: 0 auto 20px;
    position: relative;
    transform: rotate(30deg);
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
}

.hexagon::before,
.hexagon::after {
    content: '';
    position: absolute;
    width: 0;
    border-left: 60px solid transparent;
    border-right: 60px solid transparent;
}

.hexagon::before {
    bottom: 100%;
    border-bottom: 30px solid #3498db;
}

.hexagon::after {
    top: 100%;
    border-top: 30px solid #2980b9;
}

.hexagon-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-30deg);
    font-size: 32px;
    color: white;
}

.hexagon-item:hover .hexagon {
    transform: rotate(30deg) scale(1.1);
    box-shadow: 0 15px 40px rgba(52, 152, 219, 0.5);
}

.hexagon-item h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
}

.hexagon-item p {
    color: #7f8c8d;
    font-size: 14px;
}

/* الفوتر */
.main-footer {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 30px 0;
    margin-top: 60px;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    margin: 30px 0;
}

.table {
    margin: 0;
}

.table thead th {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 15px;
    font-weight: 600;
}

.table tbody tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
}

/* الكروت */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-list {
        flex-direction: column;
        gap: 10px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hexagon-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .logo-section {
        flex-direction: column;
        text-align: center;
    }
}
