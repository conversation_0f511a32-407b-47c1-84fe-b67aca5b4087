{% extends "base.html" %}

{% block title %}العقوبات والمكافآت - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة العقوبات والمكافآت{% endblock %}

{% block content %}
<!-- إحصائيات -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
            <i class="fas fa-gavel fa-2x mb-2"></i>
            <h3>0</h3>
            <p>العقوبات</p>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <i class="fas fa-award fa-2x mb-2"></i>
            <h3>0</h3>
            <p>المكافآت</p>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>إضافة جديد
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="addSanction()">
                    <i class="fas fa-gavel me-2"></i>إضافة عقوبة
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="addReward()">
                    <i class="fas fa-award me-2"></i>إضافة مكافأة
                </a></li>
            </ul>
        </div>
        <button class="btn btn-success" onclick="exportData()">
            <i class="fas fa-file-excel me-2"></i>تصدير البيانات
        </button>
    </div>
</div>

<!-- العقوبات -->
<div class="card mb-4">
    <div class="card-header">
        <h4><i class="fas fa-gavel me-2"></i>العقوبات</h4>
    </div>
    <div class="card-body">
        <div class="text-center py-5">
            <i class="fas fa-gavel fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عقوبات مسجلة</h5>
            <p class="text-muted">ابدأ بإضافة عقوبة جديدة لعرض البيانات هنا</p>
            <button class="btn btn-danger" onclick="addSanction()">
                <i class="fas fa-plus me-2"></i>إضافة عقوبة جديدة
            </button>
        </div>
    </div>
</div>

<!-- المكافآت -->
<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-award me-2"></i>المكافآت</h4>
    </div>
    <div class="card-body">
        <div class="text-center py-5">
            <i class="fas fa-award fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مكافآت مسجلة</h5>
            <p class="text-muted">ابدأ بإضافة مكافأة جديدة لعرض البيانات هنا</p>
            <button class="btn btn-success" onclick="addReward()">
                <i class="fas fa-plus me-2"></i>إضافة مكافأة جديدة
            </button>
        </div>
    </div>
</div>

<!-- Modal إضافة عقوبة -->
<div class="modal fade" id="sanctionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-gavel me-2"></i>إضافة عقوبة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="sanctionForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف <span class="required">*</span></label>
                                <select name="employee_id" class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">درجة العقوبة <span class="required">*</span></label>
                                <select name="sanction_degree" class="form-select" required>
                                    <option value="">اختر الدرجة</option>
                                    <option value="الدرجة الأولى">الدرجة الأولى</option>
                                    <option value="الدرجة الثانية">الدرجة الثانية</option>
                                    <option value="الدرجة الثالثة">الدرجة الثالثة</option>
                                    <option value="الدرجة الرابعة">الدرجة الرابعة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تحديد العقوبة <span class="required">*</span></label>
                                <select name="sanction_type" class="form-select" required>
                                    <option value="">اختر نوع العقوبة</option>
                                    <option value="إنذار">إنذار</option>
                                    <option value="توبيخ">توبيخ</option>
                                    <option value="خصم من الراتب">خصم من الراتب</option>
                                    <option value="توقيف مؤقت">توقيف مؤقت</option>
                                    <option value="تنزيل في الدرجة">تنزيل في الدرجة</option>
                                    <option value="تنزيل في الرتبة">تنزيل في الرتبة</option>
                                    <option value="إحالة على التقاعد">إحالة على التقاعد</option>
                                    <option value="طرد">طرد</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ مقرر العقوبة</label>
                                <input type="date" name="decision_date" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم مقرر العقوبة</label>
                                <input type="text" name="decision_number" class="form-control" placeholder="رقم المقرر">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ تطبيق العقوبة</label>
                                <input type="date" name="application_date" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">سبب العقوبة</label>
                        <textarea name="reason" class="form-control" rows="3" placeholder="اذكر سبب العقوبة بالتفصيل"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">حفظ العقوبة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة مكافأة -->
<div class="modal fade" id="rewardModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-award me-2"></i>إضافة مكافأة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rewardForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف <span class="required">*</span></label>
                                <select name="employee_id" class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المكافأة <span class="required">*</span></label>
                                <select name="reward_type" class="form-select" required>
                                    <option value="">اختر النوع</option>
                                    <option value="مادية">مادية</option>
                                    <option value="معنوية">معنوية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تحديد المكافأة <span class="required">*</span></label>
                                <select name="reward_specification" class="form-select" required>
                                    <option value="">اختر المكافأة</option>
                                    <option value="شهادة تقدير">شهادة تقدير</option>
                                    <option value="وسام">وسام</option>
                                    <option value="مكافأة مالية">مكافأة مالية</option>
                                    <option value="ترقية استثنائية">ترقية استثنائية</option>
                                    <option value="إجازة إضافية">إجازة إضافية</option>
                                    <option value="تهنئة رسمية">تهنئة رسمية</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ المنح</label>
                                <input type="date" name="grant_date" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الهيئة المانحة</label>
                                <input type="text" name="granting_authority" class="form-control" placeholder="اسم الهيئة المانحة">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم المقرر</label>
                                <input type="text" name="decision_number" class="form-control" placeholder="رقم مقرر المكافأة">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">سبب المنح</label>
                        <textarea name="reason" class="form-control" rows="3" placeholder="اذكر سبب منح المكافأة"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ المكافأة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addSanction() {
    // فتح modal العقوبة
    const modal = new bootstrap.Modal(document.getElementById('sanctionModal'));
    modal.show();
}

function addReward() {
    // فتح modal المكافأة
    const modal = new bootstrap.Modal(document.getElementById('rewardModal'));
    modal.show();
}

function exportData() {
    alert('سيتم تصدير بيانات العقوبات والمكافآت إلى Excel');
    // يمكن إضافة وظيفة التصدير الفعلية
}

// معالجة نموذج العقوبة
document.getElementById('sanctionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ العقوبة بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('sanctionModal')).hide();
});

// معالجة نموذج المكافأة
document.getElementById('rewardForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ المكافأة بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('rewardModal')).hide();
});
</script>
{% endblock %}
