#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص قيود قاعدة البيانات وحل مشكلة الحقول الفارغة المكررة
"""

import sqlite3

def check_database_constraints():
    """فحص قيود قاعدة البيانات"""
    print("🔍 فحص قيود قاعدة البيانات")
    print("=" * 60)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    # فحص هيكل الجدول
    cursor.execute("PRAGMA table_info(employees)")
    columns = cursor.fetchall()
    
    print("📋 هيكل جدول الموظفين:")
    for col in columns:
        col_id, name, data_type, not_null, default, pk = col
        constraints = []
        if pk:
            constraints.append("PRIMARY KEY")
        if not_null:
            constraints.append("NOT NULL")
        if default:
            constraints.append(f"DEFAULT {default}")
        
        constraint_str = " | ".join(constraints) if constraints else "لا توجد قيود"
        print(f"  {name:<30} {data_type:<10} {constraint_str}")
    
    # فحص الفهارس والقيود الفريدة
    cursor.execute("PRAGMA index_list(employees)")
    indexes = cursor.fetchall()
    
    print(f"\n🔑 الفهارس والقيود:")
    for index in indexes:
        index_name = index[1]
        is_unique = index[2]
        cursor.execute(f"PRAGMA index_info({index_name})")
        index_info = cursor.fetchall()
        
        columns_in_index = [info[2] for info in index_info]
        unique_str = "UNIQUE" if is_unique else "INDEX"
        print(f"  {unique_str}: {index_name} على الأعمدة: {', '.join(columns_in_index)}")
    
    # فحص الموظفين الذين لديهم رقم ضمان اجتماعي فارغ
    cursor.execute("""
        SELECT id, first_name, last_name, social_security_number 
        FROM employees 
        WHERE social_security_number IS NULL OR social_security_number = ''
    """)
    empty_ssn_employees = cursor.fetchall()
    
    print(f"\n👥 الموظفون برقم ضمان اجتماعي فارغ:")
    if empty_ssn_employees:
        print(f"  العدد: {len(empty_ssn_employees)}")
        for emp in empty_ssn_employees:
            ssn_display = emp[3] if emp[3] else "NULL"
            print(f"  ID: {emp[0]} - {emp[1]} {emp[2]} - رقم الضمان: '{ssn_display}'")
    else:
        print("  لا يوجد موظفون برقم ضمان اجتماعي فارغ")
    
    # فحص التكرارات في رقم الضمان الاجتماعي
    cursor.execute("""
        SELECT social_security_number, COUNT(*) as count
        FROM employees 
        GROUP BY social_security_number
        HAVING COUNT(*) > 1
    """)
    duplicates = cursor.fetchall()
    
    print(f"\n🔄 التكرارات في رقم الضمان الاجتماعي:")
    if duplicates:
        for dup in duplicates:
            ssn_display = dup[0] if dup[0] else "فارغ/NULL"
            print(f"  رقم الضمان: '{ssn_display}' - عدد التكرارات: {dup[1]}")
    else:
        print("  لا توجد تكرارات")
    
    conn.close()

def fix_empty_ssn_constraint():
    """إصلاح مشكلة القيد الفريد للحقول الفارغة"""
    print(f"\n🔧 إصلاح مشكلة القيد الفريد للحقول الفارغة")
    print("-" * 60)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    try:
        # إزالة القيد الفريد من رقم الضمان الاجتماعي إذا كان موجوداً
        print("🗑️  محاولة إزالة القيد الفريد من رقم الضمان الاجتماعي...")
        
        # في SQLite، لا يمكن إزالة القيود مباشرة، لكن يمكننا إنشاء جدول جديد
        # أولاً، دعنا نتحقق من وجود القيد
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='employees'")
        table_sql = cursor.fetchone()[0]
        
        print(f"📄 SQL الحالي للجدول:")
        print(f"  {table_sql}")
        
        # التحقق من وجود UNIQUE constraint على social_security_number
        if 'social_security_number' in table_sql and 'UNIQUE' in table_sql:
            print("⚠️  تم العثور على قيد UNIQUE على رقم الضمان الاجتماعي")
            print("💡 الحل: سنحتاج لإعادة إنشاء الجدول بدون هذا القيد")
        else:
            print("✅ لا يوجد قيد UNIQUE مباشر على رقم الضمان الاجتماعي")
        
        # فحص الفهارس الفريدة
        cursor.execute("PRAGMA index_list(employees)")
        indexes = cursor.fetchall()
        
        unique_ssn_index = None
        for index in indexes:
            if index[2]:  # is_unique
                cursor.execute(f"PRAGMA index_info({index[1]})")
                index_info = cursor.fetchall()
                columns_in_index = [info[2] for info in index_info]
                if 'social_security_number' in columns_in_index:
                    unique_ssn_index = index[1]
                    break
        
        if unique_ssn_index:
            print(f"🗑️  إزالة الفهرس الفريد: {unique_ssn_index}")
            cursor.execute(f"DROP INDEX IF EXISTS {unique_ssn_index}")
            print("✅ تم إزالة الفهرس الفريد")
        else:
            print("✅ لا يوجد فهرس فريد على رقم الضمان الاجتماعي")
        
        conn.commit()
        print("✅ تم حفظ التغييرات")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
    finally:
        conn.close()

def test_duplicate_empty_ssn():
    """اختبار إضافة موظفين برقم ضمان اجتماعي فارغ"""
    print(f"\n🧪 اختبار إضافة موظفين برقم ضمان اجتماعي فارغ")
    print("-" * 60)
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    try:
        # محاولة إضافة موظف برقم ضمان اجتماعي فارغ
        test_data = {
            'registration_number': '999998',
            'first_name': 'اختبار',
            'last_name': 'فارغ1',
            'social_security_number': '',  # فارغ
        }
        
        cursor.execute("""
            INSERT INTO employees (registration_number, first_name, last_name, social_security_number)
            VALUES (?, ?, ?, ?)
        """, (test_data['registration_number'], test_data['first_name'], 
              test_data['last_name'], test_data['social_security_number']))
        
        print("✅ تم إضافة الموظف الأول برقم ضمان فارغ")
        
        # محاولة إضافة موظف آخر برقم ضمان اجتماعي فارغ
        test_data2 = {
            'registration_number': '999997',
            'first_name': 'اختبار',
            'last_name': 'فارغ2',
            'social_security_number': '',  # فارغ أيضاً
        }
        
        cursor.execute("""
            INSERT INTO employees (registration_number, first_name, last_name, social_security_number)
            VALUES (?, ?, ?, ?)
        """, (test_data2['registration_number'], test_data2['first_name'], 
              test_data2['last_name'], test_data2['social_security_number']))
        
        print("✅ تم إضافة الموظف الثاني برقم ضمان فارغ")
        print("🎉 لا توجد مشكلة في إضافة موظفين برقم ضمان فارغ")
        
        conn.commit()
        
    except sqlite3.IntegrityError as e:
        print(f"❌ خطأ في التكامل: {e}")
        print("🚨 المشكلة: يوجد قيد فريد على رقم الضمان الاجتماعي")
        conn.rollback()
    except Exception as e:
        print(f"❌ خطأ آخر: {e}")
        conn.rollback()
    finally:
        # حذف البيانات التجريبية
        try:
            cursor.execute("DELETE FROM employees WHERE registration_number IN ('999998', '999997')")
            conn.commit()
            print("🗑️  تم حذف البيانات التجريبية")
        except:
            pass
        conn.close()

if __name__ == "__main__":
    check_database_constraints()
    fix_empty_ssn_constraint()
    test_duplicate_empty_ssn()