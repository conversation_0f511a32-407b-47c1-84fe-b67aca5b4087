#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام شامل لإدارة حالات الموظفين - الجمارك الجزائرية
Comprehensive Employee Status Management System - Algerian Customs
"""

import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple, Any
import json

class ComprehensiveEmployeeStatusSystem:
    """النظام الشامل لإدارة حالات الموظفين"""
    
    def __init__(self, db_path: str = 'customs_employees.db'):
        self.db_path = db_path
        
        # تصنيف الحالات
        self.permanent_removal_statuses = {
            'death': 'متوفى',
            'external_transfer': 'محول خارجياً', 
            'dismissal': 'معزول',
            'retirement': 'متقاعد'
        }
        
        self.temporary_statuses = {
            'leave_of_absence': 'مستودع',
            'suspension': 'موقوف',
            'resignation': 'مستقيل',
            'national_service': 'في الخدمة الوطنية',
            'long_term_leave': 'في عطلة طويلة الأمد',
            'assignment': 'منتدب',
            'study_training': 'في دراسة/تكوين'
        }
        
        # أسباب الاستيداع الافتراضية
        self.leave_of_absence_reasons = [
            'رعاية الأطفال',
            'الدراسة',
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def create_status_tables(self):
        """إنشاء جداول حالات الموظفين"""
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        try:
            # جدول أسباب الاستيداع (قابل للتعديل)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    reason TEXT NOT NULL UNIQUE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إدراج الأسباب الافتراضية
            for reason in self.leave_of_absence_reasons:
                cursor.execute('''
                    INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
                    VALUES (?)
                ''', (reason,))
            
            # ================================
            # جداول الحالات المؤقتة
            # ================================
            
            # 1. جدول الاستيداع
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_leave_of_absence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    period_number INTEGER NOT NULL, -- الفترة (الأولى، الثانية...)
                    duration_months INTEGER NOT NULL, -- المدة بالأشهر
                    reason_id INTEGER NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL, -- محسوب تلقائياً
                    decision_number TEXT,
                    decision_date DATE,
                    total_previous_months INTEGER DEFAULT 0, -- إجمالي الأشهر السابقة
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id),
                    FOREIGN KEY (reason_id) REFERENCES leave_of_absence_reasons (id)
                )
            ''')
            
            # 2. جدول التوقيف
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_suspension (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    suspension_date DATE NOT NULL,
                    suspension_reason TEXT NOT NULL,
                    decision_number TEXT NOT NULL,
                    decision_date DATE NOT NULL,
                    end_date DATE, -- إذا كان التوقيف مؤقت
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # 3. جدول الاستقالة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_resignation (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    request_date DATE NOT NULL,
                    resignation_reason TEXT NOT NULL,
                    is_accepted BOOLEAN, -- NULL = قيد الدراسة، 1 = مقبولة، 0 = مرفوضة
                    decision_number TEXT,
                    decision_date DATE,
                    effective_date DATE, -- تاريخ سريان الاستقالة
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # 4. جدول الخدمة الوطنية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_national_service (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    decision_number TEXT NOT NULL,
                    decision_date DATE NOT NULL,
                    service_location TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # 5. جدول عطلة طويلة الأمد
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_long_term_leave (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    start_date DATE NOT NULL,
                    review_date DATE NOT NULL,
                    document_number TEXT NOT NULL,
                    document_date DATE NOT NULL,
                    granting_authority TEXT NOT NULL, -- الهيئة المانحة
                    leave_reason TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # 6. جدول الانتداب
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_assignment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    assignment_date DATE NOT NULL,
                    duration_months INTEGER NOT NULL,
                    assignment_reason TEXT NOT NULL,
                    assignment_location TEXT NOT NULL,
                    end_date DATE NOT NULL, -- محسوب تلقائياً
                    decision_number TEXT,
                    decision_date DATE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # 7. جدول الدراسة/التكوين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_study_training (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    start_date DATE NOT NULL,
                    study_type TEXT NOT NULL, -- نوع التكوين أو الدراسة
                    decision_number TEXT NOT NULL,
                    decision_date DATE NOT NULL,
                    institution TEXT, -- المؤسسة
                    expected_end_date DATE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # ================================
            # جداول الحالات النهائية (الحذف)
            # ================================
            
            # 1. جدول الوفيات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS deceased_employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    -- نسخ جميع بيانات الموظف
                    original_employee_id INTEGER NOT NULL,
                    registration_number TEXT NOT NULL,
                    first_name TEXT NOT NULL,
                    last_name TEXT NOT NULL,
                    birth_date DATE,
                    hire_date DATE,
                    -- بيانات الوفاة
                    death_date DATE NOT NULL,
                    death_cause TEXT NOT NULL CHECK (death_cause IN ('عادية', 'حادث عمل')),
                    death_certificate_number TEXT NOT NULL,
                    -- معلومات إضافية
                    employee_data TEXT, -- JSON لجميع بيانات الموظف
                    transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    transferred_by TEXT
                )
            ''')
            
            # 2. جدول التحويل الخارجي
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS external_transfer_employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    -- نسخ جميع بيانات الموظف
                    original_employee_id INTEGER NOT NULL,
                    registration_number TEXT NOT NULL,
                    first_name TEXT NOT NULL,
                    last_name TEXT NOT NULL,
                    birth_date DATE,
                    hire_date DATE,
                    -- بيانات التحويل
                    transfer_date DATE NOT NULL,
                    destination_directorate TEXT NOT NULL,
                    decision_number TEXT NOT NULL,
                    decision_date DATE NOT NULL,
                    -- معلومات إضافية
                    employee_data TEXT, -- JSON لجميع بيانات الموظف
                    transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    transferred_by TEXT
                )
            ''')
            
            # 3. جدول العزل
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dismissed_employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    -- نسخ جميع بيانات الموظف
                    original_employee_id INTEGER NOT NULL,
                    registration_number TEXT NOT NULL,
                    first_name TEXT NOT NULL,
                    last_name TEXT NOT NULL,
                    birth_date DATE,
                    hire_date DATE,
                    -- بيانات العزل
                    dismissal_date DATE NOT NULL,
                    dismissal_reason TEXT NOT NULL,
                    decision_number TEXT NOT NULL,
                    decision_date DATE NOT NULL,
                    -- معلومات إضافية
                    employee_data TEXT, -- JSON لجميع بيانات الموظف
                    transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    transferred_by TEXT
                )
            ''')
            
            # 4. جدول التقاعد
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS retired_employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    -- نسخ جميع بيانات الموظف
                    original_employee_id INTEGER NOT NULL,
                    registration_number TEXT NOT NULL,
                    first_name TEXT NOT NULL,
                    last_name TEXT NOT NULL,
                    birth_date DATE,
                    hire_date DATE,
                    -- بيانات التقاعد
                    retirement_date DATE NOT NULL,
                    retirement_decision_number TEXT NOT NULL,
                    retirement_decision_date DATE NOT NULL,
                    retirement_card_number TEXT,
                    retirement_card_issue_date DATE,
                    -- معلومات إضافية
                    employee_data TEXT, -- JSON لجميع بيانات الموظف
                    transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    transferred_by TEXT
                )
            ''')
            
            # جدول تاريخ تغييرات الحالات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_status_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    old_status TEXT,
                    new_status TEXT NOT NULL,
                    change_date DATE NOT NULL,
                    change_reason TEXT,
                    decision_number TEXT,
                    decision_date DATE,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT
                )
            ''')
            
            conn.commit()
            print("✅ تم إنشاء جميع جداول حالات الموظفين")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    # ================================
    # دوال الحالات المؤقتة
    # ================================
    
    def add_leave_of_absence(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة استيداع"""
        conn = self.get_db_connection()
        try:
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # حساب إجمالي أشهر الاستيداع السابقة
            total_previous = conn.execute('''
                SELECT COALESCE(SUM(duration_months), 0) 
                FROM employee_leave_of_absence 
                WHERE employee_id = ?
            ''', (employee_id,)).fetchone()[0]
            
            # التحقق من عدم تجاوز 60 شهر (5 سنوات)
            new_duration = int(data['duration_months'])
            if total_previous + new_duration > 60:
                remaining = 60 - total_previous
                return False, f"تجاوز الحد الأقصى للاستيداع. المتبقي: {remaining} شهر"
            
            # حساب تاريخ النهاية
            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            end_date = start_date + timedelta(days=new_duration * 30)  # تقريبي
            
            # حساب رقم الفترة
            period_count = conn.execute('''
                SELECT COUNT(*) FROM employee_leave_of_absence WHERE employee_id = ?
            ''', (employee_id,)).fetchone()[0]
            
            # إدراج البيانات
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO employee_leave_of_absence 
                (employee_id, period_number, duration_months, reason_id, start_date, 
                 end_date, decision_number, decision_date, total_previous_months, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, period_count + 1, new_duration, data['reason_id'],
                start_date, end_date, data.get('decision_number'),
                data.get('decision_date'), total_previous, data.get('created_by')
            ))
            
            # تحديث حالة الموظف
            conn.execute('UPDATE employees SET status = ? WHERE id = ?', 
                        ('مستودع', employee_id))
            
            # إضافة سجل في التاريخ
            self._add_status_history(conn, employee_id, employee['status'], 'مستودع',
                                   start_date, 'استيداع', data.get('decision_number'),
                                   data.get('decision_date'), data.get('created_by'))
            
            conn.commit()
            return True, "تم إضافة الاستيداع بنجاح"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    def add_suspension(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """إضافة توقيف"""
        conn = self.get_db_connection()
        try:
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO employee_suspension 
                (employee_id, suspension_date, suspension_reason, decision_number, 
                 decision_date, end_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, data['suspension_date'], data['suspension_reason'],
                data['decision_number'], data['decision_date'], 
                data.get('end_date'), data.get('created_by')
            ))
            
            # تحديث حالة الموظف
            conn.execute('UPDATE employees SET status = ? WHERE id = ?', 
                        ('موقوف', employee_id))
            
            # إضافة سجل في التاريخ
            self._add_status_history(conn, employee_id, employee['status'], 'موقوف',
                                   data['suspension_date'], 'توقيف', 
                                   data['decision_number'], data['decision_date'], 
                                   data.get('created_by'))
            
            conn.commit()
            return True, "تم إضافة التوقيف بنجاح"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    # ================================
    # دوال الحالات النهائية (الحذف)
    # ================================
    
    def transfer_to_death(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """نقل الموظف لجدول الوفيات وحذفه من جدول الموظفين"""
        conn = self.get_db_connection()
        try:
            # الحصول على بيانات الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # تحويل بيانات الموظف إلى JSON
            employee_data = dict(employee)
            employee_json = json.dumps(employee_data, ensure_ascii=False, default=str)
            
            cursor = conn.cursor()
            
            # إدراج في جدول الوفيات
            cursor.execute('''
                INSERT INTO deceased_employees 
                (original_employee_id, registration_number, first_name, last_name,
                 birth_date, hire_date, death_date, death_cause, death_certificate_number,
                 employee_data, transferred_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, employee['registration_number'], employee['first_name'],
                employee['last_name'], employee['birth_date'], employee['hire_date'],
                data['death_date'], data['death_cause'], data['death_certificate_number'],
                employee_json, data.get('transferred_by')
            ))
            
            # إضافة سجل في التاريخ
            self._add_status_history(conn, employee_id, employee['status'], 'متوفى',
                                   data['death_date'], 'وفاة', None, None, 
                                   data.get('transferred_by'))
            
            # حذف من جدول الموظفين
            conn.execute('DELETE FROM employees WHERE id = ?', (employee_id,))
            
            conn.commit()
            return True, "تم نقل الموظف لجدول الوفيات"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    def transfer_to_external(self, employee_id: int, data: Dict) -> Tuple[bool, str]:
        """نقل الموظف لجدول التحويل الخارجي"""
        conn = self.get_db_connection()
        try:
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            employee_data = dict(employee)
            employee_json = json.dumps(employee_data, ensure_ascii=False, default=str)
            
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO external_transfer_employees 
                (original_employee_id, registration_number, first_name, last_name,
                 birth_date, hire_date, transfer_date, destination_directorate,
                 decision_number, decision_date, employee_data, transferred_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, employee['registration_number'], employee['first_name'],
                employee['last_name'], employee['birth_date'], employee['hire_date'],
                data['transfer_date'], data['destination_directorate'],
                data['decision_number'], data['decision_date'],
                employee_json, data.get('transferred_by')
            ))
            
            self._add_status_history(conn, employee_id, employee['status'], 'محول خارجياً',
                                   data['transfer_date'], 'تحويل خارجي', 
                                   data['decision_number'], data['decision_date'],
                                   data.get('transferred_by'))
            
            conn.execute('DELETE FROM employees WHERE id = ?', (employee_id,))
            
            conn.commit()
            return True, "تم التحويل الخارجي بنجاح"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    def _add_status_history(self, conn, employee_id, old_status, new_status, 
                           change_date, change_reason, decision_number, 
                           decision_date, created_by):
        """إضافة سجل في تاريخ تغييرات الحالات"""
        conn.execute('''
            INSERT INTO employee_status_history 
            (employee_id, old_status, new_status, change_date, change_reason,
             decision_number, decision_date, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (employee_id, old_status, new_status, change_date, change_reason,
              decision_number, decision_date, created_by))
    
    # ================================
    # دوال الإحصائيات والتقارير
    # ================================
    
    def get_employee_statistics(self) -> Dict:
        """إحصائيات الموظفين حسب الحالات"""
        conn = self.get_db_connection()
        try:
            stats = {}
            
            # الموظفين النشطين
            stats['active'] = conn.execute('''
                SELECT COUNT(*) FROM employees WHERE status = 'نشط'
            ''').fetchone()[0]
            
            # الحالات المؤقتة
            for status_key, status_name in self.temporary_statuses.items():
                stats[status_key] = conn.execute('''
                    SELECT COUNT(*) FROM employees WHERE status = ?
                ''', (status_name,)).fetchone()[0]
            
            # الحالات النهائية
            stats['deceased'] = conn.execute('SELECT COUNT(*) FROM deceased_employees').fetchone()[0]
            stats['external_transfer'] = conn.execute('SELECT COUNT(*) FROM external_transfer_employees').fetchone()[0]
            stats['dismissed'] = conn.execute('SELECT COUNT(*) FROM dismissed_employees').fetchone()[0]
            stats['retired'] = conn.execute('SELECT COUNT(*) FROM retired_employees').fetchone()[0]
            
            # الإجمالي
            stats['total_active'] = sum([stats['active']] + [stats[key] for key in self.temporary_statuses.keys()])
            stats['total_removed'] = stats['deceased'] + stats['external_transfer'] + stats['dismissed'] + stats['retired']
            stats['grand_total'] = stats['total_active'] + stats['total_removed']
            
            return stats
            
        except Exception as e:
            print(f"خطأ في الإحصائيات: {e}")
            return {}
        finally:
            conn.close()

def create_comprehensive_status_system():
    """إنشاء النظام الشامل لحالات الموظفين"""
    system = ComprehensiveEmployeeStatusSystem()
    system.create_status_tables()
    return system

if __name__ == "__main__":
    print("🚀 إنشاء النظام الشامل لإدارة حالات الموظفين")
    print("=" * 60)
    
    system = create_comprehensive_status_system()
    
    # عرض الإحصائيات
    stats = system.get_employee_statistics()
    print(f"\n📊 إحصائيات الموظفين:")
    print(f"   النشطين: {stats.get('active', 0)}")
    print(f"   المستودعين: {stats.get('leave_of_absence', 0)}")
    print(f"   الموقوفين: {stats.get('suspension', 0)}")
    print(f"   المتوفين: {stats.get('deceased', 0)}")
    print(f"   المحولين خارجياً: {stats.get('external_transfer', 0)}")
    print(f"   المتقاعدين: {stats.get('retired', 0)}")
    print(f"   الإجمالي النشط: {stats.get('total_active', 0)}")
    print(f"   الإجمالي العام: {stats.get('grand_total', 0)}")
    
    print(f"\n✅ تم إنشاء النظام الشامل بنجاح!")