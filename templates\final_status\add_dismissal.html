{% extends "base.html" %}

{% block title %}عزل موظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-times"></i>
                        عزل موظف
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- تحذير مهم -->
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم:</h6>
                        <p class="mb-1">• سيتم نقل الموظف من جدول الموظفين النشطين إلى جدول المعزولين</p>
                        <p class="mb-1">• لن يُحسب الموظف في إجمالي عدد الموظفين النشطين</p>
                        <p class="mb-0">• سيتم الاحتفاظ بجميع بياناته في سجل منفصل</p>
                    </div>

                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user"></i> معلومات الموظف:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                                <p class="mb-1"><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الحالة الحالية:</strong> {{ employee.status }}</p>
                                {% if employee.hire_date %}
                                <p class="mb-0"><strong>تاريخ التوظيف:</strong> {{ employee.hire_date }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <form method="POST" onsubmit="return confirmDismissal()">
                        <div class="row">
                            <!-- تاريخ العزل -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dismissal_date">
                                        <i class="fas fa-calendar-alt"></i>
                                        تاريخ العزل <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="dismissal_date" 
                                           name="dismissal_date" required>
                                </div>
                            </div>

                            <!-- رقم المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">
                                        <i class="fas fa-file-alt"></i>
                                        رقم مقرر العزل <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="decision_number" 
                                           name="decision_number" required
                                           placeholder="رقم المقرر الإداري">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">
                                        <i class="fas fa-calendar"></i>
                                        تاريخ المقرر <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="decision_date" 
                                           name="decision_date" required>
                                </div>
                            </div>

                            <!-- سبب العزل -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dismissal_reason">
                                        <i class="fas fa-question-circle"></i>
                                        سبب العزل <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="dismissal_reason" 
                                            name="dismissal_reason" required>
                                        <option value="">اختر السبب...</option>
                                        <option value="مخالفة تأديبية جسيمة">مخالفة تأديبية جسيمة</option>
                                        <option value="إهمال في العمل">إهمال في العمل</option>
                                        <option value="عدم الكفاءة المهنية">عدم الكفاءة المهنية</option>
                                        <option value="مخالفة قانونية">مخالفة قانونية</option>
                                        <option value="سوء السلوك">سوء السلوك</option>
                                        <option value="أسباب أخرى">أسباب أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل إضافية -->
                        <div class="form-group">
                            <label for="details">
                                <i class="fas fa-sticky-note"></i>
                                تفاصيل العزل (اختياري)
                            </label>
                            <textarea class="form-control" id="details" name="details" rows="3"
                                      placeholder="تفاصيل إضافية حول أسباب العزل..."></textarea>
                        </div>

                        <!-- تأكيد العملية -->
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_dismissal" required>
                                <label class="custom-control-label text-danger" for="confirm_dismissal">
                                    <strong>أؤكد صحة المعلومات وأن الموظف سيتم عزله نهائياً</strong>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-user-times"></i>
                                تأكيد العزل
                            </button>
                            <a href="{{ url_for('employees') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDismissal() {
    const employeeName = "{{ employee.first_name }} {{ employee.last_name }}";
    const dismissalDate = document.getElementById('dismissal_date').value;
    const dismissalReason = document.getElementById('dismissal_reason').value;
    const decisionNumber = document.getElementById('decision_number').value;
    
    const confirmMessage = `
هل أنت متأكد من عزل الموظف؟

الموظف: ${employeeName}
تاريخ العزل: ${dismissalDate}
السبب: ${dismissalReason}
رقم المقرر: ${decisionNumber}

تحذير: سيتم نقل الموظف لجدول المعزولين وحذفه من قائمة الموظفين النشطين.
هذا الإجراء لا يمكن التراجع عنه بسهولة.
    `;
    
    return confirm(confirmMessage);
}
</script>
{% endblock %}