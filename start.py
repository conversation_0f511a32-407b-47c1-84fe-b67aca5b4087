#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة موظفي الجمارك الجزائرية
"""

import subprocess
import sys
import os

def main():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام إدارة موظفي الجمارك الجزائرية...")
    print("=" * 60)
    
    try:
        # تشغيل التطبيق النظيف
        subprocess.run([sys.executable, "clean_app.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")

if __name__ == "__main__":
    main()
