#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث نظام الحالات الخاصة حسب المواصفات الجديدة
"""

import sqlite3
import os
from datetime import datetime

def run_database_updates():
    """تشغيل تحديثات قاعدة البيانات"""
    try:
        print("🔧 تشغيل تحديثات قاعدة البيانات...")
        
        # تشغيل إنشاء الجداول المحدثة
        from create_updated_special_status_tables import create_updated_special_status_tables, check_updated_tables
        
        if create_updated_special_status_tables():
            print("✅ تم إنشاء الجداول المحدثة")
            
            if check_updated_tables():
                print("✅ تم التحقق من الجداول بنجاح")
                return True
            else:
                print("❌ فشل في التحقق من الجداول")
                return False
        else:
            print("❌ فشل في إنشاء الجداول")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

def test_updated_system():
    """اختبار النظام المحدث"""
    try:
        print("\n🧪 اختبار النظام المحدث...")
        
        # اختبار استيراد المدير المحدث
        from updated_special_status_manager import UpdatedSpecialStatusManager
        manager = UpdatedSpecialStatusManager()
        
        print("✅ تم إنشاء مدير الحالات المحدث")
        
        # اختبار الحصول على أسباب الاستيداع
        reasons = manager.get_leave_reasons()
        print(f"✅ تم الحصول على {len(reasons)} سبب للاستيداع")
        
        # اختبار الإحصائيات
        stats = manager.get_statistics()
        print(f"✅ تم الحصول على الإحصائيات: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def update_navigation():
    """تحديث روابط التنقل"""
    try:
        print("\n🔗 تحديث روابط التنقل...")
        
        # تحديث الصفحة الرئيسية للحالات الخاصة
        index_content = '''{% extends "base.html" %}

{% block page_title %}الحالات الخاصة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-times me-2"></i>
                        نظام إدارة الحالات الخاصة للموظفين (محدث)
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إحصائيات سريعة -->
                        <div class="col-md-3 mb-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-clock fa-2x mb-2"></i>
                                    <h4>{{ stats.active_leave_of_absence or 0 }}</h4>
                                    <p class="mb-0">الاستيداع النشط</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-slash fa-2x mb-2"></i>
                                    <h4>{{ stats.active_suspensions or 0 }}</h4>
                                    <p class="mb-0">التوقيف النشط</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-minus fa-2x mb-2"></i>
                                    <h4>{{ stats.pending_resignations or 0 }}</h4>
                                    <p class="mb-0">الاستقالات المعلقة</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <h4>{{ (stats.total_deaths + stats.total_retirements + stats.total_dismissals + stats.total_external_transfers) or 0 }}</h4>
                                    <p class="mb-0">الحالات النهائية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة الحالات الخاصة المحدثة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="mb-3">إدارة الحالات الخاصة</h5>
                        </div>
                    </div>

                    <div class="row">
                        <!-- الاستيداع -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-clock me-2"></i>
                                        الاستيداع (محدث)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إدارة حالات الاستيداع مع تتبع الحد الأقصى 5 سنوات</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.leave_of_absence') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-list me-2"></i>عرض الاستيداع
                                        </a>
                                        <a href="{{ url_for('special_status.add_leave_of_absence') }}" class="btn btn-secondary">
                                            <i class="fas fa-plus me-2"></i>إضافة استيداع
                                        </a>
                                        <a href="{{ url_for('special_status.leave_reasons_settings') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-cog me-2"></i>إعدادات الأسباب
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الاستقالات -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-minus me-2"></i>
                                        الاستقالات
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إدارة طلبات الاستقالة مع نظام الموافقة</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.resignations') }}" class="btn btn-outline-danger">
                                            <i class="fas fa-list me-2"></i>عرض الاستقالات
                                        </a>
                                        <a href="{{ url_for('special_status.add_resignation') }}" class="btn btn-danger">
                                            <i class="fas fa-plus me-2"></i>إضافة استقالة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الوفيات -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-dark">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-heart me-2"></i>
                                        الوفيات (حالة نهائية)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تسجيل حالات الوفاة ونقل الموظف من النظام</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.deaths') }}" class="btn btn-outline-dark">
                                            <i class="fas fa-list me-2"></i>سجل الوفيات
                                        </a>
                                        <a href="{{ url_for('special_status.add_death') }}" class="btn btn-dark">
                                            <i class="fas fa-plus me-2"></i>تسجيل وفاة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظة مهمة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>ملاحظة مهمة:</h6>
                                <p class="mb-0">
                                    <strong>الحالات النهائية:</strong> الوفاة، التحويل الخارجي، العزل، والتقاعد تؤدي إلى نقل الموظف من جدول الموظفين النشطين.
                                    <br>
                                    <strong>الحالات المؤقتة:</strong> الاستيداع، التوقيف، والاستقالة المعلقة تبقي الموظف في النظام مع تغيير حالته.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
        
        # كتابة الملف المحدث
        with open('templates/special_status/index_updated.html', 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print("✅ تم تحديث الصفحة الرئيسية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث التنقل: {e}")
        return False

def main():
    """الدالة الرئيسية للتحديث"""
    print("🚀 تحديث نظام الحالات الخاصة حسب المواصفات الجديدة")
    print("=" * 70)
    
    # تحديث قاعدة البيانات
    if not run_database_updates():
        print("❌ فشل في تحديث قاعدة البيانات")
        return
    
    # اختبار النظام المحدث
    if not test_updated_system():
        print("❌ فشل في اختبار النظام المحدث")
        return
    
    # تحديث التنقل
    if not update_navigation():
        print("❌ فشل في تحديث التنقل")
        return
    
    print("\n🎉 تم تحديث نظام الحالات الخاصة بنجاح!")
    print("=" * 70)
    print("🌐 الميزات الجديدة:")
    print("   ✅ نظام إدارة أسباب الاستيداع في الإعدادات")
    print("   ✅ تتبع الحد الأقصى للاستيداع (5 سنوات)")
    print("   ✅ حساب تواريخ النهاية تلقائياً")
    print("   ✅ نظام الموافقة للاستقالات")
    print("   ✅ نقل الموظفين للحالات النهائية")
    print("   ✅ حقول مفصلة لكل نوع من الحالات")
    
    print("\n🔗 الروابط المهمة:")
    print("   - الصفحة الرئيسية: http://localhost:5000/special_status/")
    print("   - إعدادات أسباب الاستيداع: http://localhost:5000/special_status/leave_reasons_settings")
    print("   - إضافة استيداع: http://localhost:5000/special_status/leave_of_absence/add")

if __name__ == '__main__':
    main()
