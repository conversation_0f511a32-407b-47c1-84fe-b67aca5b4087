#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وإنشاء أرقام ضمان اجتماعي صحيحة
"""

def generate_correct_ssn(birth_year, gender):
    """إنشاء رقم ضمان اجتماعي صحيح"""
    # الرقم الأول: الجنس (1 للذكر، 2 للأنثى)
    gender_digit = '1' if gender == 'ذكر' else '2'
    
    # الرقمان 2-3: آخر رقمين من سنة الميلاد
    year_digits = str(birth_year)[-2:]
    
    # الأرقام 4-5: الشهر (افتراضي 05)
    month_digits = '05'
    
    # الأرقام 6-13: رقم تسلسلي (افتراضي)
    serial_digits = '********'
    
    # تكوين أول 13 رقم
    first_13 = gender_digit + year_digits + month_digits + serial_digits
    
    # حساب مفتاح التحقق
    key = 97 - (int(first_13) % 97)
    key_str = f"{key:02d}"
    
    return first_13 + key_str

def generate_correct_postal_account():
    """إنشاء رقم حساب جاري بريدي صحيح"""
    # أول 8 أرقام
    first_8 = '********'
    
    # حساب مفتاح التحقق
    key = 97 - (int(first_8) % 97)
    key_str = f"{key:02d}"
    
    return first_8 + key_str

if __name__ == "__main__":
    print("🔢 إنشاء أرقام صحيحة للاختبار:")
    print("=" * 50)
    
    # إنشاء رقم ضمان اجتماعي صحيح
    ssn_male = generate_correct_ssn(1985, 'ذكر')
    ssn_female = generate_correct_ssn(1990, 'أنثى')
    
    print(f"رقم ضمان اجتماعي لذكر مولود 1985: {ssn_male}")
    print(f"رقم ضمان اجتماعي لأنثى مولودة 1990: {ssn_female}")
    
    # إنشاء رقم حساب جاري بريدي صحيح
    postal = generate_correct_postal_account()
    print(f"رقم حساب جاري بريدي صحيح: {postal}")
    
    # اختبار الأرقام المولدة
    from app import validate_social_security_number, validate_postal_account
    
    print(f"\n🧪 اختبار الأرقام المولدة:")
    print("-" * 30)
    
    result1 = validate_social_security_number(ssn_male, 1985, 'ذكر')
    print(f"✅ رقم الذكر صحيح: {result1}")
    
    result2 = validate_social_security_number(ssn_female, 1990, 'أنثى')
    print(f"✅ رقم الأنثى صحيح: {result2}")
    
    result3 = validate_postal_account(postal)
    print(f"✅ الحساب البريدي صحيح: {result3}")
    
    # اختبار الحقول الفارغة
    print(f"\n🔍 اختبار الحقول الفارغة:")
    print("-" * 30)
    
    empty_ssn = validate_social_security_number('', None, None)
    print(f"✅ رقم ضمان فارغ صحيح: {empty_ssn}")
    
    empty_postal = validate_postal_account('')
    print(f"✅ حساب بريدي فارغ صحيح: {empty_postal}")
    
    none_ssn = validate_social_security_number(None, None, None)
    print(f"✅ رقم ضمان None صحيح: {none_ssn}")
    
    none_postal = validate_postal_account(None)
    print(f"✅ حساب بريدي None صحيح: {none_postal}")