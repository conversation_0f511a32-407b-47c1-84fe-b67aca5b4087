#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح خطأ employee_statuses
Fix employee_statuses error
"""

import os
import re

def find_special_status_references():
    """البحث عن جميع المراجع لـ special_status"""
    print("🔍 البحث عن مراجع special_status...")
    
    # قائمة الملفات للبحث فيها
    files_to_check = []
    
    # البحث في جميع ملفات Python
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                files_to_check.append(os.path.join(root, file))
    
    # البحث في جميع ملفات HTML
    for root, dirs, files in os.walk('templates'):
        for file in files:
            if file.endswith('.html'):
                files_to_check.append(os.path.join(root, file))
    
    found_references = []
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'employee_statuses' in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if 'employee_statuses' in line:
                            found_references.append({
                                'file': file_path,
                                'line': i,
                                'content': line.strip()
                            })
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف {file_path}: {e}")
    
    return found_references

def fix_special_status_references(references):
    """إصلاح مراجع special_status"""
    print("🔧 إصلاح المراجع...")
    
    for ref in references:
        file_path = ref['file']
        print(f"📝 إصلاح الملف: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # استبدال employee_statuses بـ employee_statuses
            content = content.replace('employee_statuses', 'employee_statuses')
            content = content.replace("'employee_statuses'", "'employee_statuses'")
            content = content.replace('"employee_statuses"', '"employee_statuses"')
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إصلاح الملف: {file_path}")
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح الملف {file_path}: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 أداة إصلاح خطأ employee_statuses")
    print("🔧 employee_statuses Error Fix Tool")
    print("=" * 60)
    
    # البحث عن المراجع
    references = find_special_status_references()
    
    if not references:
        print("✅ لم يتم العثور على أي مراجع لـ special_status")
        print("✅ No special_status references found")
    else:
        print(f"🔍 تم العثور على {len(references)} مرجع:")
        for ref in references:
            print(f"   📄 {ref['file']}:{ref['line']} - {ref['content']}")
        
        print("\n🔧 بدء الإصلاح...")
        fix_special_status_references(references)
        print("✅ تم الإصلاح بنجاح!")
    
    print("\n" + "=" * 60)
    print("🎉 انتهت عملية الإصلاح")
    print("🎉 Fix process completed")
    print("=" * 60)

if __name__ == '__main__':
    main()
