@echo off
cls
echo.
echo ==========================================
echo     نظام إدارة موظفي الجمارك الجزائرية
echo              تشخيص وتشغيل
echo ==========================================
echo.

echo 🔍 فحص Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير متوفر
    pause
    exit /b 1
)

echo.
echo 🔍 فحص Flask...
python -c "import flask; print('Flask OK')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Flask غير مثبت - جاري التثبيت...
    python -m pip install flask
)

echo.
echo 🚀 تشغيل النظام...
python simple_main.py

pause
