{% extends "base.html" %}

{% block title %}إضافة عطلة أخرى - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إضافة عطلة أخرى{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <a href="{{ url_for('leaves') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للعطل والإجازات
        </a>
    </div>
</div>

<!-- نموذج إضافة العطلة الأخرى -->
<form method="POST" id="otherLeaveForm">
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-calendar-alt me-2"></i>بيانات العطلة الأخرى</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الموظف <span class="required">*</span></label>
                        <select name="employee_id" class="form-select" required>
                            <option value="">اختر الموظف</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}">
                                {{ employee.registration_number }} - {{ employee.first_name }} {{ employee.last_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">نوع العطلة <span class="required">*</span></label>
                        <select name="leave_type" class="form-select" required onchange="toggleSalaryDeduction()">
                            <option value="">اختر النوع</option>
                            <option value="عطلة استثنائية">عطلة استثنائية</option>
                            <option value="عطلة بدون راتب">عطلة بدون راتب</option>
                            <option value="عطلة دراسية">عطلة دراسية</option>
                            <option value="عطلة حج">عطلة حج</option>
                            <option value="عطلة عمرة">عطلة عمرة</option>
                            <option value="عطلة زواج">عطلة زواج</option>
                            <option value="عطلة وفاة">عطلة وفاة</option>
                            <option value="عطلة ولادة (للأب)">عطلة ولادة (للأب)</option>
                            <option value="عطلة ختان">عطلة ختان</option>
                            <option value="عطلة طارئة">عطلة طارئة</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ بداية العطلة <span class="required">*</span></label>
                        <input type="date" name="start_date" class="form-control" required id="start_date" onchange="calculateEndDate()">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">عدد أيام العطلة <span class="required">*</span></label>
                        <input type="number" name="days_count" class="form-control" required min="1" max="30" id="days_count" onchange="calculateEndDate()">
                        <div class="form-text">الحد الأقصى 30 يوم في السنة</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ نهاية العطلة</label>
                        <input type="date" name="end_date" class="form-control" readonly id="end_date">
                        <div class="form-text">محسوب تلقائياً</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">سبب العطلة <span class="required">*</span></label>
                        <textarea name="reason" class="form-control" rows="3" required placeholder="اذكر سبب طلب العطلة بالتفصيل"></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الوجهة</label>
                        <input type="text" name="destination" class="form-control" placeholder="الوجهة أو المكان المقصود">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم مقرر العطلة</label>
                        <input type="text" name="decision_number" class="form-control" placeholder="رقم المقرر">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ المقرر</label>
                        <input type="date" name="decision_date" class="form-control">
                    </div>
                </div>
            </div>
            
            <!-- قسم خصم الراتب -->
            <div class="card bg-light mb-3" id="salary_deduction_section">
                <div class="card-header">
                    <h6><i class="fas fa-money-bill-wave me-2"></i>خصم الراتب</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">خصم من الراتب</label>
                                <select name="salary_deduction" class="form-select" id="salary_deduction" onchange="toggleDeductionDetails()">
                                    <option value="0">لا</option>
                                    <option value="1">نعم</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4" id="deduction_number_group" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">رقم الخصم</label>
                                <input type="text" name="deduction_number" class="form-control" placeholder="رقم مقرر الخصم">
                            </div>
                        </div>
                        <div class="col-md-4" id="deduction_date_group" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الخصم</label>
                                <input type="date" name="deduction_date" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="deduction_amount_group" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مبلغ الخصم (دج)</label>
                                <input type="number" name="deduction_amount" class="form-control" min="0" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نسبة الخصم (%)</label>
                                <input type="number" name="deduction_percentage" class="form-control" min="0" max="100" step="0.1" placeholder="0.0">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="2" placeholder="ملاحظات إضافية"></textarea>
            </div>
        </div>
    </div>
    
    <!-- أزرار الحفظ -->
    <div class="card mt-4">
        <div class="card-body text-center">
            <button type="submit" class="btn btn-info btn-lg me-3">
                <i class="fas fa-save me-2"></i>حفظ العطلة
            </button>
            <a href="{{ url_for('leaves') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </div>
</form>
{% endblock %}

{% block scripts %}
<script>
// حساب تاريخ النهاية
function calculateEndDate() {
    const startDate = document.getElementById('start_date').value;
    const daysCount = parseInt(document.getElementById('days_count').value) || 0;
    const endDateInput = document.getElementById('end_date');
    
    if (startDate && daysCount > 0) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setDate(start.getDate() + daysCount - 1);
        
        const endDateStr = end.toISOString().split('T')[0];
        endDateInput.value = endDateStr;
    } else {
        endDateInput.value = '';
    }
}

// إظهار/إخفاء قسم خصم الراتب حسب نوع العطلة
function toggleSalaryDeduction() {
    const leaveType = document.querySelector('select[name="leave_type"]').value;
    const salaryDeductionSection = document.getElementById('salary_deduction_section');
    const salaryDeductionSelect = document.getElementById('salary_deduction');
    
    if (leaveType === 'عطلة بدون راتب') {
        salaryDeductionSection.style.display = 'block';
        salaryDeductionSelect.value = '1';
        toggleDeductionDetails();
    } else if (leaveType === 'عطلة استثنائية' || leaveType === 'عطلة طارئة') {
        salaryDeductionSection.style.display = 'block';
        salaryDeductionSelect.value = '0';
        toggleDeductionDetails();
    } else {
        salaryDeductionSection.style.display = 'none';
        salaryDeductionSelect.value = '0';
        toggleDeductionDetails();
    }
}

// إظهار/إخفاء تفاصيل الخصم
function toggleDeductionDetails() {
    const salaryDeduction = document.getElementById('salary_deduction').value;
    const deductionNumberGroup = document.getElementById('deduction_number_group');
    const deductionDateGroup = document.getElementById('deduction_date_group');
    const deductionAmountGroup = document.getElementById('deduction_amount_group');
    
    if (salaryDeduction === '1') {
        deductionNumberGroup.style.display = 'block';
        deductionDateGroup.style.display = 'block';
        deductionAmountGroup.style.display = 'block';
    } else {
        deductionNumberGroup.style.display = 'none';
        deductionDateGroup.style.display = 'none';
        deductionAmountGroup.style.display = 'none';
        
        // مسح القيم
        document.querySelector('input[name="deduction_number"]').value = '';
        document.querySelector('input[name="deduction_date"]').value = '';
        document.querySelector('input[name="deduction_amount"]').value = '';
        document.querySelector('input[name="deduction_percentage"]').value = '';
    }
}

// التحقق من صحة النموذج
document.getElementById('otherLeaveForm').addEventListener('submit', function(e) {
    const employeeId = document.querySelector('select[name="employee_id"]').value;
    const leaveType = document.querySelector('select[name="leave_type"]').value;
    const startDate = document.getElementById('start_date').value;
    const daysCount = parseInt(document.getElementById('days_count').value) || 0;
    const reason = document.querySelector('textarea[name="reason"]').value.trim();
    
    if (!employeeId || !leaveType || !startDate || daysCount <= 0 || !reason) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    if (daysCount > 30) {
        e.preventDefault();
        alert('عدد أيام العطلة الاستثنائية لا يمكن أن يتجاوز 30 يوم في السنة');
        return false;
    }
});

// تحديد تاريخ اليوم كحد أدنى
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').min = today;
    
    // إخفاء قسم خصم الراتب في البداية
    toggleSalaryDeduction();
});
</script>
{% endblock %}
