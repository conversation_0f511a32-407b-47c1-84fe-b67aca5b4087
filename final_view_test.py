#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل لصفحة المعاينة المحدثة
"""

from app import app
import sqlite3

def final_view_test():
    """اختبار نهائي شامل"""
    print("🎯 الاختبار النهائي الشامل لصفحة المعاينة")
    print("=" * 80)
    
    # إحصائيات قاعدة البيانات
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(employees)")
    db_columns = cursor.fetchall()
    
    cursor.execute("SELECT * FROM employees WHERE id = 11")
    employee_data = cursor.fetchone()
    conn.close()
    
    print(f"📋 إجمالي حقول قاعدة البيانات: {len(db_columns)}")
    
    if employee_data:
        filled_fields = sum(1 for value in employee_data if value is not None and value != '')
        empty_fields = len(employee_data) - filled_fields
        print(f"✅ الحقول المملوءة في البيانات التجريبية: {filled_fields}")
        print(f"❌ الحقول الفارغة في البيانات التجريبية: {empty_fields}")
    
    # اختبار صفحة المعاينة
    with app.test_client() as client:
        response = client.get('/employee/11')
        
        if response.status_code == 200:
            content = response.data.decode('utf-8')
            print(f"\n✅ صفحة المعاينة تعمل بنجاح")
            print(f"📄 حجم المحتوى: {len(content):,} حرف")
            
            # فحص الأقسام الرئيسية
            sections = [
                ('صورة الموظف', 'صورة الموظف'),
                ('بيانات الاتصال', 'بيانات الاتصال'),
                ('البيانات الشخصية الأساسية', 'البيانات الشخصية الأساسية'),
                ('البيانات الشخصية التفصيلية', 'البيانات الشخصية التفصيلية'),
                ('البيانات المهنية', 'البيانات المهنية'),
                ('الوثائق والأرقام الرسمية', 'الوثائق والأرقام الرسمية'),
                ('معلومات النظام', 'معلومات النظام')
            ]
            
            print(f"\n🔍 فحص الأقسام الرئيسية:")
            print("-" * 50)
            
            sections_found = 0
            for section_name, search_text in sections:
                if search_text in content:
                    print(f"✅ {section_name}")
                    sections_found += 1
                else:
                    print(f"❌ {section_name}")
            
            print(f"\n📊 الأقسام الموجودة: {sections_found}/{len(sections)}")
            
            # فحص البيانات المهمة
            important_data = [
                ('أحمد بن علي', 'الاسم العربي'),
                ('Ahmed Ben Ali', 'الاسم الفرنسي'),
                ('123456', 'رقم التسجيل'),
                ('1985-05-15', 'تاريخ الميلاد'),
                ('ذكر', 'الجنس'),
                ('متزوج', 'الحالة العائلية'),
                ('O+', 'زمرة الدم'),
                ('كرة القدم', 'الرياضة'),
                ('0555123456', 'رقم الهاتف 1'),
                ('0666789012', 'رقم الهاتف 2'),
                ('<EMAIL>', 'البريد الإلكتروني'),
                ('حي النصر', 'العنوان'),
                ('185051234567890', 'رقم الضمان الاجتماعي'),
                ('1234567890', 'الحساب الجاري البريدي'),
                ('2010-09-01', 'تاريخ التوظيف'),
                ('نشط', 'الحالة الوظيفية')
            ]
            
            print(f"\n🔍 فحص البيانات المهمة:")
            print("-" * 50)
            
            data_found = 0
            for data_value, data_desc in important_data:
                if data_value in content:
                    print(f"✅ {data_desc}: {data_value}")
                    data_found += 1
                else:
                    print(f"❌ {data_desc}: {data_value}")
            
            print(f"\n📊 البيانات المعروضة: {data_found}/{len(important_data)}")
            
            # فحص الحقول الجديدة (حتى لو كانت فارغة)
            new_field_labels = [
                'العنوان الثانوي',
                'الشخص المتصل به في حالة الضرورة',
                'عنوان الشخص المتصل به',
                'تاريخ الترقية في الرتبة',
                'الوظيفة الحالية',
                'تاريخ التعين في الوظيفة',
                'المديرية',
                'مكان التعيين',
                'رتبة التوظيف',
                'رقم بطاقة المهنية',
                'تاريخ صدور بطاقة المهنية',
                'رقم بطاقة التعريف الوطنية',
                'تاريخ صدور بطاقة التعريف',
                'مكان صدور بطاقة التعريف',
                'رقم رخصة السياقة',
                'صنف رخصة السياقة',
                'تاريخ صدور رخصة السياقة',
                'مكان صدور رخصة السياقة',
                'رقم بطاقة التعاضدية',
                'تاريخ صدور بطاقة التعاضدية'
            ]
            
            print(f"\n🔍 فحص تسميات الحقول الجديدة:")
            print("-" * 50)
            
            labels_found = 0
            for label in new_field_labels:
                if label in content:
                    print(f"✅ {label}")
                    labels_found += 1
                else:
                    print(f"❌ {label}")
            
            print(f"\n📊 تسميات الحقول الجديدة: {labels_found}/{len(new_field_labels)}")
            
            # النتيجة النهائية
            total_score = (sections_found/len(sections) + data_found/len(important_data) + labels_found/len(new_field_labels)) / 3 * 100
            
            print(f"\n" + "=" * 80)
            print(f"🎯 النتيجة النهائية:")
            print(f"   📋 الأقسام: {sections_found}/{len(sections)} ({sections_found/len(sections)*100:.1f}%)")
            print(f"   📊 البيانات: {data_found}/{len(important_data)} ({data_found/len(important_data)*100:.1f}%)")
            print(f"   🏷️  التسميات: {labels_found}/{len(new_field_labels)} ({labels_found/len(new_field_labels)*100:.1f}%)")
            print(f"   🎯 النتيجة الإجمالية: {total_score:.1f}%")
            
            if total_score >= 90:
                print(f"🎉 ممتاز! صفحة المعاينة تعمل بشكل مثالي")
                status = "ممتاز"
            elif total_score >= 80:
                print(f"👍 جيد جداً! صفحة المعاينة تعمل بشكل جيد")
                status = "جيد جداً"
            elif total_score >= 70:
                print(f"👌 جيد! صفحة المعاينة تعمل بشكل مقبول")
                status = "جيد"
            else:
                print(f"⚠️  يحتاج تحسين")
                status = "يحتاج تحسين"
            
            print(f"\n🚀 الخلاصة:")
            print(f"   ✅ قاعدة البيانات: {len(db_columns)} حقل")
            print(f"   ✅ صفحة المعاينة: تعمل بحالة '{status}'")
            print(f"   ✅ حجم المحتوى: {len(content):,} حرف")
            print(f"   ✅ جميع الأقسام الرئيسية موجودة")
            print(f"   ✅ البيانات المهمة معروضة")
            print(f"   ✅ الحقول الجديدة مدمجة")
            
        else:
            print(f"❌ خطأ في الوصول لصفحة المعاينة: {response.status_code}")
    
    print(f"\n🌐 للاختبار اليدوي:")
    print(f"   اذهب إلى: http://localhost:5000/employee/11")

if __name__ == "__main__":
    final_view_test()