#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مقارنة الحقول الموجودة في قاعدة البيانات مع الحقول المعروضة في صفحة المعاينة
"""

import sqlite3
from app import app

def compare_database_vs_view():
    """مقارنة حقول قاعدة البيانات مع صفحة المعاينة"""
    print("🔍 مقارنة حقول قاعدة البيانات مع صفحة المعاينة...")
    print("=" * 80)
    
    # الحصول على حقول قاعدة البيانات
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(employees)")
    db_columns = cursor.fetchall()
    
    print("📋 الحقول الموجودة في قاعدة البيانات:")
    print("-" * 50)
    db_fields = []
    for i, col in enumerate(db_columns, 1):
        col_name = col[1]
        col_type = col[2]
        db_fields.append(col_name)
        print(f"{i:2d}. {col_name:<25} ({col_type})")
    
    print(f"\n📊 إجمالي حقول قاعدة البيانات: {len(db_fields)}")
    
    # الحصول على بيانات الموظف التجريبي
    cursor.execute("SELECT * FROM employees WHERE id = 11")
    employee = cursor.fetchone()
    conn.close()
    
    if not employee:
        print("❌ لم يتم العثور على الموظف التجريبي")
        return
    
    # اختبار صفحة المعاينة
    with app.test_client() as client:
        response = client.get('/employee/11')
        
        if response.status_code != 200:
            print(f"❌ خطأ في الوصول لصفحة المعاينة: {response.status_code}")
            return
        
        content = response.data.decode('utf-8')
        
        print(f"\n🔍 فحص عرض الحقول في صفحة المعاينة:")
        print("-" * 80)
        
        # تعريف الحقول مع قيمها المتوقعة
        field_checks = {
            'id': ('معرف الموظف', '11'),
            'registration_number': ('رقم التسجيل', '123456'),
            'first_name': ('الاسم (عربي)', 'أحمد'),
            'last_name': ('اللقب (عربي)', 'بن علي'),
            'first_name_fr': ('الاسم (فرنسي)', 'Ahmed'),
            'last_name_fr': ('اللقب (فرنسي)', 'Ben Ali'),
            'birth_date': ('تاريخ الميلاد', '1985-05-15'),
            'birth_wilaya_id': ('معرف ولاية الميلاد', '1'),
            'birth_commune_id': ('معرف بلدية الميلاد', '1'),
            'gender': ('الجنس', 'ذكر'),
            'social_security_number': ('رقم الضمان الاجتماعي', '***************'),
            'hire_date': ('تاريخ التوظيف', '2010-09-01'),
            'current_rank_id': ('معرف الرتبة', '1'),
            'corps_id': ('معرف السلك', '1'),
            'current_service_id': ('معرف المصلحة', '1'),
            'postal_account': ('الحساب الجاري البريدي', '**********'),
            'phone': ('رقم الهاتف القديم', None),  # هذا الحقل فارغ
            'email': ('البريد الإلكتروني', '<EMAIL>'),
            'address': ('العنوان', 'حي النصر'),
            'photo': ('الصورة', None),  # هذا الحقل فارغ
            'status': ('الحالة الوظيفية', 'نشط'),
            'created_at': ('تاريخ الإنشاء', '2025-07-26'),
            'updated_at': ('آخر تحديث', '2025-07-26'),
            'marital_status': ('الحالة العائلية', 'متزوج'),
            'children_count': ('عدد الأبناء', '2'),
            'dependents_count': ('عدد المتكفل بهم', '1'),
            'blood_type': ('زمرة الدم', 'O+'),
            'sport_practiced': ('الرياضة الممارسة', 'كرة القدم'),
            'phone1': ('رقم الهاتف 1', '**********'),
            'phone2': ('رقم الهاتف 2', '0666789012')
        }
        
        displayed_fields = 0
        not_displayed_fields = []
        
        for field_name in db_fields:
            if field_name in field_checks:
                field_desc, expected_value = field_checks[field_name]
                
                # البحث عن الحقل في المحتوى
                field_found = False
                
                # البحث عن وصف الحقل
                if field_desc in content:
                    field_found = True
                
                # البحث عن القيمة إذا كانت موجودة
                if expected_value and expected_value in content:
                    field_found = True
                
                # حالات خاصة للحقول الفارغة
                if field_name == 'phone' and expected_value is None:
                    # هذا الحقل فارغ، لا يجب أن يظهر
                    if 'رقم الهاتف (قديم)' not in content:
                        field_found = True  # صحيح أنه لا يظهر
                
                if field_name == 'photo' and expected_value is None:
                    # البحث عن "حالة الصورة" أو "لا توجد صورة"
                    if 'حالة الصورة' in content or 'لا توجد صورة' in content:
                        field_found = True
                
                if field_found:
                    displayed_fields += 1
                    status = "✅ معروض"
                else:
                    not_displayed_fields.append((field_name, field_desc))
                    status = "❌ غير معروض"
                
                print(f"{field_name:<25} | {field_desc:<35} | {status}")
            else:
                print(f"{field_name:<25} | {'غير معرف في الاختبار':<35} | ⚠️  غير مختبر")
        
        print("-" * 80)
        print(f"📊 ملخص النتائج:")
        print(f"   📋 إجمالي حقول قاعدة البيانات: {len(db_fields)}")
        print(f"   ✅ الحقول المعروضة: {displayed_fields}")
        print(f"   ❌ الحقول غير المعروضة: {len(not_displayed_fields)}")
        
        if not_displayed_fields:
            print(f"\n⚠️  الحقول غير المعروضة:")
            for field_name, field_desc in not_displayed_fields:
                print(f"      - {field_desc} ({field_name})")
        
        # حساب النسبة المئوية
        percentage = (displayed_fields / len(db_fields)) * 100
        print(f"\n📈 نسبة العرض: {percentage:.1f}%")
        
        if percentage >= 95:
            print("🎉 ممتاز! تقريباً جميع الحقول معروضة")
        elif percentage >= 85:
            print("👍 جيد جداً! معظم الحقول معروضة")
        elif percentage >= 70:
            print("👌 جيد! أغلب الحقول معروضة")
        else:
            print("⚠️  يحتاج تحسين - العديد من الحقول غير معروضة")
        
        print(f"\n🌐 للمراجعة اليدوية:")
        print(f"   اذهب إلى: http://localhost:5000/employee/11")

if __name__ == "__main__":
    compare_database_vs_view()