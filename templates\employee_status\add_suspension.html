{% extends "base.html" %}

{% block title %}إضافة توقيف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ban"></i>
                        تسجيل توقيف
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user"></i> معلومات الموظف</h5>
                        <strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}<br>
                        <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                        <strong>الحالة الحالية:</strong> <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> التوقيف سيغير حالة الموظف إلى "موقوف" ويمكن إنهاؤه لاحقاً.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="suspension_date">تاريخ التوقيف <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="suspension_date" name="suspension_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ التوقيف
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="suspension_type">نوع التوقيف <span class="text-danger">*</span></label>
                                    <select class="form-control" id="suspension_type" name="suspension_type" required onchange="handleSuspensionTypeChange()">
                                        <option value="">اختر نوع التوقيف</option>
                                        <option value="توقيف مؤقت">توقيف مؤقت</option>
                                        <option value="توقيف احتياطي">توقيف احتياطي</option>
                                        <option value="توقيف تأديبي">توقيف تأديبي</option>
                                        <option value="توقيف إداري">توقيف إداري</option>
                                        <option value="توقيف لأسباب صحية">توقيف لأسباب صحية</option>
                                        <option value="توقيف قضائي">توقيف قضائي</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار نوع التوقيف
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="duration_days">مدة التوقيف (بالأيام)</label>
                                    <input type="number" class="form-control" id="duration_days" name="duration_days" 
                                           min="1" max="365" placeholder="اتركه فارغاً للتوقيف غير محدد المدة">
                                    <small class="form-text text-muted">اختياري - للتوقيف محدد المدة فقط</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">تاريخ القرار</label>
                                    <input type="date" class="form-control" id="decision_date" name="decision_date">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">رقم القرار</label>
                                    <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                           placeholder="مثال: 2024/SUS/001">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="reason">سبب التوقيف <span class="text-danger">*</span></label>
                            <select class="form-control" id="reason_select" onchange="handleReasonChange()">
                                <option value="">اختر سبب التوقيف</option>
                                <option value="مخالفة تأديبية">مخالفة تأديبية</option>
                                <option value="تحقيق إداري">تحقيق إداري</option>
                                <option value="قضية قضائية">قضية قضائية</option>
                                <option value="أسباب صحية">أسباب صحية</option>
                                <option value="عدم الانضباط">عدم الانضباط</option>
                                <option value="إهمال في العمل">إهمال في العمل</option>
                                <option value="سوء السلوك">سوء السلوك</option>
                                <option value="مخالفة مالية">مخالفة مالية</option>
                                <option value="أخرى">أخرى (حدد)</option>
                            </select>
                            <textarea class="form-control mt-2" id="reason" name="reason" rows="3" required 
                                      placeholder="اذكر سبب التوقيف بالتفصيل"></textarea>
                            <div class="invalid-feedback">
                                يرجى إدخال سبب التوقيف
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                      placeholder="أي ملاحظات إضافية حول التوقيف (اختياري)"></textarea>
                        </div>

                        <!-- معلومات محسوبة -->
                        <div class="card bg-light mb-3" id="duration_info" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">معلومات المدة</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>تاريخ الانتهاء المتوقع:</strong>
                                        <span id="expected_end_date">-</span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>نوع التوقيف:</strong>
                                        <span id="duration_type">غير محدد المدة</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm" required>
                                <label class="custom-control-label" for="confirm">
                                    أؤكد صحة البيانات المدخلة وضرورة توقيف الموظف
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد صحة البيانات
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-ban"></i> تسجيل التوقيف
                            </button>
                            <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// معالجة تغيير نوع التوقيف
function handleSuspensionTypeChange() {
    const suspensionType = document.getElementById('suspension_type').value;
    const durationInput = document.getElementById('duration_days');
    
    // اقتراح مدة حسب نوع التوقيف
    if (suspensionType === 'توقيف تأديبي') {
        durationInput.placeholder = 'عادة 1-30 يوم';
    } else if (suspensionType === 'توقيف احتياطي') {
        durationInput.placeholder = 'عادة 15-60 يوم';
    } else if (suspensionType === 'توقيف قضائي') {
        durationInput.placeholder = 'حسب القرار القضائي';
    } else {
        durationInput.placeholder = 'اتركه فارغاً للتوقيف غير محدد المدة';
    }
}

// معالجة تغيير سبب التوقيف
function handleReasonChange() {
    var select = document.getElementById('reason_select');
    var textarea = document.getElementById('reason');
    
    if (select.value && select.value !== 'أخرى') {
        textarea.value = select.value;
    } else if (select.value === 'أخرى') {
        textarea.value = '';
        textarea.focus();
    }
}

// حساب تاريخ الانتهاء المتوقع
function calculateEndDate() {
    const suspensionDate = document.getElementById('suspension_date').value;
    const duration = document.getElementById('duration_days').value;
    
    if (suspensionDate && duration) {
        const startDate = new Date(suspensionDate);
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + parseInt(duration));
        
        document.getElementById('expected_end_date').textContent = endDate.toLocaleDateString('ar-DZ');
        document.getElementById('duration_type').textContent = 'محدد المدة';
        document.getElementById('duration_info').style.display = 'block';
    } else if (suspensionDate && !duration) {
        document.getElementById('expected_end_date').textContent = 'غير محدد';
        document.getElementById('duration_type').textContent = 'غير محدد المدة';
        document.getElementById('duration_info').style.display = 'block';
    } else {
        document.getElementById('duration_info').style.display = 'none';
    }
}

// ربط الأحداث
document.getElementById('suspension_date').addEventListener('change', calculateEndDate);
document.getElementById('duration_days').addEventListener('input', calculateEndDate);

// التحقق من المدة
document.getElementById('duration_days').addEventListener('change', function() {
    const duration = parseInt(this.value);
    
    if (duration && duration > 365) {
        alert('مدة التوقيف تبدو طويلة جداً. تأكد من صحة الرقم.');
    }
});

// التحقق من التواريخ
document.getElementById('decision_date').addEventListener('change', function() {
    const decisionDate = new Date(this.value);
    const suspensionDate = document.getElementById('suspension_date').value;
    
    if (suspensionDate && decisionDate > new Date(suspensionDate)) {
        alert('تاريخ القرار لا يمكن أن يكون بعد تاريخ التوقيف');
        this.value = '';
    }
});

// تعيين تاريخ اليوم كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('suspension_date').value = today;
    document.getElementById('decision_date').value = today;
});
</script>
{% endblock %}