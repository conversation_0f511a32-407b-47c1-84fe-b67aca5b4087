{% extends "base.html" %}

{% block page_title %}إعدادات أسباب الاستيداع{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات أسباب الاستيداع
                        </h3>
                        <div>
                            <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addReasonModal">
                                <i class="fas fa-plus me-2"></i>إضافة سبب جديد
                            </button>
                            <a href="{{ url_for('special_status.index') }}" class="btn btn-outline-light">
                                <i class="fas fa-arrow-right me-2"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> الحد الأقصى للاستيداع هو 5 سنوات (60 شهر) في الحياة الوظيفية للموظف.
                    </div>

                    {% if reasons %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-secondary">
                                <tr>
                                    <th>#</th>
                                    <th>سبب الاستيداع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reason in reasons %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ reason.reason }}</td>
                                    <td>
                                        {% if reason.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ reason.created_at[:10] if reason.created_at else 'غير محدد' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="editReason({{ reason.id }}, '{{ reason.reason }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if reason.is_active %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deactivateReason({{ reason.id }})">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            {% else %}
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="activateReason({{ reason.id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أسباب استيداع مضافة</h5>
                        <p class="text-muted">يمكنك إضافة أسباب الاستيداع من خلال الزر أعلاه</p>
                        <button type="button" class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#addReasonModal">
                            <i class="fas fa-plus me-2"></i>إضافة سبب جديد
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة سبب جديد -->
<div class="modal fade" id="addReasonModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title">إضافة سبب استيداع جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('special_status.add_leave_reason') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_reason" class="form-label">سبب الاستيداع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="new_reason" name="reason" required 
                               placeholder="اكتب سبب الاستيداع...">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل السبب -->
<div class="modal fade" id="editReasonModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">تعديل سبب الاستيداع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('special_status.update_leave_reason') }}">
                <div class="modal-body">
                    <input type="hidden" id="edit_reason_id" name="reason_id">
                    <div class="mb-3">
                        <label for="edit_reason_text" class="form-label">سبب الاستيداع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_reason_text" name="reason" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>تحديث
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editReason(id, reason) {
    document.getElementById('edit_reason_id').value = id;
    document.getElementById('edit_reason_text').value = reason;
    new bootstrap.Modal(document.getElementById('editReasonModal')).show();
}

function deactivateReason(id) {
    if (confirm('هل أنت متأكد من إلغاء تفعيل هذا السبب؟ لن يظهر في قوائم الاختيار الجديدة.')) {
        fetch(`/special_status/api/leave_reason/${id}/deactivate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إلغاء تفعيل السبب بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            alert('خطأ في العملية: ' + error);
        });
    }
}

function activateReason(id) {
    if (confirm('هل أنت متأكد من تفعيل هذا السبب؟')) {
        fetch(`/special_status/api/leave_reason/${id}/activate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تفعيل السبب بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            alert('خطأ في العملية: ' + error);
        });
    }
}
</script>

<style>
.table th {
    background-color: #6c757d !important;
    color: white !important;
}

.btn-group .btn {
    margin: 0 1px;
}

.modal-header.bg-secondary {
    border-bottom: 1px solid #5a6268;
}

.modal-header.bg-warning {
    border-bottom: 1px solid #ffc107;
}
</style>
{% endblock %}
