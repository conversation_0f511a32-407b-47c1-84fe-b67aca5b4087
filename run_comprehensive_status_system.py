#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الشامل لإدارة حالات الموظفين
Run Comprehensive Employee Status System
"""

from comprehensive_employee_status_system import create_comprehensive_status_system
from employee_status_routes import register_employee_status_routes
from app import app
import sqlite3

def setup_comprehensive_system():
    """إعداد النظام الشامل"""
    print("🚀 إعداد النظام الشامل لإدارة حالات الموظفين")
    print("=" * 70)
    
    # إنشاء النظام
    print("1️⃣  إنشاء جداول النظام...")
    system = create_comprehensive_status_system()
    
    # تسجيل المسارات
    print("2️⃣  تسجيل مسارات النظام...")
    register_employee_status_routes(app)
    
    # إضافة قائمة جديدة للتنقل
    print("3️⃣  تحديث قائمة التنقل...")
    add_navigation_menu()
    
    # عرض الإحصائيات
    print("4️⃣  عرض الإحصائيات الحالية...")
    stats = system.get_employee_statistics()
    display_statistics(stats)
    
    print("✅ تم إعداد النظام الشامل بنجاح!")
    return system

def add_navigation_menu():
    """إضافة قائمة التنقل للنظام الجديد"""
    # هذه الدالة ستضيف عناصر القائمة للتطبيق الرئيسي
    # يمكن تنفيذها لاحقاً في ملف base.html
    pass

def display_statistics(stats):
    """عرض الإحصائيات"""
    print(f"\n📊 إحصائيات النظام:")
    print(f"   {'='*50}")
    print(f"   🟢 الموظفين النشطين: {stats.get('active', 0)}")
    print(f"   🟡 المستودعين: {stats.get('leave_of_absence', 0)}")
    print(f"   🔴 الموقوفين: {stats.get('suspension', 0)}")
    print(f"   🔵 المستقيلين: {stats.get('resignation', 0)}")
    print(f"   🟣 في الخدمة الوطنية: {stats.get('national_service', 0)}")
    print(f"   🟠 في عطلة طويلة: {stats.get('long_term_leave', 0)}")
    print(f"   🟤 المنتدبين: {stats.get('assignment', 0)}")
    print(f"   ⚫ في دراسة/تكوين: {stats.get('study_training', 0)}")
    print(f"   {'='*50}")
    print(f"   📈 إجمالي النشطين: {stats.get('total_active', 0)}")
    print(f"   {'='*50}")
    print(f"   ⚰️  المتوفين: {stats.get('deceased', 0)}")
    print(f"   🔄 المحولين خارجياً: {stats.get('external_transfer', 0)}")
    print(f"   ❌ المعزولين: {stats.get('dismissed', 0)}")
    print(f"   🏖️  المتقاعدين: {stats.get('retired', 0)}")
    print(f"   {'='*50}")
    print(f"   📉 إجمالي المحذوفين: {stats.get('total_removed', 0)}")
    print(f"   📊 الإجمالي العام: {stats.get('grand_total', 0)}")

def test_system_functionality():
    """اختبار وظائف النظام"""
    print(f"\n🧪 اختبار وظائف النظام:")
    print("-" * 40)
    
    system = create_comprehensive_status_system()
    
    # اختبار الاتصال بقاعدة البيانات
    try:
        conn = system.get_db_connection()
        
        # فحص الجداول المنشأة
        tables = conn.execute('''
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE '%employee%'
            ORDER BY name
        ''').fetchall()
        
        print("✅ الجداول المنشأة:")
        for table in tables:
            count = conn.execute(f'SELECT COUNT(*) FROM {table[0]}').fetchone()[0]
            print(f"   - {table[0]}: {count} سجل")
        
        # فحص أسباب الاستيداع
        reasons = conn.execute('''
            SELECT COUNT(*) FROM leave_of_absence_reasons WHERE is_active = 1
        ''').fetchone()[0]
        print(f"✅ أسباب الاستيداع المتاحة: {reasons}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def create_sample_data():
    """إنشاء بيانات تجريبية للاختبار"""
    print(f"\n🎯 إنشاء بيانات تجريبية:")
    print("-" * 30)
    
    # يمكن إضافة بيانات تجريبية هنا لاحقاً
    print("💡 يمكن إضافة بيانات تجريبية لاحقاً")

def main():
    """الدالة الرئيسية"""
    print("🌟 النظام الشامل لإدارة حالات الموظفين - الجمارك الجزائرية")
    print("=" * 80)
    
    try:
        # إعداد النظام
        system = setup_comprehensive_system()
        
        # اختبار النظام
        test_system_functionality()
        
        # إنشاء بيانات تجريبية (اختياري)
        # create_sample_data()
        
        print(f"\n🎉 النظام جاهز للاستخدام!")
        print(f"🌐 للوصول للنظام:")
        print(f"   - الصفحة الرئيسية: http://localhost:5000/")
        print(f"   - لوحة حالات الموظفين: http://localhost:5000/employee_status/")
        print(f"   - إدارة أسباب الاستيداع: http://localhost:5000/employee_status/settings/leave_reasons")
        
        print(f"\n📋 الميزات المتاحة:")
        print(f"   ✅ إدارة الاستيداع (مع حساب الرصيد)")
        print(f"   ✅ إدارة التوقيف")
        print(f"   ✅ إدارة الوفيات (نقل لجدول منفصل)")
        print(f"   ✅ إدارة التحويل الخارجي (نقل لجدول منفصل)")
        print(f"   ✅ إدارة العزل (نقل لجدول منفصل)")
        print(f"   ✅ إدارة التقاعد (نقل لجدول منفصل)")
        print(f"   ✅ تتبع تاريخ تغييرات الحالات")
        print(f"   ✅ إحصائيات شاملة")
        print(f"   ✅ API للبيانات")
        
        return system
        
    except Exception as e:
        print(f"❌ خطأ في إعداد النظام: {e}")
        return None

if __name__ == "__main__":
    system = main()
    
    if system:
        print(f"\n🚀 لتشغيل الخادم:")
        print(f"   python app.py")
        print(f"\n📚 للمساعدة:")
        print(f"   راجع الملفات المنشأة في المجلد")
    else:
        print(f"\n❌ فشل في إعداد النظام")