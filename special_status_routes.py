#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات الحالات الخاصة للموظفين
Special Employee Status Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from special_status_manager import SpecialStatusManager
from datetime import datetime, date
import json

# إنشاء Blueprint
special_status_bp = Blueprint('special_status', __name__, url_prefix='/special_status')

# إنشاء نسخة من المدير
status_manager = SpecialStatusManager()

@special_status_bp.route('/')
def index():
    """الصفحة الرئيسية للحالات الخاصة"""
    stats = status_manager.get_statistics()
    return render_template('special_status/index.html', stats=stats)

@special_status_bp.route('/dashboard')
def dashboard():
    """لوحة تحكم الحالات الخاصة"""
    stats = status_manager.get_statistics()
    return render_template('special_status/dashboard.html', stats=stats)

# ================================
# مسارات الاستقالات
# ================================

@special_status_bp.route('/resignations')
def resignations():
    """قائمة الاستقالات"""
    resignations = status_manager.get_resignations()
    return render_template('special_status/resignations.html', resignations=resignations)

@special_status_bp.route('/resignations/add', methods=['GET', 'POST'])
def add_resignation():
    """إضافة استقالة جديدة"""
    if request.method == 'POST':
        data = {
            'employee_id': request.form.get('employee_id'),
            'resignation_date': request.form.get('resignation_date'),
            'reason': request.form.get('reason'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'effective_date': request.form.get('effective_date'),
            'notes': request.form.get('notes'),
            'status': request.form.get('status', 'pending')
        }
        
        if status_manager.add_resignation(data):
            flash('تم إضافة الاستقالة بنجاح', 'success')
            return redirect(url_for('special_status.resignations'))
        else:
            flash('حدث خطأ في إضافة الاستقالة', 'error')
    
    employees = status_manager.get_all_employees()
    return render_template('special_status/add_resignation.html', employees=employees)

# ================================
# مسارات الوفيات
# ================================

@special_status_bp.route('/deaths')
def deaths():
    """قائمة الوفيات"""
    deaths = status_manager.get_deaths()
    return render_template('special_status/deaths.html', deaths=deaths)

@special_status_bp.route('/deaths/add', methods=['GET', 'POST'])
def add_death():
    """إضافة وفاة جديدة"""
    if request.method == 'POST':
        data = {
            'employee_id': request.form.get('employee_id'),
            'death_date': request.form.get('death_date'),
            'death_place': request.form.get('death_place'),
            'death_cause': request.form.get('death_cause'),
            'certificate_number': request.form.get('certificate_number'),
            'burial_place': request.form.get('burial_place'),
            'family_contact': request.form.get('family_contact'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'notes': request.form.get('notes')
        }
        
        if status_manager.add_death(data):
            flash('تم إضافة الوفاة بنجاح', 'success')
            return redirect(url_for('special_status.deaths'))
        else:
            flash('حدث خطأ في إضافة الوفاة', 'error')
    
    employees = status_manager.get_all_employees()
    return render_template('special_status/add_death.html', employees=employees)

# ================================
# مسارات التقاعد
# ================================

@special_status_bp.route('/retirements')
def retirements():
    """قائمة المتقاعدين"""
    retirements = status_manager.get_retirements()
    return render_template('special_status/retirements.html', retirements=retirements)

@special_status_bp.route('/retirements/add', methods=['GET', 'POST'])
def add_retirement():
    """إضافة تقاعد جديد"""
    if request.method == 'POST':
        data = {
            'employee_id': request.form.get('employee_id'),
            'retirement_date': request.form.get('retirement_date'),
            'retirement_type': request.form.get('retirement_type'),
            'years_of_service': request.form.get('years_of_service'),
            'pension_amount': request.form.get('pension_amount'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'effective_date': request.form.get('effective_date'),
            'notes': request.form.get('notes')
        }
        
        if status_manager.add_retirement(data):
            flash('تم إضافة التقاعد بنجاح', 'success')
            return redirect(url_for('special_status.retirements'))
        else:
            flash('حدث خطأ في إضافة التقاعد', 'error')
    
    employees = status_manager.get_all_employees()
    retirement_types = status_manager.retirement_types
    return render_template('special_status/add_retirement.html', 
                         employees=employees, retirement_types=retirement_types)

# ================================
# مسارات التحويل الخارجي
# ================================

@special_status_bp.route('/external_transfers')
def external_transfers():
    """قائمة التحويلات الخارجية"""
    # سيتم إضافة هذه الوظيفة لاحقاً
    return render_template('special_status/external_transfers.html', transfers=[])

@special_status_bp.route('/external_transfers/add', methods=['GET', 'POST'])
def add_external_transfer():
    """إضافة تحويل خارجي جديد"""
    if request.method == 'POST':
        # سيتم إضافة هذه الوظيفة لاحقاً
        flash('سيتم إضافة هذه الميزة قريباً', 'info')
        return redirect(url_for('special_status.external_transfers'))
    
    employees = status_manager.get_all_employees()
    return render_template('special_status/add_external_transfer.html', employees=employees)

# ================================
# مسارات الاستيداع
# ================================

@special_status_bp.route('/leave_of_absence')
def leave_of_absence():
    """قائمة المستودعين"""
    # سيتم إضافة هذه الوظيفة لاحقاً
    return render_template('special_status/leave_of_absence.html', leaves=[])

@special_status_bp.route('/leave_of_absence/add', methods=['GET', 'POST'])
def add_leave_of_absence():
    """إضافة استيداع جديد"""
    if request.method == 'POST':
        # سيتم إضافة هذه الوظيفة لاحقاً
        flash('سيتم إضافة هذه الميزة قريباً', 'info')
        return redirect(url_for('special_status.leave_of_absence'))
    
    employees = status_manager.get_all_employees()
    leave_reasons = status_manager.leave_reasons
    return render_template('special_status/add_leave_of_absence.html', 
                         employees=employees, leave_reasons=leave_reasons)

# ================================
# مسارات التوقيف
# ================================

@special_status_bp.route('/suspensions')
def suspensions():
    """قائمة الموقوفين"""
    # سيتم إضافة هذه الوظيفة لاحقاً
    return render_template('special_status/suspensions.html', suspensions=[])

@special_status_bp.route('/suspensions/add', methods=['GET', 'POST'])
def add_suspension():
    """إضافة توقيف جديد"""
    if request.method == 'POST':
        # سيتم إضافة هذه الوظيفة لاحقاً
        flash('سيتم إضافة هذه الميزة قريباً', 'info')
        return redirect(url_for('special_status.suspensions'))
    
    employees = status_manager.get_all_employees()
    suspension_reasons = status_manager.suspension_reasons
    return render_template('special_status/add_suspension.html', 
                         employees=employees, suspension_reasons=suspension_reasons)

# ================================
# API للحصول على معلومات الموظف
# ================================

@special_status_bp.route('/api/employee/<int:employee_id>')
def api_employee_info(employee_id):
    """API للحصول على معلومات الموظف"""
    employee = status_manager.get_employee_info(employee_id)
    if employee:
        return jsonify(employee)
    else:
        return jsonify({'error': 'الموظف غير موجود'}), 404

# ================================
# تسجيل Blueprint
# ================================

def register_special_status_routes(app):
    """تسجيل مسارات الحالات الخاصة"""
    app.register_blueprint(special_status_bp)
