{% extends "base.html" %}

{% block title %}عطلة طويلة الأمد - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}عطلة طويلة الأمد{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-calendar-times me-2"></i>عطلة طويلة الأمد
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الرتبة:</strong> {{ employee.rank or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-success">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج العطلة طويلة الأمد -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>بيانات العطلة طويلة الأمد
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="longLeaveForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="review_date" class="form-label">تاريخ المراجعة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="review_date" name="review_date" required>
                        <div class="form-text">تاريخ مراجعة حالة الموظف</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="document_number" class="form-label">رقم الوثيقة</label>
                        <input type="text" class="form-control" id="document_number" name="document_number" 
                               placeholder="أدخل رقم الوثيقة الطبية أو الإدارية">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="document_date" class="form-label">تاريخ الوثيقة</label>
                        <input type="date" class="form-control" id="document_date" name="document_date">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="granting_authority" class="form-label">الهيئة المانحة <span class="text-danger">*</span></label>
                        <select class="form-select" id="granting_authority" name="granting_authority" required>
                            <option value="">اختر الهيئة المانحة</option>
                            <option value="اللجنة الطبية المختصة">اللجنة الطبية المختصة</option>
                            <option value="مديرية الموارد البشرية">مديرية الموارد البشرية</option>
                            <option value="الإدارة المركزية">الإدارة المركزية</option>
                            <option value="وزارة المالية">وزارة المالية</option>
                            <option value="المديرية الجهوية">المديرية الجهوية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- حقل الهيئة المخصصة -->
            <div class="row" id="customAuthorityRow" style="display: none;">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="custom_authority" class="form-label">اسم الهيئة</label>
                        <input type="text" class="form-control" id="custom_authority" name="custom_authority" 
                               placeholder="أدخل اسم الهيئة المانحة">
                    </div>
                </div>
            </div>
            
            <!-- معاينة مدة العطلة -->
            <div class="alert alert-info" id="durationPreview" style="display: none;">
                <i class="fas fa-calendar-alt me-2"></i>
                <strong>مدة العطلة المتوقعة:</strong> <span id="calculatedDuration"></span>
            </div>
            
            <!-- معلومات مهمة -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> العطلة طويلة الأمد تتطلب مراجعة دورية لحالة الموظف وقد تؤثر على الحقوق الوظيفية.
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> يجب تحديد تاريخ المراجعة بدقة لمتابعة حالة الموظف وإمكانية عودته للعمل.
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-warning" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل العطلة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal تأكيد العطلة -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>تأكيد العطلة طويلة الأمد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من منح عطلة طويلة الأمد للموظف:</p>
                <div class="text-center p-3 border rounded bg-light">
                    <h6><strong>{{ employee.first_name }} {{ employee.last_name }}</strong></h6>
                    <p class="mb-0">رقم التسجيل: {{ employee.registration_number }}</p>
                </div>
                <div class="mt-3">
                    <p><strong>تاريخ البداية:</strong> <span id="confirmStartDate"></span></p>
                    <p><strong>تاريخ المراجعة:</strong> <span id="confirmReviewDate"></span></p>
                    <p><strong>الهيئة المانحة:</strong> <span id="confirmAuthority"></span></p>
                    <p><strong>المدة المتوقعة:</strong> <span id="confirmDuration"></span></p>
                </div>
                <div class="alert alert-info mt-3 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم تعليق الموظف مؤقتاً وتحديد موعد لمراجعة حالته.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>تأكيد العطلة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إظهار/إخفاء حقل الهيئة المخصصة
document.getElementById('granting_authority').addEventListener('change', function() {
    const customAuthorityRow = document.getElementById('customAuthorityRow');
    if (this.value === 'أخرى') {
        customAuthorityRow.style.display = 'block';
        document.getElementById('custom_authority').required = true;
    } else {
        customAuthorityRow.style.display = 'none';
        document.getElementById('custom_authority').required = false;
    }
});

// حساب مدة العطلة عند تغيير التواريخ
function calculateDuration() {
    const startDate = document.getElementById('start_date').value;
    const reviewDate = document.getElementById('review_date').value;
    
    if (startDate && reviewDate) {
        const start = new Date(startDate);
        const review = new Date(reviewDate);
        
        if (review > start) {
            const diffTime = Math.abs(review - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            const diffMonths = Math.floor(diffDays / 30);
            const remainingDays = diffDays % 30;
            
            let durationText = '';
            if (diffMonths > 0) {
                durationText += diffMonths + ' شهر';
                if (remainingDays > 0) {
                    durationText += ' و ' + remainingDays + ' يوم';
                }
            } else {
                durationText = diffDays + ' يوم';
            }
            
            document.getElementById('calculatedDuration').textContent = durationText;
            document.getElementById('durationPreview').style.display = 'block';
        } else {
            document.getElementById('durationPreview').style.display = 'none';
        }
    } else {
        document.getElementById('durationPreview').style.display = 'none';
    }
}

document.getElementById('start_date').addEventListener('change', calculateDuration);
document.getElementById('review_date').addEventListener('change', calculateDuration);

document.getElementById('longLeaveForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const startDate = document.getElementById('start_date').value;
    const reviewDate = document.getElementById('review_date').value;
    const grantingAuthority = document.getElementById('granting_authority').value;
    
    if (!startDate || !reviewDate || !grantingAuthority) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من أن تاريخ المراجعة بعد تاريخ البداية
    const start = new Date(startDate);
    const review = new Date(reviewDate);
    
    if (review <= start) {
        alert('تاريخ المراجعة يجب أن يكون بعد تاريخ البداية');
        return;
    }
    
    // التحقق من الهيئة المخصصة إذا كانت مطلوبة
    if (grantingAuthority === 'أخرى') {
        const customAuthority = document.getElementById('custom_authority').value.trim();
        if (!customAuthority) {
            alert('يرجى إدخال اسم الهيئة المانحة');
            return;
        }
    }
    
    // حساب المدة للتأكيد
    const diffTime = Math.abs(review - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);
    const remainingDays = diffDays % 30;
    
    let durationText = '';
    if (diffMonths > 0) {
        durationText += diffMonths + ' شهر';
        if (remainingDays > 0) {
            durationText += ' و ' + remainingDays + ' يوم';
        }
    } else {
        durationText = diffDays + ' يوم';
    }
    
    // تحديث بيانات التأكيد
    document.getElementById('confirmStartDate').textContent = startDate;
    document.getElementById('confirmReviewDate').textContent = reviewDate;
    document.getElementById('confirmAuthority').textContent = grantingAuthority === 'أخرى' ? 
        document.getElementById('custom_authority').value : grantingAuthority;
    document.getElementById('confirmDuration').textContent = durationText;
    
    // إظهار مودال التأكيد
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
});

document.getElementById('confirmSubmit').addEventListener('click', function() {
    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
    
    // تعطيل الزر وإظهار مؤشر التحميل
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
    
    // تحديث قيمة الهيئة إذا كانت مخصصة
    const authoritySelect = document.getElementById('granting_authority');
    if (authoritySelect.value === 'أخرى') {
        authoritySelect.value = document.getElementById('custom_authority').value;
    }
    
    // إرسال النموذج
    document.getElementById('longLeaveForm').submit();
});

// تحديد تاريخ اليوم كحد أدنى لتاريخ البداية
document.getElementById('start_date').min = new Date().toISOString().split('T')[0];

// تحديد تاريخ اليوم كحد أقصى لتاريخ الوثيقة
document.getElementById('document_date').max = new Date().toISOString().split('T')[0];
</script>
{% endblock %}