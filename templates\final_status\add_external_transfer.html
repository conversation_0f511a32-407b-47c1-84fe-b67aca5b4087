{% extends "base.html" %}

{% block title %}تحويل خارجي - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exchange-alt"></i>
                        تحويل خارجي
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- تحذير مهم -->
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم:</h6>
                        <p class="mb-1">• سيتم نقل الموظف من جدول الموظفين النشطين إلى جدول التحويل الخارجي</p>
                        <p class="mb-1">• لن يُحسب الموظف في إجمالي عدد الموظفين النشطين</p>
                        <p class="mb-0">• سيتم الاحتفاظ بجميع بياناته في سجل منفصل</p>
                    </div>

                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user"></i> معلومات الموظف:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                                <p class="mb-1"><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الحالة الحالية:</strong> {{ employee.status }}</p>
                                {% if employee.hire_date %}
                                <p class="mb-0"><strong>تاريخ التوظيف:</strong> {{ employee.hire_date }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <form method="POST" onsubmit="return confirmTransfer()">
                        <div class="row">
                            <!-- تاريخ التحويل -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="transfer_date">
                                        <i class="fas fa-calendar-alt"></i>
                                        تاريخ التحويل <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="transfer_date" 
                                           name="transfer_date" required>
                                </div>
                            </div>

                            <!-- الجهة المحول إليها -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="destination_directorate">
                                        <i class="fas fa-building"></i>
                                        الجهة المحول إليها <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="destination_directorate" 
                                            name="destination_directorate" required>
                                        <option value="">اختر الجهة...</option>
                                        <option value="مديرية جمارك الجزائر">مديرية جمارك الجزائر</option>
                                        <option value="مديرية جمارك وهران">مديرية جمارك وهران</option>
                                        <option value="مديرية جمارك قسنطينة">مديرية جمارك قسنطينة</option>
                                        <option value="مديرية جمارك عنابة">مديرية جمارك عنابة</option>
                                        <option value="مديرية جمارك سطيف">مديرية جمارك سطيف</option>
                                        <option value="مديرية جمارك بجاية">مديرية جمارك بجاية</option>
                                        <option value="مديرية جمارك تلمسان">مديرية جمارك تلمسان</option>
                                        <option value="مديرية جمارك سيدي بلعباس">مديرية جمارك سيدي بلعباس</option>
                                        <option value="مديرية جمارك باتنة">مديرية جمارك باتنة</option>
                                        <option value="مديرية جمارك ورقلة">مديرية جمارك ورقلة</option>
                                        <option value="مديرية جمارك بسكرة">مديرية جمارك بسكرة</option>
                                        <option value="مديرية جمارك الأغواط">مديرية جمارك الأغواط</option>
                                        <option value="وزارة المالية">وزارة المالية</option>
                                        <option value="إدارة أخرى">إدارة أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">
                                        <i class="fas fa-file-alt"></i>
                                        رقم مقرر التحويل <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="decision_number" 
                                           name="decision_number" required
                                           placeholder="رقم المقرر الإداري">
                                </div>
                            </div>

                            <!-- تاريخ المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">
                                        <i class="fas fa-calendar"></i>
                                        تاريخ المقرر <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="decision_date" 
                                           name="decision_date" required>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات إضافية -->
                        <div class="form-group">
                            <label for="notes">
                                <i class="fas fa-sticky-note"></i>
                                ملاحظات إضافية (اختياري)
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="أي ملاحظات حول التحويل..."></textarea>
                        </div>

                        <!-- تأكيد العملية -->
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_transfer" required>
                                <label class="custom-control-label text-warning" for="confirm_transfer">
                                    <strong>أؤكد صحة المعلومات وأن الموظف سيتم تحويله خارجياً</strong>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-exchange-alt"></i>
                                تأكيد التحويل الخارجي
                            </button>
                            <a href="{{ url_for('employees') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmTransfer() {
    const employeeName = "{{ employee.first_name }} {{ employee.last_name }}";
    const transferDate = document.getElementById('transfer_date').value;
    const destination = document.getElementById('destination_directorate').value;
    const decisionNumber = document.getElementById('decision_number').value;
    
    const confirmMessage = `
هل أنت متأكد من تحويل الموظف خارجياً؟

الموظف: ${employeeName}
تاريخ التحويل: ${transferDate}
الجهة المحول إليها: ${destination}
رقم المقرر: ${decisionNumber}

تحذير: سيتم نقل الموظف لجدول التحويل الخارجي وحذفه من قائمة الموظفين النشطين.
هذا الإجراء لا يمكن التراجع عنه بسهولة.
    `;
    
    return confirm(confirmMessage);
}

// التحقق من صحة التواريخ
document.getElementById('decision_date').addEventListener('change', function() {
    const decisionDate = new Date(this.value);
    const transferDate = new Date(document.getElementById('transfer_date').value);
    
    if (transferDate && decisionDate > transferDate) {
        alert('تاريخ المقرر لا يمكن أن يكون بعد تاريخ التحويل');
        this.value = '';
    }
});

document.getElementById('transfer_date').addEventListener('change', function() {
    const transferDate = new Date(this.value);
    const decisionDate = new Date(document.getElementById('decision_date').value);
    
    if (decisionDate && decisionDate > transferDate) {
        alert('تاريخ التحويل لا يمكن أن يكون قبل تاريخ المقرر');
        this.value = '';
    }
});
</script>

<style>
.custom-control-label {
    font-weight: bold;
}

.alert-danger {
    border-left: 4px solid #dc3545;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}
</style>
{% endblock %}