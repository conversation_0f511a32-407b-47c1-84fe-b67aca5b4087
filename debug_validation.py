#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة التحقق من البيانات
"""

from app import validate_social_security_number, validate_postal_account

def debug_validation():
    """تشخيص دوال التحقق"""
    print("🔍 تشخيص دوال التحقق")
    print("=" * 50)
    
    # اختبار الحقول الفارغة
    test_cases = [
        ('', None, None, 'حقل فارغ'),
        ('   ', None, None, 'حقل بمسافات'),
        (None, None, None, 'حقل None'),
        ('***************', 1900, 'ذكر', 'رقم صحيح'),
        ('abc', None, None, 'نص غير صحيح'),
        ('12345', None, None, 'رقم قصير'),
    ]
    
    print("🧪 اختبار رقم الضمان الاجتماعي:")
    for ssn, year, gender, desc in test_cases:
        try:
            result = validate_social_security_number(ssn, year, gender)
            print(f"  {desc}: {ssn} -> {result}")
        except Exception as e:
            print(f"  {desc}: {ssn} -> خطأ: {e}")
    
    print(f"\n🧪 اختبار الحساب البريدي:")
    postal_cases = [
        ('', 'حقل فارغ'),
        ('   ', 'حقل بمسافات'),
        (None, 'حقل None'),
        ('**********', 'رقم صحيح'),
        ('abc', 'نص غير صحيح'),
        ('12345', 'رقم قصير'),
    ]
    
    for account, desc in postal_cases:
        try:
            result = validate_postal_account(account)
            print(f"  {desc}: {account} -> {result}")
        except Exception as e:
            print(f"  {desc}: {account} -> خطأ: {e}")

def test_form_data_processing():
    """اختبار معالجة بيانات النموذج"""
    print(f"\n🔍 اختبار معالجة بيانات النموذج:")
    print("-" * 40)
    
    # محاكاة بيانات النموذج
    form_data = {
        'registration_number': '123456',
        'first_name': 'محمد',
        'last_name': 'أحمد',
        'social_security_number': '',  # فارغ
        'postal_account': '',          # فارغ
        'birth_date': '',              # فارغ
        'gender': ''                   # فارغ
    }
    
    print("📝 بيانات النموذج:")
    for key, value in form_data.items():
        print(f"  {key}: '{value}' (فارغ: {not value})")
    
    # اختبار التحقق
    print(f"\n🧪 نتائج التحقق:")
    
    # رقم التسجيل
    from app import validate_registration_number
    reg_result = validate_registration_number(form_data['registration_number'])
    print(f"  رقم التسجيل: {reg_result}")
    
    # رقم الضمان الاجتماعي
    birth_year = None
    if form_data['birth_date']:
        birth_year = int(form_data['birth_date'][:4])
    
    ssn_result = validate_social_security_number(
        form_data['social_security_number'], 
        birth_year, 
        form_data['gender']
    )
    print(f"  رقم الضمان الاجتماعي: {ssn_result}")
    
    # الحساب البريدي
    postal_result = validate_postal_account(form_data['postal_account'])
    print(f"  الحساب البريدي: {postal_result}")

if __name__ == "__main__":
    debug_validation()
    test_form_data_processing()