#!/usr/bin/env python3
import sqlite3
import os

def test_database():
    db_path = 'customs_employees.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 الجداول الموجودة: {tables}")
        
        # فحص جدول wilayas
        if 'wilayas' in tables:
            cursor.execute("SELECT COUNT(*) FROM wilayas")
            count = cursor.fetchone()[0]
            print(f"✅ جدول wilayas موجود مع {count} سجل")
            
            # عرض أول 5 ولايات
            cursor.execute("SELECT id, name FROM wilayas LIMIT 5")
            wilayas = cursor.fetchall()
            print("📍 أول 5 ولايات:")
            for w in wilayas:
                print(f"   {w[0]}: {w[1]}")
        else:
            print("❌ جدول wilayas غير موجود")
        
        # فحص جدول employees
        if 'employees' in tables:
            cursor.execute("SELECT COUNT(*) FROM employees")
            count = cursor.fetchone()[0]
            print(f"✅ جدول employees موجود مع {count} سجل")
        else:
            print("❌ جدول employees غير موجود")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

if __name__ == '__main__':
    test_database()