#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جداول الحالات الخاصة المحدثة حسب المواصفات الجديدة
"""

import sqlite3
from datetime import datetime

def create_updated_special_status_tables():
    """إنشاء جداول الحالات الخاصة المحدثة"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        print("🔧 إنشاء جداول الحالات الخاصة المحدثة...")
        
        # 1. جدول الاستيداع المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_leave_of_absence_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                period_number INTEGER NOT NULL, -- الفترة (الأولى، الثانية...)
                duration_months INTEGER NOT NULL, -- المدة بالأشهر
                reason TEXT NOT NULL, -- سبب الاستيداع (من قائمة الإعدادات)
                start_date DATE NOT NULL, -- تاريخ بداية الاستيداع
                end_date DATE NOT NULL, -- تاريخ نهاية الاستيداع (محسوب)
                decision_number TEXT, -- رقم المقرر/الوثيقة
                decision_date DATE, -- تاريخ المقرر/الوثيقة
                status TEXT DEFAULT 'active', -- الحالة
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 2. جدول التوقيف المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_suspensions_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                suspension_date DATE NOT NULL, -- تاريخ التوقيف
                reason TEXT NOT NULL, -- سبب التوقيف
                decision_number TEXT, -- رقم مقرر التوقيف
                decision_date DATE, -- تاريخ مقرر التوقيف
                end_date DATE, -- تاريخ انتهاء التوقيف (إن وجد)
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 3. جدول العزل (جديد)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_dismissals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                dismissal_date DATE NOT NULL, -- تاريخ العزل
                reason TEXT NOT NULL, -- سبب العزل
                decision_number TEXT, -- رقم مقرر العزل
                decision_date DATE, -- تاريخ مقرر العزل
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 4. جدول الوفيات المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_deaths_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                death_date DATE NOT NULL, -- تاريخ الوفاة
                cause_type TEXT NOT NULL DEFAULT 'عادية', -- السبب (عادية - حادث عمل)
                certificate_number TEXT, -- رقم شهادة الوفاة
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 5. جدول الانتداب المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_assignments_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                assignment_date DATE NOT NULL, -- تاريخ الانتداب
                duration_months INTEGER, -- مدة الانتداب بالأشهر
                reason TEXT, -- سبب الانتداب
                location TEXT NOT NULL, -- مكان الانتداب
                end_date DATE, -- تاريخ انتهاء الانتداب (محسوب)
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 6. جدول الاستقالات المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_resignations_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                request_date DATE NOT NULL, -- تاريخ الطلب
                reason TEXT, -- سبب الاستقالة
                is_approved BOOLEAN, -- مقبولة (نعم/لا)
                approval_decision_number TEXT, -- رقم مقرر الموافقة
                approval_decision_date DATE, -- تاريخ المقرر
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 7. جدول التحويل الخارجي المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_external_transfers_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                end_of_duties_date DATE NOT NULL, -- تاريخ إنهاء المهام
                destination_directorate TEXT NOT NULL, -- المديرية المحول لها
                decision_number TEXT, -- رقم المقرر
                decision_date DATE, -- تاريخ المقرر
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 8. جدول عطلة طويلة الأمد (جديد)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_long_term_leave (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL, -- تاريخ البداية
                review_date DATE, -- تاريخ المراجعة
                document_number TEXT, -- رقم الوثيقة
                document_date DATE, -- تاريخ الوثيقة
                granting_authority TEXT, -- الهيئة المانحة
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 9. جدول التقاعد المحدث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_retirements_updated (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                retirement_date DATE NOT NULL, -- تاريخ التقاعد
                decision_number TEXT, -- رقم مقرر التقاعد
                decision_date DATE, -- تاريخ مقرر التقاعد
                retirement_card_number TEXT, -- رقم بطاقة التقاعد
                card_issue_date DATE, -- تاريخ صدورها
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 10. جدول الخدمة الوطنية (جديد)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_national_service (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL, -- تاريخ بداية الخدمة
                end_date DATE, -- تاريخ نهاية الخدمة
                decision_number TEXT, -- رقم مقرر الخدمة الوطنية
                decision_date DATE, -- تاريخ مقرر الخدمة الوطنية
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 11. جدول الدراسة أو التكوين (جديد)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_study_training (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL, -- تاريخ بداية الدراسة أو التكوين
                type TEXT NOT NULL, -- نوع التكوين أو الدراسة
                decision_number TEXT, -- رقم مقرر الدراسة أو التكوين
                decision_date DATE, -- تاريخ مقرر الدراسة أو التكوين
                end_date DATE, -- تاريخ الانتهاء
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 12. جدول أسباب الاستيداع (للإعدادات)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reason TEXT NOT NULL UNIQUE,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج أسباب الاستيداع الافتراضية
        default_reasons = [
            'رعاية الأطفال',
            'الدراسة',
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        for reason in default_reasons:
            cursor.execute('''
                INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء جداول الحالات الخاصة المحدثة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def check_updated_tables():
    """فحص الجداول المحدثة"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        tables = [
            'employee_leave_of_absence_updated',
            'employee_suspensions_updated',
            'employee_dismissals',
            'employee_deaths_updated',
            'employee_assignments_updated',
            'employee_resignations_updated',
            'employee_external_transfers_updated',
            'employee_long_term_leave',
            'employee_retirements_updated',
            'employee_national_service',
            'employee_study_training',
            'leave_of_absence_reasons'
        ]
        
        print("\n📋 فحص جداول الحالات الخاصة المحدثة:")
        print("=" * 60)
        
        all_exist = True
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            exists = cursor.fetchone()
            
            if exists:
                print(f"✅ {table}")
                # عد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   📊 عدد السجلات: {count}")
            else:
                print(f"❌ {table} - غير موجود")
                all_exist = False
        
        conn.close()
        return all_exist
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

if __name__ == '__main__':
    print("🚀 إنشاء جداول الحالات الخاصة المحدثة")
    print("=" * 60)
    
    if create_updated_special_status_tables():
        check_updated_tables()
        print("\n🎉 تم إعداد جداول الحالات الخاصة المحدثة بنجاح!")
    else:
        print("\n❌ فشل في إعداد الجداول")
