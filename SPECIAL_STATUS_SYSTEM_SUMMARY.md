# نظام الحالات الخاصة للموظفين - ملخص النظام

## 🎯 نظرة عامة
تم إنشاء نظام شامل لإدارة الحالات الخاصة للموظفين في نظام إدارة موظفي الجمارك الجزائرية. يتيح هذا النظام إدارة وتتبع جميع الحالات الخاصة التي قد يمر بها الموظف خلال مسيرته المهنية.

## 📋 الحالات الخاصة المدعومة

### 1. الاستقالات (Resignations)
- تسجيل طلبات الاستقالة
- تتبع حالة الاستقالة (معلقة، مؤكدة، مرفوضة)
- تسجيل أسباب الاستقالة
- إدارة قرارات الاستقالة وتواريخها

### 2. الوفيات (Deaths)
- تسجيل حالات وفاة الموظفين
- تسجيل تفاصيل الوفاة (المكان، السبب، الشهادة)
- معلومات الدفن والاتصال بالعائلة
- إدارة القرارات الإدارية المتعلقة بالوفاة

### 3. التقاعد (Retirements)
- تسجيل حالات التقاعد
- أنواع التقاعد المختلفة
- حساب سنوات الخدمة والمعاش
- إدارة قرارات التقاعد

### 4. التحويل الخارجي (External Transfers)
- تحويل الموظفين لجهات خارجية
- تسجيل الجهة المستقبلة والمنصب
- أسباب التحويل وقراراته

### 5. الاستيداع (Leave of Absence)
- إدارة حالات الاستيداع
- تحديد مدة الاستيداع وأسبابه
- تتبع الحالة النشطة للاستيداع

### 6. التوقيف (Suspensions)
- تسجيل حالات توقيف الموظفين
- أسباب التوقيف ومدته
- إدارة قرارات التوقيف

### 7. الانتداب (Assignments)
- تسجيل حالات انتداب الموظفين
- الجهة المنتدب إليها والمنصب
- مدة الانتداب وحالته

## 🗂️ هيكل قاعدة البيانات

### الجداول المنشأة:
1. `employee_resignations` - جدول الاستقالات
2. `employee_deaths` - جدول الوفيات
3. `employee_retirements` - جدول التقاعد
4. `employee_external_transfers` - جدول التحويلات الخارجية
5. `employee_leave_of_absence` - جدول الاستيداع
6. `employee_suspensions` - جدول التوقيف
7. `employee_assignments` - جدول الانتداب

### الحقول المشتركة:
- `id` - المعرف الفريد
- `employee_id` - معرف الموظف (مرتبط بجدول الموظفين)
- `decision_number` - رقم القرار
- `decision_date` - تاريخ القرار
- `notes` - ملاحظات
- `created_at` - تاريخ الإنشاء
- `updated_at` - تاريخ التحديث

## 🌐 الواجهات والصفحات

### الصفحة الرئيسية
- `/special_status/` - الصفحة الرئيسية للحالات الخاصة
- عرض إحصائيات سريعة لجميع الحالات
- روابط سريعة لإدارة كل نوع من الحالات

### صفحات الاستقالات
- `/special_status/resignations` - قائمة الاستقالات
- `/special_status/resignations/add` - إضافة استقالة جديدة
- عرض تفاصيل الاستقالة في نافذة منبثقة
- إمكانية تأكيد أو رفض الاستقالات

### صفحات الوفيات
- `/special_status/deaths` - سجل الوفيات
- `/special_status/deaths/add` - تسجيل وفاة جديدة
- عرض تفاصيل شاملة للوفاة
- إمكانية طباعة تفاصيل الوفاة

### صفحات أخرى (قيد التطوير)
- صفحات التقاعد والتحويل الخارجي
- صفحات الاستيداع والتوقيف
- صفحات الانتداب

## 🔧 الملفات التقنية

### ملفات Python:
1. `special_status_routes.py` - مسارات Flask للحالات الخاصة
2. `special_status_manager.py` - مدير الحالات الخاصة
3. `create_special_status_tables.py` - إنشاء جداول قاعدة البيانات
4. `setup_special_status.py` - إعداد وتفعيل النظام

### ملفات HTML:
1. `templates/special_status/index.html` - الصفحة الرئيسية
2. `templates/special_status/resignations.html` - قائمة الاستقالات
3. `templates/special_status/add_resignation.html` - إضافة استقالة
4. `templates/special_status/deaths.html` - سجل الوفيات
5. `templates/special_status/add_death.html` - تسجيل وفاة

## 🚀 كيفية الوصول للنظام

### من الشريط الجانبي:
- انقر على "الحالات الخاصة" في الشريط الجانبي
- سيتم توجيهك إلى الصفحة الرئيسية للنظام

### الروابط المباشرة:
- الصفحة الرئيسية: `http://localhost:5000/special_status/`
- الاستقالات: `http://localhost:5000/special_status/resignations`
- الوفيات: `http://localhost:5000/special_status/deaths`

## ✨ المميزات الرئيسية

### 1. واجهة مستخدم متقدمة
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان مميزة لكل نوع من الحالات
- أيقونات واضحة ومعبرة

### 2. إدارة شاملة للبيانات
- نماذج تفاعلية لإدخال البيانات
- التحقق من صحة البيانات
- عرض معلومات الموظف تلقائياً

### 3. تتبع الحالات
- إحصائيات فورية لجميع الحالات
- تتبع حالة كل طلب (معلق، مؤكد، مرفوض)
- تاريخ شامل لجميع التغييرات

### 4. تكامل مع النظام الرئيسي
- ربط مباشر مع جدول الموظفين
- تحديث تلقائي لحالة الموظف
- تكامل مع الشريط الجانبي

## 🔄 التحديثات المستقبلية

### المرحلة التالية:
1. إكمال صفحات التقاعد والتحويل الخارجي
2. إضافة صفحات الاستيداع والتوقيف
3. تطوير نظام التقارير والإحصائيات
4. إضافة إمكانية طباعة الوثائق
5. تطوير نظام الإشعارات

### تحسينات مقترحة:
- إضافة نظام الموافقات المتدرجة
- تطوير واجهة API للتكامل الخارجي
- إضافة نظام النسخ الاحتياطي التلقائي
- تطوير تطبيق الهاتف المحمول

## 📊 الإحصائيات الحالية
- ✅ 7 أنواع من الحالات الخاصة
- ✅ 7 جداول في قاعدة البيانات
- ✅ 5 صفحات ويب جاهزة
- ✅ تكامل كامل مع النظام الرئيسي

---

**تم إنشاء هذا النظام بنجاح وهو جاهز للاستخدام! 🎉**
