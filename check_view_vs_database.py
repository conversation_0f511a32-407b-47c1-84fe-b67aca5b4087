#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص الفرق بين الحقول في قاعدة البيانات وما يظهر في صفحة المعاينة
"""

import sqlite3
from app import app

def check_view_vs_database():
    """مقارنة ما يظهر في المعاينة مع ما هو موجود في قاعدة البيانات"""
    print("🔍 فحص الفرق بين قاعدة البيانات وصفحة المعاينة...")
    print("=" * 80)
    
    # الحصول على جميع حقول قاعدة البيانات
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(employees)")
    db_columns = cursor.fetchall()
    
    # الحصول على بيانات الموظف التجريبي
    cursor.execute("SELECT * FROM employees WHERE id = 11")
    employee_data = cursor.fetchone()
    conn.close()
    
    if not employee_data:
        print("❌ لم يتم العثور على الموظف التجريبي")
        return
    
    print(f"📋 إجمالي حقول قاعدة البيانات: {len(db_columns)}")
    print(f"📊 بيانات الموظف التجريبي (ID: 11):")
    print("-" * 80)
    
    # عرض جميع الحقول مع قيمها
    filled_fields = 0
    empty_fields = 0
    
    for i, col in enumerate(db_columns):
        col_name = col[1]
        value = employee_data[i] if i < len(employee_data) else None
        
        if value is not None and value != '':
            status = "✅ موجود"
            filled_fields += 1
            # تقصير القيم الطويلة
            if isinstance(value, str) and len(str(value)) > 40:
                display_value = str(value)[:37] + "..."
            else:
                display_value = value
        else:
            status = "❌ فارغ"
            empty_fields += 1
            display_value = "NULL"
        
        print(f"{i+1:2d}. {col_name:<30} = {display_value:<25} {status}")
    
    print("-" * 80)
    print(f"📊 ملخص البيانات:")
    print(f"   ✅ الحقول المملوءة: {filled_fields}")
    print(f"   ❌ الحقول الفارغة: {empty_fields}")
    print(f"   📋 إجمالي الحقول: {len(db_columns)}")
    
    # اختبار صفحة المعاينة
    print(f"\n🔍 اختبار صفحة المعاينة...")
    
    with app.test_client() as client:
        response = client.get('/employee/11')
        
        if response.status_code == 200:
            content = response.data.decode('utf-8')
            print(f"✅ صفحة المعاينة تعمل")
            print(f"📄 حجم المحتوى: {len(content):,} حرف")
            
            # فحص الحقول الجديدة في المحتوى
            new_fields_to_check = [
                ('secondary_address', 'العنوان الثانوي'),
                ('emergency_contact_name', 'الشخص المتصل به في حالة الضرورة'),
                ('emergency_contact_address', 'عنوان الشخص المتصل به'),
                ('rank_promotion_date', 'تاريخ الترقية في الرتبة'),
                ('current_position_id', 'الوظيفة الحالية'),
                ('position_assignment_date', 'تاريخ التعين في الوظيفة'),
                ('directorate_id', 'المديرية'),
                ('assignment_location_id', 'مكان التعيين'),
                ('initial_rank_id', 'رتبة التوظيف'),
                ('professional_card_number', 'رقم بطاقة المهنية'),
                ('professional_card_issue_date', 'تاريخ صدور بطاقة المهنية'),
                ('national_id_number', 'رقم بطاقة التعريف الوطنية'),
                ('national_id_issue_date', 'تاريخ صدور بطاقة التعريف'),
                ('national_id_issue_place_id', 'مكان صدور بطاقة التعريف'),
                ('driving_license_number', 'رقم رخصة السياقة'),
                ('driving_license_category', 'صنف رخصة السياقة'),
                ('driving_license_issue_date', 'تاريخ صدور رخصة السياقة'),
                ('driving_license_issue_place_id', 'مكان صدور رخصة السياقة'),
                ('mutual_card_number', 'رقم بطاقة التعاضدية'),
                ('mutual_card_issue_date', 'تاريخ صدور بطاقة التعاضدية')
            ]
            
            print(f"\n🔍 فحص الحقول الجديدة في صفحة المعاينة:")
            print("-" * 80)
            
            displayed_new_fields = 0
            missing_new_fields = []
            
            for field_name, field_desc in new_fields_to_check:
                if field_desc in content:
                    print(f"✅ {field_desc:<40} معروض")
                    displayed_new_fields += 1
                else:
                    print(f"❌ {field_desc:<40} غير معروض")
                    missing_new_fields.append((field_name, field_desc))
            
            print("-" * 80)
            print(f"📊 نتائج فحص الحقول الجديدة:")
            print(f"   ✅ الحقول الجديدة المعروضة: {displayed_new_fields}/{len(new_fields_to_check)}")
            print(f"   ❌ الحقول الجديدة المفقودة: {len(missing_new_fields)}")
            
            if missing_new_fields:
                print(f"\n⚠️  الحقول الجديدة غير المعروضة:")
                for field_name, field_desc in missing_new_fields:
                    print(f"      - {field_desc} ({field_name})")
            
            # حساب النسبة
            percentage = (displayed_new_fields / len(new_fields_to_check)) * 100
            print(f"\n📈 نسبة عرض الحقول الجديدة: {percentage:.1f}%")
            
            if percentage == 0:
                print("🚨 المشكلة: صفحة المعاينة لا تعرض أي من الحقول الجديدة!")
                print("💡 الحل: يجب تحديث صفحة المعاينة لتشمل الحقول الجديدة")
            elif percentage < 50:
                print("⚠️  المشكلة: معظم الحقول الجديدة غير معروضة")
            else:
                print("👍 جيد: معظم الحقول الجديدة معروضة")
                
        else:
            print(f"❌ خطأ في الوصول لصفحة المعاينة: {response.status_code}")

if __name__ == "__main__":
    check_view_vs_database()