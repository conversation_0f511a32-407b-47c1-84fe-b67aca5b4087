{% extends "base.html" %}

{% block title %}تسجيل وفاة الموظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}تسجيل وفاة الموظف{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">
            <i class="fas fa-times-circle me-2"></i>تسجيل وفاة الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ الميلاد:</strong> {{ employee.birth_date or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-info">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تسجيل الوفاة -->
<div class="card">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-file-medical me-2"></i>بيانات الوفاة
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="deathForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="death_date" class="form-label">تاريخ الوفاة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="death_date" name="death_date" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="cause" class="form-label">سبب الوفاة <span class="text-danger">*</span></label>
                        <select class="form-select" id="cause" name="cause" required>
                            <option value="">اختر سبب الوفاة</option>
                            <option value="عادية">وفاة عادية</option>
                            <option value="حادث عمل">حادث عمل</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="certificate_number" class="form-label">رقم شهادة الوفاة</label>
                        <input type="text" class="form-control" id="certificate_number" name="certificate_number" 
                               placeholder="أدخل رقم شهادة الوفاة">
                    </div>
                </div>
            </div>
            
            <!-- تحذير -->
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه مهم:</strong> تسجيل الوفاة سيؤدي إلى تغيير حالة الموظف نهائياً إلى "متوفي" ولا يمكن التراجع عن هذا الإجراء.
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-danger" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل الوفاة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal تأكيد التسجيل -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد تسجيل الوفاة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من تسجيل وفاة الموظف:</p>
                <div class="text-center p-3 border rounded bg-light">
                    <h6><strong>{{ employee.first_name }} {{ employee.last_name }}</strong></h6>
                    <p class="mb-0">رقم التسجيل: {{ employee.registration_number }}</p>
                </div>
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه وسيؤثر على جميع السجلات المرتبطة بالموظف.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>تأكيد التسجيل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('deathForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const deathDate = document.getElementById('death_date').value;
    const cause = document.getElementById('cause').value;
    
    if (!deathDate || !cause) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من أن تاريخ الوفاة ليس في المستقبل
    const today = new Date();
    const selectedDate = new Date(deathDate);
    
    if (selectedDate > today) {
        alert('تاريخ الوفاة لا يمكن أن يكون في المستقبل');
        return;
    }
    
    // إظهار مودال التأكيد
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
});

document.getElementById('confirmSubmit').addEventListener('click', function() {
    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
    
    // تعطيل الزر وإظهار مؤشر التحميل
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
    
    // إرسال النموذج
    document.getElementById('deathForm').submit();
});

// تحديد تاريخ اليوم كحد أقصى
document.getElementById('death_date').max = new Date().toISOString().split('T')[0];
</script>
{% endblock %}