{% extends "base.html" %}

{% block title %}الإعدادات - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إعدادات النظام{% endblock %}

{% block content %}
<style>
.settings-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
    height: 100%;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.settings-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 0.35rem 0.35rem 0 0;
    text-align: center;
}

.settings-card-body {
    padding: 2rem;
    text-align: center;
}

.settings-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #5a5c69;
}

.settings-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.settings-description {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.settings-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.settings-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.institution-card .settings-card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.institution-card .settings-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.employee-status-card .settings-card-header {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.employee-status-card .settings-btn {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.general-settings-card .settings-card-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
}

.general-settings-card .settings-btn {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
}

.stats-badge {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    margin-left: 0.5rem;
}
</style>

<!-- مقدمة الصفحة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info border-0" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle fa-2x text-primary me-3"></i>
                <div>
                    <h5 class="alert-heading mb-1">مرحباً بك في إعدادات النظام</h5>
                    <p class="mb-0">من هنا يمكنك إدارة جميع إعدادات النظام وتخصيصه حسب احتياجات مؤسستك</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مربعات الإعدادات -->
<div class="row g-4">
    <!-- بيانات المؤسسة -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card institution-card">
            <div class="settings-card-header">
                <i class="fas fa-building fa-2x mb-2"></i>
                <h4 class="mb-0">بيانات المؤسسة</h4>
                <span class="stats-badge">معلومات أساسية</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-building-columns"></i>
                </div>
                <h5 class="settings-title">إعدادات المؤسسة</h5>
                <p class="settings-description">
                    إدارة بيانات المؤسسة الأساسية مثل الاسم، العنوان، أرقام الهواتف، والشعار الرسمي
                </p>
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        اسم المؤسسة والعنوان
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        أرقام الهواتف والفاكس
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        الشعار والهوية البصرية
                    </small>
                </div>
                <button class="settings-btn" onclick="openInstitutionModal()">
                    <i class="fas fa-edit me-2"></i>إدارة البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- حالات الموظفين -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card employee-status-card">
            <div class="settings-card-header">
                <i class="fas fa-user-check fa-2x mb-2"></i>
                <h4 class="mb-0">حالات الموظفين</h4>
                <span class="stats-badge" id="statusCount">جاري التحميل...</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <h5 class="settings-title">إدارة حالات الموظفين</h5>
                <p class="settings-description">
                    إضافة وتعديل حالات الموظفين مع تخصيص الألوان والأيقونات لكل حالة
                </p>
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        إضافة حالات جديدة
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        تخصيص الألوان والأيقونات
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        تفعيل وإلغاء تفعيل الحالات
                    </small>
                </div>
                <button class="settings-btn" onclick="openEmployeeStatusesModal()">
                    <i class="fas fa-cog me-2"></i>إدارة الحالات
                </button>
            </div>
        </div>
    </div>

    <!-- الإعدادات العامة -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card general-settings-card">
            <div class="settings-card-header">
                <i class="fas fa-sliders-h fa-2x mb-2"></i>
                <h4 class="mb-0">الإعدادات العامة</h4>
                <span class="stats-badge">تخصيص النظام</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h5 class="settings-title">إعدادات النظام العامة</h5>
                <p class="settings-description">
                    تخصيص إعدادات النظام العامة مثل اللغة، المنطقة الزمنية، وإعدادات الأمان
                </p>
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        إعدادات اللغة والمنطقة
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        إعدادات الأمان والخصوصية
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        إعدادات النسخ الاحتياطي
                    </small>
                </div>
                <button class="settings-btn" onclick="openGeneralSettingsModal()">
                    <i class="fas fa-wrench me-2"></i>إدارة الإعدادات
                </button>
            </div>
        </div>
    </div>

    <!-- البيانات المرجعية -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card">
            <div class="settings-card-header" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
                <i class="fas fa-database fa-2x mb-2"></i>
                <h4 class="mb-0">البيانات المرجعية</h4>
                <span class="stats-badge">أسلاك ورتب</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-list-alt"></i>
                </div>
                <h5 class="settings-title">إدارة البيانات المرجعية</h5>
                <p class="settings-description">
                    إدارة الأسلاك، الرتب، المديريات، المصالح، والتقسيم الإداري
                </p>
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        الأسلاك والرتب
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        المديريات والمصالح
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        التقسيم الإداري
                    </small>
                </div>
                <button class="settings-btn" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);" onclick="openReferenceDataModal()">
                    <i class="fas fa-cogs me-2"></i>إدارة البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- أدوات النظام -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card">
            <div class="settings-card-header" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                <i class="fas fa-tools fa-2x mb-2"></i>
                <h4 class="mb-0">أدوات النظام</h4>
                <span class="stats-badge">صيانة وإدارة</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-wrench"></i>
                </div>
                <h5 class="settings-title">أدوات الصيانة</h5>
                <p class="settings-description">
                    أدوات صيانة قاعدة البيانات والنسخ الاحتياطي وتنظيف النظام
                </p>
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        نسخ احتياطي للبيانات
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        تنظيف الملفات المؤقتة
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        فحص سلامة البيانات
                    </small>
                </div>
                <button class="settings-btn" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);" onclick="openSystemToolsModal()">
                    <i class="fas fa-tools me-2"></i>أدوات الصيانة
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات النظام -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card">
            <div class="settings-card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                <h4 class="mb-0">إحصائيات النظام</h4>
                <span class="stats-badge">معلومات سريعة</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-analytics"></i>
                </div>
                <h5 class="settings-title">إحصائيات سريعة</h5>
                <p class="settings-description">
                    عرض إحصائيات سريعة حول استخدام النظام وحالة البيانات
                </p>
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-primary mb-1" id="totalEmployees">0</h6>
                            <small class="text-muted">إجمالي الموظفين</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-success mb-1" id="activeEmployees">0</h6>
                            <small class="text-muted">الموظفين النشطين</small>
                        </div>
                    </div>
                </div>
                <button class="settings-btn" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);" onclick="showSystemStats()">
                    <i class="fas fa-chart-line me-2"></i>عرض التفاصيل
                </button>
            </div>
        </div>
    </div>

    <!-- المساعدة والدعم -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card">
            <div class="settings-card-header" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <i class="fas fa-question-circle fa-2x mb-2"></i>
                <h4 class="mb-0">المساعدة والدعم</h4>
                <span class="stats-badge">دليل المستخدم</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-life-ring"></i>
                </div>
                <h5 class="settings-title">المساعدة والدعم</h5>
                <p class="settings-description">
                    دليل المستخدم، الأسئلة الشائعة، ومعلومات الاتصال بالدعم الفني
                </p>
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        دليل المستخدم التفاعلي
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        الأسئلة الشائعة
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        معلومات الاتصال
                    </small>
                </div>
                <button class="settings-btn" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);" onclick="openHelpModal()">
                    <i class="fas fa-book me-2"></i>دليل المساعدة
                </button>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="col-lg-4 col-md-6">
        <div class="settings-card">
            <div class="settings-card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <h4 class="mb-0">معلومات النظام</h4>
                <span class="stats-badge">الإصدار 1.0</span>
            </div>
            <div class="settings-card-body">
                <div class="settings-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h5 class="settings-title">معلومات النظام</h5>
                <p class="settings-description">
                    معلومات حول إصدار النظام، المطور، والترخيص
                </p>
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        الإصدار: 1.0.0
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        تاريخ الإصدار: 2025-01-20
                    </small>
                    <small class="text-muted d-block">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        مرخص للجمارك الجزائرية
                    </small>
                </div>
                <button class="settings-btn" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);" onclick="showSystemInfo()">
                    <i class="fas fa-info me-2"></i>عرض المعلومات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة بيانات المؤسسة -->
<div class="modal fade" id="institutionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                <h5 class="modal-title">
                    <i class="fas fa-building me-2"></i>تعديل بيانات المؤسسة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="institutionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المؤسسة</label>
                                <input type="text" class="form-control" value="الجمارك الجزائرية">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" value="021-XX-XX-XX">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الفاكس</label>
                                <input type="text" class="form-control" value="021-XX-XX-XX">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عنوان الموقع</label>
                                <input type="url" class="form-control" value="https://www.customs.dz">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عنوان المراسلة</label>
                                <textarea class="form-control" rows="3">الجزائر العاصمة، الجزائر</textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveInstitutionData()">
                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة حالات الموظفين -->
<div class="modal fade" id="employeeStatusesModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); color: white;">
                <h5 class="modal-title">
                    <i class="fas fa-user-check me-2"></i>إدارة حالات الموظفين
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>قائمة حالات الموظفين</h6>
                    <button class="btn btn-primary" onclick="openAddStatusModal()">
                        <i class="fas fa-plus me-2"></i>إضافة حالة جديدة
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" id="statusesTable">
                        <thead class="table-light">
                            <tr>
                                <th>الاسم</th>
                                <th>اللون</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="statusesTableBody">
                            <!-- سيتم تحميل البيانات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل حالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">إضافة حالة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    <input type="hidden" id="statusId">
                    <div class="mb-3">
                        <label class="form-label">اسم الحالة</label>
                        <input type="text" class="form-control" id="statusName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اللون</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color me-2" id="statusColor" value="#28a745">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="setStatusColor('#28a745')">أخضر</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setStatusColor('#007bff')">أزرق</button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="setStatusColor('#ffc107')">أصفر</button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="setStatusColor('#dc3545')">أحمر</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setStatusColor('#6c757d')">رمادي</button>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="statusDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="statusActive" checked>
                            <label class="form-check-label" for="statusActive">
                                حالة نشطة
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveStatus()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadEmployeeStatusesCount();
    loadSystemStats();
});

// تحميل إحصائيات النظام
function loadSystemStats() {
    fetch('/api/system_stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalEmployees').textContent = data.total_employees || 0;
            document.getElementById('activeEmployees').textContent = data.active_employees || 0;
        })
        .catch(error => {
            console.error('خطأ في تحميل الإحصائيات:', error);
        });
}

// تحميل عدد حالات الموظفين
function loadEmployeeStatusesCount() {
    fetch('/api/employee_statuses')
        .then(response => response.json())
        .then(data => {
            document.getElementById('statusCount').textContent = `${data.length} حالة`;
        })
        .catch(error => {
            console.error('خطأ في تحميل عدد الحالات:', error);
            document.getElementById('statusCount').textContent = 'غير متاح';
        });
}

// فتح نافذة بيانات المؤسسة
function openInstitutionModal() {
    const modal = new bootstrap.Modal(document.getElementById('institutionModal'));
    modal.show();
}

// حفظ بيانات المؤسسة
function saveInstitutionData() {
    alert('تم حفظ بيانات المؤسسة بنجاح!');
    bootstrap.Modal.getInstance(document.getElementById('institutionModal')).hide();
}

// فتح نافذة حالات الموظفين
function openEmployeeStatusesModal() {
    const modal = new bootstrap.Modal(document.getElementById('employeeStatusesModal'));
    modal.show();
    loadEmployeeStatuses();
}

// تحميل حالات الموظفين
function loadEmployeeStatuses() {
    fetch('/api/employee_statuses')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('statusesTableBody');
            tbody.innerHTML = '';
            
            data.forEach(status => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${status.name}</strong></td>
                    <td>
                        <span class="badge" style="background-color: ${status.color}; color: white;">
                            ${status.color}
                        </span>
                    </td>
                    <td>${status.description || 'لا يوجد وصف'}</td>
                    <td>
                        <span class="badge ${status.is_active ? 'bg-success' : 'bg-secondary'}">
                            ${status.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editStatus(${status.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteStatus(${status.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الحالات:', error);
        });
}

// فتح نافذة إضافة حالة
function openAddStatusModal() {
    document.getElementById('statusModalTitle').textContent = 'إضافة حالة جديدة';
    document.getElementById('statusForm').reset();
    document.getElementById('statusId').value = '';
    document.getElementById('statusColor').value = '#28a745';
    document.getElementById('statusActive').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

// تعديل حالة
function editStatus(id) {
    fetch(`/api/employee_statuses/${id}`)
        .then(response => response.json())
        .then(status => {
            document.getElementById('statusModalTitle').textContent = 'تعديل الحالة';
            document.getElementById('statusId').value = status.id;
            document.getElementById('statusName').value = status.name;
            document.getElementById('statusColor').value = status.color;
            document.getElementById('statusDescription').value = status.description || '';
            document.getElementById('statusActive').checked = status.is_active;
            
            const modal = new bootstrap.Modal(document.getElementById('statusModal'));
            modal.show();
        })
        .catch(error => {
            console.error('خطأ في تحميل بيانات الحالة:', error);
        });
}

// حفظ الحالة
function saveStatus() {
    const id = document.getElementById('statusId').value;
    const data = {
        name: document.getElementById('statusName').value,
        color: document.getElementById('statusColor').value,
        description: document.getElementById('statusDescription').value,
        is_active: document.getElementById('statusActive').checked
    };
    
    const url = id ? `/api/employee_statuses/${id}` : '/api/employee_statuses';
    const method = id ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            bootstrap.Modal.getInstance(document.getElementById('statusModal')).hide();
            loadEmployeeStatuses();
            loadEmployeeStatusesCount();
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('خطأ في حفظ الحالة:', error);
        alert('حدث خطأ أثناء حفظ الحالة');
    });
}

// حذف حالة
function deleteStatus(id) {
    if (confirm('هل أنت متأكد من حذف هذه الحالة؟')) {
        fetch(`/api/employee_statuses/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert(result.message);
                loadEmployeeStatuses();
                loadEmployeeStatusesCount();
            } else {
                alert('خطأ: ' + result.message);
            }
        })
        .catch(error => {
            console.error('خطأ في حذف الحالة:', error);
            alert('حدث خطأ أثناء حذف الحالة');
        });
    }
}

// تعيين لون الحالة
function setStatusColor(color) {
    document.getElementById('statusColor').value = color;
}

// فتح نافذة الإعدادات العامة
function openGeneralSettingsModal() {
    alert('سيتم إضافة هذه الميزة قريباً');
}

// فتح نافذة البيانات المرجعية
function openReferenceDataModal() {
    // إنشاء نافذة منبثقة للبيانات المرجعية
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white;">
                    <h5 class="modal-title">
                        <i class="fas fa-database me-2"></i>إدارة البيانات المرجعية
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-users-cog fa-3x text-primary mb-3"></i>
                                    <h5>الأسلاك والرتب</h5>
                                    <p class="text-muted">إدارة أسلاك الموظفين ورتبهم</p>
                                    <a href="/reference_data/corps_ranks" class="btn btn-primary">
                                        <i class="fas fa-cog me-2"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-building fa-3x text-success mb-3"></i>
                                    <h5>المديريات والمصالح</h5>
                                    <p class="text-muted">إدارة الهيكل التنظيمي</p>
                                    <a href="/reference_data/services" class="btn btn-success">
                                        <i class="fas fa-building me-2"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-briefcase fa-3x text-warning mb-3"></i>
                                    <h5>المناصب والوظائف</h5>
                                    <p class="text-muted">إدارة مناصب العمل</p>
                                    <button class="btn btn-warning" onclick="alert('سيتم إضافة هذه الميزة قريباً')">
                                        <i class="fas fa-briefcase me-2"></i>إدارة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-map-marked-alt fa-3x text-info mb-3"></i>
                                    <h5>التقسيم الإداري</h5>
                                    <p class="text-muted">الولايات والبلديات</p>
                                    <a href="/reference_data/locations" class="btn btn-info">
                                        <i class="fas fa-map me-2"></i>إدارة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    // حذف النافذة عند الإغلاق
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// فتح نافذة أدوات النظام
function openSystemToolsModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white;">
                    <h5 class="modal-title">
                        <i class="fas fa-tools me-2"></i>أدوات النظام
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="d-grid gap-3">
                        <button class="btn btn-outline-primary" onclick="createBackup()">
                            <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                        </button>
                        <button class="btn btn-outline-warning" onclick="cleanTempFiles()">
                            <i class="fas fa-broom me-2"></i>تنظيف الملفات المؤقتة
                        </button>
                        <button class="btn btn-outline-info" onclick="checkDataIntegrity()">
                            <i class="fas fa-check-circle me-2"></i>فحص سلامة البيانات
                        </button>
                        <button class="btn btn-outline-success" onclick="optimizeDatabase()">
                            <i class="fas fa-database me-2"></i>تحسين قاعدة البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// عرض إحصائيات النظام
function showSystemStats() {
    fetch('/api/system_stats')
        .then(response => response.json())
        .then(data => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-bar me-2"></i>إحصائيات النظام التفصيلية
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                            <h3 class="text-primary">${data.total_employees}</h3>
                                            <p class="text-muted">إجمالي الموظفين</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                            <h3 class="text-success">${data.active_employees}</h3>
                                            <p class="text-muted">الموظفين النشطين</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-calendar-alt fa-2x text-warning mb-2"></i>
                                            <h3 class="text-warning">${data.total_leaves}</h3>
                                            <p class="text-muted">إجمالي العطل</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-arrow-up fa-2x text-info mb-2"></i>
                                            <h3 class="text-info">${data.total_promotions}</h3>
                                            <p class="text-muted">إجمالي الترقيات</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
            
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الإحصائيات:', error);
            alert('حدث خطأ في تحميل الإحصائيات');
        });
}

// فتح نافذة المساعدة
function openHelpModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white;">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle me-2"></i>المساعدة والدعم
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="accordion" id="helpAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#help1">
                                    <i class="fas fa-user-plus me-2"></i>كيفية إضافة موظف جديد
                                </button>
                            </h2>
                            <div id="help1" class="accordion-collapse collapse show" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>انتقل إلى قسم "الموظفين" من القائمة الجانبية</li>
                                        <li>اضغط على زر "إضافة موظف جديد"</li>
                                        <li>املأ جميع البيانات المطلوبة</li>
                                        <li>اضغط على "حفظ" لإتمام العملية</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help2">
                                    <i class="fas fa-calendar me-2"></i>كيفية إدارة العطل
                                </button>
                            </h2>
                            <div id="help2" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p>يمكنك إدارة العطل من خلال:</p>
                                    <ul>
                                        <li>قسم "العطل السنوية" للعطل العادية</li>
                                        <li>قسم "العطل المرضية" للعطل الصحية</li>
                                        <li>قسم "العطل الأخرى" للعطل الاستثنائية</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help3">
                                    <i class="fas fa-phone me-2"></i>معلومات الاتصال
                                </button>
                            </h2>
                            <div id="help3" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>للدعم الفني:</strong></p>
                                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                                    <p><i class="fas fa-phone me-2"></i>021-XX-XX-XX</p>
                                    <p><strong>ساعات العمل:</strong> من 8:00 إلى 16:00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// عرض معلومات النظام
function showSystemInfo() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white;">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>معلومات النظام
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>اسم النظام:</strong></td>
                            <td>نظام إدارة موظفي الجمارك الجزائرية</td>
                        </tr>
                        <tr>
                            <td><strong>الإصدار:</strong></td>
                            <td>1.0.0</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإصدار:</strong></td>
                            <td>2025-01-20</td>
                        </tr>
                        <tr>
                            <td><strong>المطور:</strong></td>
                            <td>فريق تطوير الجمارك الجزائرية</td>
                        </tr>
                        <tr>
                            <td><strong>الترخيص:</strong></td>
                            <td>مرخص للجمارك الجزائرية</td>
                        </tr>
                        <tr>
                            <td><strong>حالة النظام:</strong></td>
                            <td><span class="badge bg-success">يعمل بشكل طبيعي</span></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// دوال أدوات النظام
function createBackup() {
    alert('جاري إنشاء نسخة احتياطية...\nسيتم إضافة هذه الميزة قريباً');
}

function cleanTempFiles() {
    alert('جاري تنظيف الملفات المؤقتة...\nسيتم إضافة هذه الميزة قريباً');
}

function checkDataIntegrity() {
    alert('جاري فحص سلامة البيانات...\nسيتم إضافة هذه الميزة قريباً');
}

function optimizeDatabase() {
    alert('جاري تحسين قاعدة البيانات...\nسيتم إضافة هذه الميزة قريباً');
}
</script>
{% endblock %}
