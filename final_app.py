from flask import Flask
import sqlite3

app = Flask(__name__)

def get_db():
    conn = sqlite3.connect('employees.db')
    conn.execute('''CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY,
        name TEXT,
        status TEXT DEFAULT 'نشط'
    )''')
    conn.commit()
    return conn

@app.route('/')
def home():
    return '''
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>نظام الموظفين</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    </head>
    <body>
        <div class="container mt-5">
            <h1 class="text-center mb-4">نظام إدارة الموظفين</h1>
            <div class="text-center">
                <a href="/statuses" class="btn btn-primary btn-lg">حالات الموظفين</a>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/statuses')
def statuses():
    conn = get_db()
    
    # حساب الأعداد
    active = conn.execute("SELECT COUNT(*) FROM employees WHERE status='نشط'").fetchone()[0]
    assigned = conn.execute("SELECT COUNT(*) FROM employees WHERE status='منتدب'").fetchone()[0]
    study = conn.execute("SELECT COUNT(*) FROM employees WHERE status='في دراسة'").fetchone()[0]
    
    conn.close()
    
    return f'''
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>حالات الموظفين</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body {{
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                min-height: 100vh;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }}
            .status-card {{
                background: white;
                border-radius: 15px;
                padding: 40px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
                margin-bottom: 20px;
                border: 2px solid transparent;
            }}
            .status-card:hover {{
                transform: translateY(-10px);
                box-shadow: 0 20px 50px rgba(0,0,0,0.2);
                border-color: #007bff;
            }}
            .status-icon {{
                font-size: 4rem;
                margin-bottom: 20px;
            }}
            .status-name {{
                font-size: 1.8rem;
                font-weight: bold;
                margin-bottom: 15px;
            }}
            .status-number {{
                font-size: 3.5rem;
                font-weight: bold;
                margin-bottom: 10px;
            }}
            .section-header {{
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 30px;
            }}
            .section-header.warning {{
                background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
                color: #333;
            }}
            .main-header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 40px;
                border-radius: 15px;
                text-align: center;
                margin-bottom: 40px;
            }}
        </style>
    </head>
    <body>
        <div class="container mt-4">
            <!-- رأس الصفحة -->
            <div class="main-header">
                <h1><i class="fas fa-users-cog me-3"></i>إدارة حالات الموظفين</h1>
                <p class="mb-0">عرض إحصائيات حالات الموظفين مصنفة حسب دخولها في التعداد الحقيقي</p>
            </div>
            
            <!-- الحالات النشطة -->
            <div class="section-header">
                <h3><i class="fas fa-users me-2"></i>الحالات التي تدخل في التعداد الحقيقي للموظفين</h3>
                <small>هذه الحالات تحسب ضمن العدد الفعلي للموظفين العاملين</small>
            </div>
            
            <div class="row mb-5">
                <div class="col-md-4">
                    <div class="status-card">
                        <i class="fas fa-check-circle status-icon text-success"></i>
                        <div class="status-name text-success">نشط</div>
                        <div class="status-number text-success">{active}</div>
                        <div class="text-muted">موظف نشط</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="status-card">
                        <i class="fas fa-user-tie status-icon text-info"></i>
                        <div class="status-name text-info">منتدب</div>
                        <div class="status-number text-info">{assigned}</div>
                        <div class="text-muted">موظف منتدب</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="status-card">
                        <i class="fas fa-graduation-cap status-icon text-primary"></i>
                        <div class="status-name text-primary">في دراسة/تكوين</div>
                        <div class="status-number text-primary">{study}</div>
                        <div class="text-muted">موظف في دراسة</div>
                    </div>
                </div>
            </div>
            
            <!-- الحالات غير النشطة -->
            <div class="section-header warning">
                <h3><i class="fas fa-exclamation-triangle me-2"></i>الحالات التي لا تدخل في التعداد الحقيقي للموظفين</h3>
                <small>هذه الحالات لا تحسب ضمن العدد الفعلي للموظفين العاملين</small>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="status-card">
                        <i class="fas fa-pause status-icon text-secondary"></i>
                        <div class="status-name text-secondary">مستودع</div>
                        <div class="status-number text-secondary">0</div>
                        <div class="text-muted">موظف مستودع</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card">
                        <i class="fas fa-stop-circle status-icon text-danger"></i>
                        <div class="status-name text-danger">موقوف</div>
                        <div class="status-number text-danger">0</div>
                        <div class="text-muted">موظف موقوف</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card">
                        <i class="fas fa-calendar-times status-icon text-warning"></i>
                        <div class="status-name text-warning">في عطلة طويلة</div>
                        <div class="status-number text-warning">0</div>
                        <div class="text-muted">موظف في عطلة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card">
                        <i class="fas fa-user-clock status-icon text-info"></i>
                        <div class="status-name text-info">متقاعد</div>
                        <div class="status-number text-info">0</div>
                        <div class="text-muted">موظف متقاعد</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card">
                        <i class="fas fa-sign-out-alt status-icon text-secondary"></i>
                        <div class="status-name text-secondary">مستقيل</div>
                        <div class="status-number text-secondary">0</div>
                        <div class="text-muted">موظف مستقيل</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card">
                        <i class="fas fa-exchange-alt status-icon text-primary"></i>
                        <div class="status-name text-primary">محول خارجياً</div>
                        <div class="status-number text-primary">0</div>
                        <div class="text-muted">موظف محول</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="status-card">
                        <i class="fas fa-heart status-icon text-dark"></i>
                        <div class="status-name text-dark">متوفى</div>
                        <div class="status-number text-dark">0</div>
                        <div class="text-muted">موظف متوفى</div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <a href="/" class="btn btn-secondary btn-lg">العودة للرئيسية</a>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 تشغيل النظام...")
    print("📊 صفحة الحالات: http://localhost:5000/statuses")
    app.run(debug=True, port=5000)
