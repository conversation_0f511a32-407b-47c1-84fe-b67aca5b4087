#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص قاعدة البيانات
"""

import sqlite3
import os

def check_database():
    """فحص قاعدة البيانات والجداول"""
    
    if not os.path.exists('customs_employees.db'):
        print("❌ قاعدة البيانات غير موجودة")
        return
    
    print("✅ قاعدة البيانات موجودة")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("📋 الجداول الموجودة:")
        print("-" * 30)
        
        for table in tables:
            table_name = table[0]
            print(f"📊 {table_name}")
            
            # عدد السجلات في كل جدول
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   📈 عدد السجلات: {count}")
            except:
                print(f"   ❌ خطأ في قراءة الجدول")
            
            print()
        
        # فحص جدول الموظفين بالتفصيل
        print("=" * 50)
        print("🔍 تفاصيل جدول الموظفين:")
        print("-" * 30)
        
        try:
            cursor.execute("PRAGMA table_info(employees)")
            columns = cursor.fetchall()
            
            print("📋 الأعمدة:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
            
            print()
            
            # عينة من البيانات
            cursor.execute("SELECT * FROM employees LIMIT 3")
            employees = cursor.fetchall()
            
            if employees:
                print("📊 عينة من البيانات:")
                for emp in employees:
                    print(f"   - {emp}")
            else:
                print("📭 لا توجد بيانات موظفين")
                
        except Exception as e:
            print(f"❌ خطأ في فحص جدول الموظفين: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")

if __name__ == "__main__":
    check_database()
