{% extends "base.html" %}

{% block title %}تقاعد الموظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}تقاعد الموظف{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-dark text-white">
        <h5 class="mb-0">
            <i class="fas fa-user-clock me-2"></i>تقاعد الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                        <p><strong>تاريخ الميلاد:</strong> {{ employee.birth_date or 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ التوظيف:</strong> {{ employee.hiring_date or 'غير محدد' }}</p>
                        <p><strong>الرتبة:</strong> {{ employee.rank or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-success">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
                
                <!-- حساب سنوات الخدمة -->
                {% if employee.hiring_date %}
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>سنوات الخدمة:</strong> 
                    <span id="serviceYears">جاري الحساب...</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نموذج التقاعد -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>بيانات التقاعد
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="retirementForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="retirement_date" class="form-label">تاريخ التقاعد <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="retirement_date" name="retirement_date" required>
                        <div class="form-text">آخر يوم عمل للموظف</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_number" class="form-label">رقم مقرر التقاعد</label>
                        <input type="text" class="form-control" id="decision_number" name="decision_number" 
                               placeholder="أدخل رقم مقرر التقاعد">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_date" class="form-label">تاريخ مقرر التقاعد</label>
                        <input type="date" class="form-control" id="decision_date" name="decision_date">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="retirement_card_number" class="form-label">رقم بطاقة التقاعد</label>
                        <input type="text" class="form-control" id="retirement_card_number" name="retirement_card_number" 
                               placeholder="أدخل رقم بطاقة التقاعد">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="card_issue_date" class="form-label">تاريخ صدور البطاقة</label>
                        <input type="date" class="form-control" id="card_issue_date" name="card_issue_date">
                    </div>
                </div>
            </div>
            
            <!-- معلومات التقاعد -->
            <div class="card border-success mb-3">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">معلومات التقاعد</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-success" id="totalServiceYears">--</h5>
                                <small class="text-muted">سنوات الخدمة الإجمالية</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-info" id="currentAge">--</h5>
                                <small class="text-muted">العمر الحالي</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-warning" id="retirementAge">--</h5>
                                <small class="text-muted">عمر التقاعد</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تحذير -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه مهم:</strong> تقاعد الموظف سيؤدي إلى إنهاء جميع الحقوق والواجبات الوظيفية نهائياً.
            </div>
            
            <div class="alert alert-success">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> سيحتفظ الموظف بحقوقه التقاعدية وفقاً للقوانين المعمول بها.
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-dark" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل التقاعد
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal تأكيد التقاعد -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-dark text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>تأكيد تقاعد الموظف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من تقاعد الموظف:</p>
                <div class="text-center p-3 border rounded bg-light">
                    <h6><strong>{{ employee.first_name }} {{ employee.last_name }}</strong></h6>
                    <p class="mb-0">رقم التسجيل: {{ employee.registration_number }}</p>
                </div>
                <div class="mt-3">
                    <p><strong>تاريخ التقاعد:</strong> <span id="confirmRetirementDate"></span></p>
                    <p><strong>سنوات الخدمة:</strong> <span id="confirmServiceYears"></span></p>
                    <p><strong>عمر التقاعد:</strong> <span id="confirmRetirementAge"></span></p>
                </div>
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    هذا الإجراء نهائي وسيؤدي إلى إنهاء الخدمة الوظيفية للموظف.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-dark" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>تأكيد التقاعد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// حساب سنوات الخدمة والعمر
function calculateServiceInfo() {
    const hiringDate = '{{ employee.hiring_date }}';
    const birthDate = '{{ employee.birth_date }}';
    const retirementDate = document.getElementById('retirement_date').value;
    
    if (hiringDate) {
        const hiring = new Date(hiringDate);
        const today = new Date();
        const retirement = retirementDate ? new Date(retirementDate) : today;
        
        // حساب سنوات الخدمة
        const serviceYears = Math.floor((retirement - hiring) / (365.25 * 24 * 60 * 60 * 1000));
        document.getElementById('serviceYears').textContent = serviceYears + ' سنة';
        document.getElementById('totalServiceYears').textContent = serviceYears + ' سنة';
    }
    
    if (birthDate) {
        const birth = new Date(birthDate);
        const today = new Date();
        const retirement = retirementDate ? new Date(retirementDate) : today;
        
        // حساب العمر الحالي
        const currentAge = Math.floor((today - birth) / (365.25 * 24 * 60 * 60 * 1000));
        document.getElementById('currentAge').textContent = currentAge + ' سنة';
        
        // حساب عمر التقاعد
        if (retirementDate) {
            const retirementAge = Math.floor((retirement - birth) / (365.25 * 24 * 60 * 60 * 1000));
            document.getElementById('retirementAge').textContent = retirementAge + ' سنة';
        }
    }
}

// حساب المعلومات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', calculateServiceInfo);

// إعادة حساب المعلومات عند تغيير تاريخ التقاعد
document.getElementById('retirement_date').addEventListener('change', calculateServiceInfo);

document.getElementById('retirementForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const retirementDate = document.getElementById('retirement_date').value;
    
    if (!retirementDate) {
        alert('يرجى تحديد تاريخ التقاعد');
        return;
    }
    
    // التحقق من أن تاريخ التقاعد منطقي
    const today = new Date();
    const retirement = new Date(retirementDate);
    const diffTime = Math.abs(retirement - today);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (retirement > today && diffDays > 365) {
        if (!confirm('تاريخ التقاعد بعيد في المستقبل. هل أنت متأكد من المتابعة؟')) {
            return;
        }
    }
    
    // حساب المعلومات للتأكيد
    const hiringDate = '{{ employee.hiring_date }}';
    const birthDate = '{{ employee.birth_date }}';
    
    let serviceYears = '--';
    let retirementAge = '--';
    
    if (hiringDate) {
        const hiring = new Date(hiringDate);
        serviceYears = Math.floor((retirement - hiring) / (365.25 * 24 * 60 * 60 * 1000)) + ' سنة';
    }
    
    if (birthDate) {
        const birth = new Date(birthDate);
        retirementAge = Math.floor((retirement - birth) / (365.25 * 24 * 60 * 60 * 1000)) + ' سنة';
    }
    
    // تحديث بيانات التأكيد
    document.getElementById('confirmRetirementDate').textContent = retirementDate;
    document.getElementById('confirmServiceYears').textContent = serviceYears;
    document.getElementById('confirmRetirementAge').textContent = retirementAge;
    
    // إظهار مودال التأكيد
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
});

document.getElementById('confirmSubmit').addEventListener('click', function() {
    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
    
    // تعطيل الزر وإظهار مؤشر التحميل
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
    
    // إرسال النموذج
    document.getElementById('retirementForm').submit();
});

// تحديد تاريخ اليوم كحد أدنى لتاريخ التقاعد (يمكن أن يكون في الماضي القريب)
const today = new Date();
const sixMonthsAgo = new Date(today);
sixMonthsAgo.setMonth(today.getMonth() - 6);

document.getElementById('retirement_date').min = sixMonthsAgo.toISOString().split('T')[0];
document.getElementById('decision_date').max = today.toISOString().split('T')[0];
document.getElementById('card_issue_date').max = today.toISOString().split('T')[0];
</script>
{% endblock %}