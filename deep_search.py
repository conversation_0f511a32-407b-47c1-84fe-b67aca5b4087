#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البحث العميق عن مراجع special_status
Deep search for special_status references
"""

import os
import sys

def search_in_file(file_path, search_term):
    """البحث في ملف محدد"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            if search_term in content:
                lines = content.split('\n')
                matches = []
                for i, line in enumerate(lines, 1):
                    if search_term in line:
                        matches.append({
                            'line_number': i,
                            'content': line.strip()
                        })
                return matches
    except Exception as e:
        print(f"خطأ في قراءة {file_path}: {e}")
    return []

def deep_search():
    """البحث العميق"""
    print("🔍 البحث العميق عن special_status...")
    
    search_terms = [
        'special_status',
        'special_status.index',
        'url_for(\'special_status',
        'url_for("special_status'
    ]
    
    # البحث في جميع الملفات
    all_files = []
    
    # ملفات Python
    for root, dirs, files in os.walk('.'):
        # تجاهل مجلدات معينة
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith(('.py', '.html', '.js', '.css')):
                file_path = os.path.join(root, file)
                all_files.append(file_path)
    
    total_matches = 0
    
    for search_term in search_terms:
        print(f"\n🔍 البحث عن: {search_term}")
        term_matches = 0
        
        for file_path in all_files:
            matches = search_in_file(file_path, search_term)
            if matches:
                print(f"\n📄 الملف: {file_path}")
                for match in matches:
                    print(f"   السطر {match['line_number']}: {match['content']}")
                    term_matches += 1
        
        if term_matches == 0:
            print(f"   ✅ لم يتم العثور على {search_term}")
        else:
            print(f"   🔍 تم العثور على {term_matches} مطابقة")
        
        total_matches += term_matches
    
    print(f"\n📊 إجمالي المطابقات: {total_matches}")
    
    if total_matches == 0:
        print("\n✅ لم يتم العثور على أي مراجع لـ special_status")
        print("✅ المشكلة قد تكون في مكان آخر")
        
        # اقتراحات للحل
        print("\n💡 اقتراحات للحل:")
        print("1. إعادة تشغيل الخادم")
        print("2. مسح ملفات التخزين المؤقت")
        print("3. التحقق من متغيرات البيئة")
        print("4. إعادة تثبيت Flask")
    
    return total_matches

if __name__ == '__main__':
    print("=" * 60)
    print("🔍 أداة البحث العميق عن special_status")
    print("🔍 Deep Search Tool for special_status")
    print("=" * 60)
    
    matches = deep_search()
    
    print("\n" + "=" * 60)
    print("🎉 انتهى البحث")
    print("🎉 Search completed")
    print("=" * 60)
