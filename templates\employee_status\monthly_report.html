{% extends "base.html" %}

{% block title %}التقرير الشهري - {{ report.period.month_name }} {{ report.period.year }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt"></i>
                        التقرير الشهري - {{ report.period.month_name }} {{ report.period.year }}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-success btn-sm" onclick="exportReport('csv')">
                            <i class="fas fa-file-csv"></i> تصدير CSV
                        </button>
                        <button type="button" class="btn btn-info btn-sm" onclick="exportReport('pdf')">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <a href="{{ url_for('employee_status_reports') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الفترة -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> معلومات التقرير</h5>
                                <strong>الفترة:</strong> من {{ report.period.start_date|format_arabic_date }} إلى {{ report.period.end_date|format_arabic_date }}<br>
                                <strong>تاريخ الإنشاء:</strong> {{ moment().format('DD/MM/YYYY HH:mm') }}
                            </div>
                        </div>
                    </div>

                    <!-- الملخص التنفيذي -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-user-minus"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">الاستقالات الجديدة</span>
                                    <span class="info-box-number">{{ report.summary.new_resignations }}</span>
                                    {% if report.comparisons.resignations_change != 0 %}
                                    <div class="progress">
                                        <div class="progress-bar {% if report.comparisons.resignations_change > 0 %}bg-danger{% else %}bg-success{% endif %}" 
                                             style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">
                                        {% if report.comparisons.resignations_change > 0 %}
                                        <i class="fas fa-arrow-up text-danger"></i> +{{ report.comparisons.resignations_change }}
                                        {% else %}
                                        <i class="fas fa-arrow-down text-success"></i> {{ report.comparisons.resignations_change }}
                                        {% endif %}
                                        عن الشهر السابق
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-exchange-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">التحويلات الخارجية</span>
                                    <span class="info-box-number">{{ report.summary.new_external_transfers }}</span>
                                    {% if report.comparisons.transfers_change != 0 %}
                                    <div class="progress">
                                        <div class="progress-bar {% if report.comparisons.transfers_change > 0 %}bg-danger{% else %}bg-success{% endif %}" 
                                             style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">
                                        {% if report.comparisons.transfers_change > 0 %}
                                        <i class="fas fa-arrow-up text-danger"></i> +{{ report.comparisons.transfers_change }}
                                        {% else %}
                                        <i class="fas fa-arrow-down text-success"></i> {{ report.comparisons.transfers_change }}
                                        {% endif %}
                                        عن الشهر السابق
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box bg-secondary">
                                <span class="info-box-icon"><i class="fas fa-user-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">التقاعدات الجديدة</span>
                                    <span class="info-box-number">{{ report.details.new_retirements.total }}</span>
                                    {% if report.comparisons.retirements_change != 0 %}
                                    <div class="progress">
                                        <div class="progress-bar {% if report.comparisons.retirements_change > 0 %}bg-danger{% else %}bg-success{% endif %}" 
                                             style="width: 100%"></div>
                                    </div>
                                    <span class="progress-description">
                                        {% if report.comparisons.retirements_change > 0 %}
                                        <i class="fas fa-arrow-up text-danger"></i> +{{ report.comparisons.retirements_change }}
                                        {% else %}
                                        <i class="fas fa-arrow-down text-success"></i> {{ report.comparisons.retirements_change }}
                                        {% endif %}
                                        عن الشهر السابق
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="info-box bg-dark">
                                <span class="info-box-icon"><i class="fas fa-cross"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">الوفيات</span>
                                    <span class="info-box-number">{{ report.summary.new_deaths }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- العطل طويلة الأمد -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <i class="fas fa-calendar-times"></i>
                                        العطل طويلة الأمد الجديدة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    {% if report.details.new_long_term_leaves.by_type %}
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>نوع العطلة</th>
                                                    <th>العدد</th>
                                                    <th>النسبة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for leave_type in report.details.new_long_term_leaves.by_type %}
                                                <tr>
                                                    <td>{{ leave_type.type }}</td>
                                                    <td>{{ leave_type.count }}</td>
                                                    <td>
                                                        {% set percentage = (leave_type.count / report.details.new_long_term_leaves.total * 100) | round(1) %}
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar bg-info" style="width: {{ percentage }}%"></div>
                                                        </div>
                                                        {{ percentage }}%
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr class="font-weight-bold">
                                                    <td>الإجمالي</td>
                                                    <td>{{ report.details.new_long_term_leaves.total }}</td>
                                                    <td>100%</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    {% else %}
                                    <p class="text-muted">لا توجد عطل طويلة الأمد جديدة في هذا الشهر</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <i class="fas fa-calendar-check"></i>
                                        العطل المنتهية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    {% if report.details.ended_long_term_leaves.by_type %}
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>نوع العطلة</th>
                                                    <th>العدد</th>
                                                    <th>النسبة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for leave_type in report.details.ended_long_term_leaves.by_type %}
                                                <tr>
                                                    <td>{{ leave_type.type }}</td>
                                                    <td>{{ leave_type.count }}</td>
                                                    <td>
                                                        {% set percentage = (leave_type.count / report.details.ended_long_term_leaves.total * 100) | round(1) %}
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar bg-success" style="width: {{ percentage }}%"></div>
                                                        </div>
                                                        {{ percentage }}%
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr class="font-weight-bold">
                                                    <td>الإجمالي</td>
                                                    <td>{{ report.details.ended_long_term_leaves.total }}</td>
                                                    <td>100%</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    {% else %}
                                    <p class="text-muted">لا توجد عطل منتهية في هذا الشهر</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التقاعدات -->
                    {% if report.details.new_retirements.by_type %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <i class="fas fa-user-clock"></i>
                                        التقاعدات حسب النوع
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        {% for retirement_type in report.details.new_retirements.by_type %}
                                        <div class="col-md-3">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-secondary">
                                                    <i class="fas fa-user-clock"></i>
                                                </span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">{{ retirement_type.type }}</span>
                                                    <span class="info-box-number">{{ retirement_type.count }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- ملاحظات وتوصيات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <i class="fas fa-lightbulb"></i>
                                        ملاحظات وتوصيات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-chart-line"></i> تحليل الاتجاهات:</h6>
                                        <ul class="mb-0">
                                            {% if report.comparisons.resignations_change > 0 %}
                                            <li>ارتفاع في عدد الاستقالات بنسبة {{ ((report.comparisons.resignations_change / (report.summary.new_resignations - report.comparisons.resignations_change)) * 100) | round(1) }}% مقارنة بالشهر السابق</li>
                                            {% elif report.comparisons.resignations_change < 0 %}
                                            <li>انخفاض في عدد الاستقالات بنسبة {{ ((abs(report.comparisons.resignations_change) / report.summary.new_resignations) * 100) | round(1) }}% مقارنة بالشهر السابق</li>
                                            {% endif %}
                                            
                                            {% if report.details.new_long_term_leaves.total > 0 %}
                                            <li>تم تسجيل {{ report.details.new_long_term_leaves.total }} عطلة طويلة الأمد جديدة</li>
                                            {% endif %}
                                            
                                            {% if report.details.new_retirements.total > 0 %}
                                            <li>{{ report.details.new_retirements.total }} موظف تقاعد خلال هذا الشهر</li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport(format) {
    const year = {{ report.period.year }};
    const month = {{ report.period.month }};
    
    const url = `/api/export_monthly_report?year=${year}&month=${month}&format=${format}`;
    window.open(url, '_blank');
}

// إضافة تنسيق للطباعة
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .card-tools {
        display: none !important;
    }
    
    .btn {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .info-box {
        border: 1px solid #ddd !important;
    }
}
</style>
{% endblock %}