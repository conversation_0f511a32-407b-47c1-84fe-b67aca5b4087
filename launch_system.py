#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل النهائي المحسن - نظام إدارة موظفي الجمارك الجزائرية
Enhanced Final Launch Script - Algerian Customs Employee Management System
"""

import os
import sys
import subprocess
import sqlite3
import webbrowser
import time
from datetime import datetime

def print_header():
    """طباعة رأس النظام"""
    print("=" * 70)
    print("🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - النسخة النهائية")
    print("   Algerian Customs Employee Management System - Final Version")
    print("=" * 70)
    print(f"📅 تاريخ التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - مناسب")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - غير مناسب")
        print("⚠️  يتطلب Python 3.8 أو أحدث")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 فحص وتثبيت المتطلبات...")
    
    requirements = [
        'flask',
        'pillow',
        'werkzeug'
    ]
    
    missing_packages = []
    
    for package in requirements:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} متوفر")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} مفقود")
    
    if missing_packages:
        print(f"\n🔧 تثبيت المكتبات المفقودة: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المتطلبات")
            return False
    
    return True

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️  فحص قاعدة البيانات...")
    
    db_path = 'customs_employees.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        print("🔧 إنشاء قاعدة البيانات...")
        
        try:
            subprocess.check_call([sys.executable, 'init_database.py'])
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return False
    else:
        print("✅ قاعدة البيانات موجودة")
    
    # فحص الجداول
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        essential_tables = [
            'employees', 'wilayas', 'communes', 'ranks', 
            'corps', 'services', 'annual_leaves'
        ]
        
        for table in essential_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ جدول {table} موجود")
            else:
                print(f"❌ جدول {table} مفقود")
                conn.close()
                return False
        
        # فحص عدد الموظفين
        cursor.execute("SELECT COUNT(*) FROM employees")
        employee_count = cursor.fetchone()[0]
        print(f"📊 عدد الموظفين: {employee_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_files():
    """فحص الملفات الأساسية"""
    print("\n📁 فحص الملفات الأساسية...")
    
    essential_files = [
        'app.py',
        'templates/base.html',
        'templates/index.html',
        'templates/employees/list.html',
        'static/css'
    ]
    
    for file_path in essential_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} موجود")
        else:
            print(f"❌ {file_path} مفقود")
            return False
    
    return True

def check_status_modules():
    """فحص وحدات إدارة الحالات"""
    print("\n🎛️  فحص وحدات إدارة الحالات...")
    
    status_modules = [
        'employee_status_manager.py',
        'employee_status_api.py',
        'employee_status_helpers.py',
        'status_integration.py'
    ]
    
    all_available = True
    for module in status_modules:
        if os.path.exists(module):
            print(f"✅ {module} متوفر")
        else:
            print(f"❌ {module} مفقود")
            all_available = False
    
    if all_available:
        print("✅ جميع وحدات إدارة الحالات متوفرة")
    else:
        print("⚠️  بعض وحدات إدارة الحالات مفقودة")
    
    return all_available

def launch_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل النظام...")
    print("=" * 50)
    
    try:
        # تشغيل التطبيق في الخلفية
        process = subprocess.Popen([
            sys.executable, 'app.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # انتظار قصير للتأكد من بدء التشغيل
        time.sleep(3)
        
        # فحص إذا كان التطبيق يعمل
        if process.poll() is None:
            print("✅ تم تشغيل النظام بنجاح!")
            print("\n🌐 روابط الوصول:")
            print("   الرئيسية: http://localhost:5000")
            print("   الموظفين: http://localhost:5000/employees")
            print("   العطل: http://localhost:5000/leaves")
            print("   الإحصائيات: http://localhost:5000/statistics")
            
            # فتح المتصفح تلقائياً
            print("\n🔗 فتح المتصفح...")
            try:
                webbrowser.open('http://localhost:5000')
                print("✅ تم فتح المتصفح")
            except:
                print("⚠️  لم يتم فتح المتصفح تلقائياً")
            
            print("\n" + "=" * 50)
            print("🎉 النظام جاهز للاستخدام!")
            print("💡 لإيقاف النظام: اضغط Ctrl+C")
            print("=" * 50)
            
            # انتظار إيقاف التطبيق
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n\n🛑 إيقاف النظام...")
                process.terminate()
                print("✅ تم إيقاف النظام بنجاح")
                
        else:
            # فحص الأخطاء
            stdout, stderr = process.communicate()
            print("❌ فشل في تشغيل النظام")
            if stderr:
                print(f"خطأ: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False
    
    return True

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📋 معلومات النظام:")
    print("   📁 المجلد: " + os.getcwd())
    print("   🐍 Python: " + sys.version.split()[0])
    print("   💻 النظام: " + os.name)
    print("   📅 التاريخ: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python_version():
        input("\n❌ اضغط Enter للخروج...")
        return
    
    # فحص وتثبيت المتطلبات
    if not install_requirements():
        input("\n❌ اضغط Enter للخروج...")
        return
    
    # فحص الملفات
    if not check_files():
        input("\n❌ اضغط Enter للخروج...")
        return
    
    # فحص قاعدة البيانات
    if not check_database():
        input("\n❌ اضغط Enter للخروج...")
        return
    
    # فحص وحدات الحالات
    status_available = check_status_modules()
    if status_available:
        print("🎛️  وحدات إدارة الحالات متاحة")
    else:
        print("⚠️  وحدات إدارة الحالات غير متاحة (النظام سيعمل بدونها)")
    
    # عرض معلومات النظام
    show_system_info()
    
    print("\n" + "=" * 50)
    print("✅ جميع الفحوصات نجحت!")
    print("🚀 جاري تشغيل النظام...")
    print("=" * 50)
    
    # تشغيل التطبيق
    launch_application()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")