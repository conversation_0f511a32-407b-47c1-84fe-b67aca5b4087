@echo off
chcp 65001 >nul
title نظام إدارة موظفي الجمارك الجزائرية

echo.
echo ===============================================================
echo 🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - النسخة النهائية
echo    Algerian Customs Employee Management System - Final Version
echo ===============================================================
echo.

:: فحص Python
echo 🐍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo 💡 يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

:: الانتقال لمجلد المشروع
cd /d "%~dp0"

:: فحص وجود الملفات الأساسية
echo 📁 فحص الملفات الأساسية...
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)

if not exist "templates" (
    echo ❌ مجلد templates غير موجود
    pause
    exit /b 1
)

echo ✅ الملفات الأساسية موجودة
echo.

:: تشغيل النظام المحسن
echo 🚀 تشغيل النظام...
echo ===============================================================
echo.

python launch_system.py

echo.
echo ===============================================================
echo 🛑 تم إيقاف النظام
echo ===============================================================
pause