#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الحقول الـ 30 في صفحة المعاينة
"""

from app import app

def test_all_30_fields():
    """اختبار جميع الحقول الـ 30"""
    print("🔍 اختبار جميع الحقول الـ 30 في صفحة المعاينة...")
    print("=" * 70)
    
    # قائمة جميع الحقول المتوقعة
    expected_fields = [
        # البيانات الأساسية
        ('id', 'معرف الموظف'),
        ('registration_number', 'رقم التسجيل'),
        ('first_name', 'الاسم (عربي)'),
        ('last_name', 'اللقب (عربي)'),
        ('first_name_fr', 'الاسم (فرنسي)'),
        ('last_name_fr', 'اللقب (فرنسي)'),
        
        # البيانات الشخصية
        ('birth_date', 'تاريخ الميلاد'),
        ('birth_wilaya_id', 'معرف ولاية الميلاد'),
        ('birth_commune_id', 'معرف بلدية الميلاد'),
        ('gender', 'الجنس'),
        ('social_security_number', 'رقم الضمان الاجتماعي'),
        ('marital_status', 'الحالة العائلية'),
        ('children_count', 'عدد الأبناء'),
        ('dependents_count', 'عدد المتكفل بهم'),
        ('blood_type', 'زمرة الدم'),
        ('sport_practiced', 'الرياضة الممارسة'),
        
        # البيانات المهنية
        ('hire_date', 'تاريخ التوظيف'),
        ('current_rank_id', 'معرف الرتبة'),
        ('corps_id', 'معرف السلك'),
        ('current_service_id', 'معرف المصلحة'),
        ('status', 'الحالة الوظيفية'),
        
        # بيانات الاتصال
        ('postal_account', 'الحساب الجاري البريدي'),
        ('phone', 'رقم الهاتف (قديم)'),
        ('phone1', 'رقم الهاتف 1'),
        ('phone2', 'رقم الهاتف 2'),
        ('email', 'البريد الإلكتروني'),
        ('address', 'العنوان'),
        
        # معلومات النظام
        ('photo', 'الصورة'),
        ('created_at', 'تاريخ الإنشاء'),
        ('updated_at', 'آخر تحديث')
    ]
    
    with app.test_client() as client:
        try:
            response = client.get('/employee/11')
            
            if response.status_code == 200:
                content = response.data.decode('utf-8')
                print("✅ صفحة المعاينة تعمل بنجاح!")
                print(f"📄 حجم المحتوى: {len(content):,} حرف")
                
                print(f"\n📋 فحص جميع الحقول الـ {len(expected_fields)}:")
                print("-" * 70)
                
                found_fields = 0
                missing_fields = []
                
                for field_name, field_desc in expected_fields:
                    # البحث عن الحقل في المحتوى
                    field_found = False
                    
                    # قائمة الكلمات المفتاحية للبحث عن كل حقل
                    search_terms = {
                        'id': ['معرف الموظف'],
                        'registration_number': ['رقم التسجيل', '123456'],
                        'first_name': ['الاسم (عربي)', 'أحمد'],
                        'last_name': ['اللقب (عربي)', 'بن علي'],
                        'first_name_fr': ['الاسم (فرنسي)', 'Ahmed'],
                        'last_name_fr': ['اللقب (فرنسي)', 'Ben Ali'],
                        'birth_date': ['تاريخ الميلاد', '1985-05-15'],
                        'birth_wilaya_id': ['معرف ولاية الميلاد'],
                        'birth_commune_id': ['معرف بلدية الميلاد'],
                        'gender': ['الجنس', 'ذكر'],
                        'social_security_number': ['رقم الضمان الاجتماعي', '18505**********'],
                        'marital_status': ['الحالة العائلية', 'متزوج'],
                        'children_count': ['عدد الأبناء'],
                        'dependents_count': ['عدد المتكفل بهم'],
                        'blood_type': ['زمرة الدم', 'O+'],
                        'sport_practiced': ['الرياضة الممارسة', 'كرة القدم'],
                        'hire_date': ['تاريخ التوظيف', '2010-09-01'],
                        'current_rank_id': ['معرف الرتبة'],
                        'corps_id': ['معرف السلك'],
                        'current_service_id': ['معرف المصلحة'],
                        'status': ['الحالة الوظيفية', 'نشط'],
                        'postal_account': ['الحساب الجاري البريدي', '**********'],
                        'phone': ['رقم الهاتف (قديم)'],
                        'phone1': ['رقم الهاتف 1', '**********'],
                        'phone2': ['رقم الهاتف 2', '**********'],
                        'email': ['البريد الإلكتروني', '<EMAIL>'],
                        'address': ['العنوان', 'حي النصر'],
                        'photo': ['حالة الصورة', 'غير موجودة'],
                        'created_at': ['تاريخ الإنشاء'],
                        'updated_at': ['آخر تحديث']
                    }
                    
                    # البحث عن الحقل
                    if field_name in search_terms:
                        for term in search_terms[field_name]:
                            if term in content:
                                field_found = True
                                break
                    
                    if field_found:
                        found_fields += 1
                        print(f"   ✅ {field_desc:<35} موجود")
                    else:
                        missing_fields.append((field_name, field_desc))
                        print(f"   ❌ {field_desc:<35} مفقود")
                
                print("-" * 70)
                print(f"📊 النتائج:")
                print(f"   ✅ الحقول الموجودة: {found_fields}/{len(expected_fields)}")
                print(f"   ❌ الحقول المفقودة: {len(missing_fields)}")
                
                if missing_fields:
                    print(f"\n⚠️  الحقول المفقودة:")
                    for field_name, field_desc in missing_fields:
                        print(f"      - {field_desc} ({field_name})")
                
                # حساب النسبة المئوية
                percentage = (found_fields / len(expected_fields)) * 100
                print(f"\n📈 نسبة الاكتمال: {percentage:.1f}%")
                
                if percentage >= 90:
                    print("🎉 ممتاز! صفحة المعاينة تعرض معظم الحقول")
                elif percentage >= 70:
                    print("👍 جيد! صفحة المعاينة تعرض أغلب الحقول")
                else:
                    print("⚠️  يحتاج تحسين - العديد من الحقول مفقودة")
                    
            else:
                print(f"❌ خطأ في الوصول للصفحة: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
    
    print(f"\n🌐 للاختبار اليدوي:")
    print(f"   اذهب إلى: http://localhost:5000/employee/11")

if __name__ == "__main__":
    test_all_30_fields()