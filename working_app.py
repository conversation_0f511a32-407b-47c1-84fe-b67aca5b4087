#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3

app = Flask(__name__)
app.config['SECRET_KEY'] = 'customs-algeria-2025'

def get_db_connection():
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """تهيئة قاعدة البيانات"""
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    # جدول أسباب الاستيداع
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reason TEXT NOT NULL UNIQUE,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إدراج أسباب افتراضية
    reasons = [
        'رعاية الأطفال',
        'الدراسة', 
        'ظروف شخصية',
        'ظروف صحية',
        'مرافقة الزوج',
        'ظروف عائلية',
        'أسباب أخرى'
    ]
    
    for reason in reasons:
        cursor.execute('''
            INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
            VALUES (?)
        ''', (reason,))
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/special_status/')
def special_status_index():
    """الصفحة الرئيسية للحالات الخاصة"""
    stats = {
        'active_leave_of_absence': 0,
        'pending_resignations': 0,
        'active_suspensions': 0,
        'total_deaths': 0,
        'resignations': 0,
        'deaths': 0,
        'retirements': 0,
        'external_transfers': 0,
        'leave_of_absence': 0,
        'suspensions': 0
    }
    
    return render_template('special_status/index.html', stats=stats)

@app.route('/special_status/leave_reasons_settings')
def leave_reasons_settings():
    """إعدادات أسباب الاستيداع"""
    conn = get_db_connection()
    try:
        reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        reasons = []
    finally:
        conn.close()
    
    return render_template('special_status/leave_reasons_settings.html', reasons=reasons)

@app.route('/special_status/add_leave_reason', methods=['POST'])
def add_leave_reason():
    """إضافة سبب استيداع جديد"""
    reason = request.form.get('reason')
    if reason:
        conn = get_db_connection()
        try:
            conn.execute('''
                INSERT INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
            conn.commit()
            flash('تم إضافة السبب بنجاح', 'success')
        except:
            flash('خطأ في إضافة السبب', 'error')
        finally:
            conn.close()
    
    return redirect(url_for('leave_reasons_settings'))

@app.route('/special_status/leave_of_absence/add', methods=['GET', 'POST'])
def add_leave_of_absence():
    """إضافة استيداع جديد"""
    if request.method == 'POST':
        flash('تم إضافة الاستيداع بنجاح', 'success')
        return redirect(url_for('special_status_index'))
    
    # الحصول على قائمة الموظفين
    conn = get_db_connection()
    try:
        employees = conn.execute('''
            SELECT id, registration_number, first_name, last_name
            FROM employees 
            ORDER BY registration_number
        ''').fetchall()
    except:
        employees = []
    
    try:
        leave_reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        leave_reasons = []
    finally:
        conn.close()
    
    return render_template('special_status/add_leave_of_absence.html', 
                         employees=employees, leave_reasons=leave_reasons)

@app.route('/special_status/api/employee/<int:employee_id>')
def api_employee_info(employee_id):
    """API للحصول على معلومات الموظف"""
    conn = get_db_connection()
    try:
        employee = conn.execute('''
            SELECT e.*, r.name as rank_name, s.name as service_name
            FROM employees e
            LEFT JOIN ranks r ON e.current_rank_id = r.id
            LEFT JOIN services s ON e.current_service_id = s.id
            WHERE e.id = ?
        ''', (employee_id,)).fetchone()
        
        if employee:
            return jsonify({
                'id': employee['id'],
                'registration_number': employee['registration_number'],
                'first_name': employee['first_name'],
                'last_name': employee['last_name'],
                'rank_name': employee['rank_name'] if employee['rank_name'] else 'غير محدد',
                'service_name': employee['service_name'] if employee['service_name'] else 'غير محدد',
                'status': employee['status'] if employee['status'] else 'نشط'
            })
        else:
            return jsonify({'error': 'الموظف غير موجود'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/special_status/api/employee/<int:employee_id>/leave_balance')
def api_employee_leave_balance(employee_id):
    """API للحصول على رصيد الاستيداع للموظف"""
    # رصيد افتراضي
    balance = {
        'total_used_months': 0,
        'total_used_years': 0,
        'remaining_months': 60,
        'remaining_years': 5,
        'max_allowed_years': 5
    }
    return jsonify(balance)

@app.route('/special_status/api/leave_reason/<int:reason_id>/deactivate', methods=['POST'])
def api_deactivate_leave_reason(reason_id):
    """API لإلغاء تفعيل سبب الاستيداع"""
    conn = get_db_connection()
    try:
        conn.execute('''
            UPDATE leave_of_absence_reasons 
            SET is_active = 0
            WHERE id = ?
        ''', (reason_id,))
        conn.commit()
        return jsonify({'success': True})
    except:
        return jsonify({'success': False, 'error': 'خطأ في العملية'}), 500
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 تشغيل التطبيق...")
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("📋 الحالات الخاصة: http://localhost:5000/special_status/")
    
    # تهيئة قاعدة البيانات
    init_db()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
