#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات بسيطة لإدارة حالات الموظفين
Simple Employee Status Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from simple_status_system import SimpleStatusManager
from datetime import datetime, date
import json

# إنشاء Blueprint
simple_status_bp = Blueprint('simple_status', __name__, url_prefix='/status')

# إنشاء نسخة من المدير
status_manager = SimpleStatusManager()

@simple_status_bp.route('/')
def dashboard():
    """لوحة تحكم حالات الموظفين"""
    stats = status_manager.get_statistics()
    return render_template('simple_status/dashboard.html', stats=stats)

@simple_status_bp.route('/leave_of_absence/add/<int:employee_id>', methods=['GET', 'POST'])
def add_leave_of_absence(employee_id):
    """إضافة استيداع"""
    if request.method == 'POST':
        data = {
            'reason_id': request.form.get('reason_id'),
            'duration_months': request.form.get('duration_months'),
            'start_date': request.form.get('start_date'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date'),
            'created_by': 'النظام'
        }
        
        success, message = status_manager.add_leave_of_absence(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على أسباب الاستيداع
    conn = status_manager.get_db_connection()
    reasons = conn.execute('SELECT id, reason_text FROM leave_reasons WHERE is_active = 1').fetchall()
    
    # الحصول على بيانات الموظف
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('simple_status/add_leave_of_absence.html', 
                         employee=employee, reasons=reasons)

@simple_status_bp.route('/death/add/<int:employee_id>', methods=['GET', 'POST'])
def add_death(employee_id):
    """إضافة وفاة"""
    if request.method == 'POST':
        data = {
            'death_date': request.form.get('death_date'),
            'death_cause': request.form.get('death_cause'),
            'death_certificate_number': request.form.get('death_certificate_number'),
            'transferred_by': 'النظام'
        }
        
        success, message = status_manager.transfer_to_death(employee_id, data)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('employees'))
        else:
            flash(message, 'error')
    
    # الحصول على بيانات الموظف
    conn = status_manager.get_db_connection()
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    conn.close()
    
    return render_template('simple_status/add_death.html', employee=employee)

@simple_status_bp.route('/deceased')
def deceased_list():
    """قائمة المتوفين"""
    conn = status_manager.get_db_connection()
    deceased = conn.execute('SELECT * FROM deceased_employees ORDER BY death_date DESC').fetchall()
    conn.close()
    
    return render_template('simple_status/deceased_list.html', deceased=deceased)

@simple_status_bp.route('/api/leave_balance/<int:employee_id>')
def api_leave_balance(employee_id):
    """API لرصيد الاستيداع"""
    balance = status_manager.get_leave_balance(employee_id)
    return jsonify(balance)

@simple_status_bp.route('/api/statistics')
def api_statistics():
    """API للإحصائيات"""
    stats = status_manager.get_statistics()
    return jsonify(stats)

def register_simple_status_routes(app):
    """تسجيل المسارات البسيطة"""
    app.register_blueprint(simple_status_bp)
    print("✅ تم تسجيل المسارات البسيطة لحالات الموظفين")