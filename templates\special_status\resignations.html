{% extends "base.html" %}

{% block page_title %}الاستقالات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-user-minus me-2"></i>
                            قائمة الاستقالات
                        </h3>
                        <div>
                            <a href="{{ url_for('special_status.add_resignation') }}" class="btn btn-light">
                                <i class="fas fa-plus me-2"></i>إضافة استقالة جديدة
                            </a>
                            <a href="{{ url_for('special_status.dashboard') }}" class="btn btn-outline-light">
                                <i class="fas fa-arrow-right me-2"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if resignations %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم التسجيل</th>
                                    <th>الاسم الكامل</th>
                                    <th>الرتبة</th>
                                    <th>المصلحة</th>
                                    <th>تاريخ الاستقالة</th>
                                    <th>السبب</th>
                                    <th>رقم القرار</th>
                                    <th>تاريخ القرار</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for resignation in resignations %}
                                <tr>
                                    <td>{{ resignation.registration_number }}</td>
                                    <td>{{ resignation.first_name }} {{ resignation.last_name }}</td>
                                    <td>{{ resignation.rank_name or 'غير محدد' }}</td>
                                    <td>{{ resignation.service_name or 'غير محدد' }}</td>
                                    <td>{{ resignation.resignation_date }}</td>
                                    <td>{{ resignation.reason or 'غير محدد' }}</td>
                                    <td>{{ resignation.decision_number or 'غير محدد' }}</td>
                                    <td>{{ resignation.decision_date or 'غير محدد' }}</td>
                                    <td>
                                        {% if resignation.status == 'pending' %}
                                            <span class="badge bg-warning">معلقة</span>
                                        {% elif resignation.status == 'approved' %}
                                            <span class="badge bg-success">مؤكدة</span>
                                        {% elif resignation.status == 'rejected' %}
                                            <span class="badge bg-danger">مرفوضة</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ resignation.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="viewResignation({{ resignation.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="editResignation({{ resignation.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if resignation.status == 'pending' %}
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="approveResignation({{ resignation.id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="rejectResignation({{ resignation.id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-user-minus fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد استقالات مسجلة</h5>
                        <p class="text-muted">يمكنك إضافة استقالة جديدة من خلال الزر أعلاه</p>
                        <a href="{{ url_for('special_status.add_resignation') }}" class="btn btn-danger">
                            <i class="fas fa-plus me-2"></i>إضافة استقالة جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الاستقالة -->
<div class="modal fade" id="resignationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تفاصيل الاستقالة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resignationDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewResignation(id) {
    // عرض تفاصيل الاستقالة
    fetch(`/special_status/api/resignation/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('خطأ: ' + data.error);
                return;
            }
            
            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>رقم التسجيل:</strong> ${data.registration_number}<br>
                        <strong>الاسم:</strong> ${data.first_name} ${data.last_name}<br>
                        <strong>الرتبة:</strong> ${data.rank_name || 'غير محدد'}<br>
                        <strong>المصلحة:</strong> ${data.service_name || 'غير محدد'}<br>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الاستقالة:</strong> ${data.resignation_date}<br>
                        <strong>تاريخ السريان:</strong> ${data.effective_date || 'غير محدد'}<br>
                        <strong>رقم القرار:</strong> ${data.decision_number || 'غير محدد'}<br>
                        <strong>تاريخ القرار:</strong> ${data.decision_date || 'غير محدد'}<br>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <strong>السبب:</strong><br>
                        <p class="border p-2 bg-light">${data.reason || 'غير محدد'}</p>
                    </div>
                </div>
                ${data.notes ? `
                <div class="row">
                    <div class="col-12">
                        <strong>ملاحظات:</strong><br>
                        <p class="border p-2 bg-light">${data.notes}</p>
                    </div>
                </div>
                ` : ''}
            `;
            
            document.getElementById('resignationDetails').innerHTML = details;
            new bootstrap.Modal(document.getElementById('resignationModal')).show();
        })
        .catch(error => {
            alert('خطأ في تحميل البيانات: ' + error);
        });
}

function editResignation(id) {
    // تحرير الاستقالة
    window.location.href = `/special_status/resignations/edit/${id}`;
}

function approveResignation(id) {
    if (confirm('هل أنت متأكد من تأكيد هذه الاستقالة؟')) {
        fetch(`/special_status/api/resignation/${id}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تأكيد الاستقالة بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            alert('خطأ في العملية: ' + error);
        });
    }
}

function rejectResignation(id) {
    if (confirm('هل أنت متأكد من رفض هذه الاستقالة؟')) {
        fetch(`/special_status/api/resignation/${id}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم رفض الاستقالة');
                location.reload();
            } else {
                alert('خطأ: ' + data.error);
            }
        })
        .catch(error => {
            alert('خطأ في العملية: ' + error);
        });
    }
}
</script>
{% endblock %}
