{% extends "base.html" %}

{% block title %}طلب استقالة - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}طلب استقالة{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">
            <i class="fas fa-sign-out-alt me-2"></i>طلب استقالة الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ التوظيف:</strong> {{ employee.hiring_date or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-success">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الاستقالة -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>بيانات طلب الاستقالة
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="resignationForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="request_date" class="form-label">تاريخ الطلب <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="request_date" name="request_date" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الاستقالة <span class="text-danger">*</span></label>
                        <select class="form-select" id="reason" name="reason" required>
                            <option value="">اختر سبب الاستقالة</option>
                            <option value="أسباب شخصية">أسباب شخصية</option>
                            <option value="فرصة عمل أفضل">فرصة عمل أفضل</option>
                            <option value="أسباب صحية">أسباب صحية</option>
                            <option value="أسباب عائلية">أسباب عائلية</option>
                            <option value="عدم الرضا عن العمل">عدم الرضا عن العمل</option>
                            <option value="تغيير المسار المهني">تغيير المسار المهني</option>
                            <option value="الانتقال لمدينة أخرى">الانتقال لمدينة أخرى</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- حقل السبب المخصص -->
            <div class="row" id="customReasonRow" style="display: none;">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="custom_reason" class="form-label">تفاصيل سبب الاستقالة</label>
                        <textarea class="form-control" id="custom_reason" name="custom_reason" rows="3" 
                                  placeholder="أدخل تفاصيل سبب الاستقالة"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- حالة قبول الاستقالة -->
            <div class="card border-info mb-3">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">حالة الطلب</h6>
                </div>
                <div class="card-body">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_accepted" name="is_accepted">
                        <label class="form-check-label" for="is_accepted">
                            <strong>الطلب مقبول</strong>
                        </label>
                    </div>
                    <div class="form-text">اتركه فارغاً إذا كان الطلب لا يزال قيد الدراسة</div>
                </div>
            </div>
            
            <!-- بيانات القرار (تظهر عند قبول الطلب) -->
            <div id="decisionFields" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="decision_number" class="form-label">رقم المقرر</label>
                            <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                   placeholder="أدخل رقم مقرر قبول الاستقالة">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="decision_date" class="form-label">تاريخ المقرر</label>
                            <input type="date" class="form-control" id="decision_date" name="decision_date">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تحذير -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> في حالة قبول الاستقالة، سيتم إنهاء جميع الحقوق والواجبات الوظيفية للموظف نهائياً.
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل الطلب
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal تأكيد الاستقالة -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد طلب الاستقالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من تسجيل طلب استقالة الموظف:</p>
                <div class="text-center p-3 border rounded bg-light">
                    <h6><strong>{{ employee.first_name }} {{ employee.last_name }}</strong></h6>
                    <p class="mb-0">رقم التسجيل: {{ employee.registration_number }}</p>
                </div>
                <div class="mt-3">
                    <p><strong>تاريخ الطلب:</strong> <span id="confirmDate"></span></p>
                    <p><strong>السبب:</strong> <span id="confirmReason"></span></p>
                    <p><strong>حالة الطلب:</strong> <span id="confirmStatus"></span></p>
                </div>
                <div class="alert alert-info mt-3 mb-0" id="acceptedAlert" style="display: none;">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم تغيير حالة الموظف إلى "مستقيل" فوراً عند قبول الطلب.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>تأكيد التسجيل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إظهار/إخفاء حقل السبب المخصص
document.getElementById('reason').addEventListener('change', function() {
    const customReasonRow = document.getElementById('customReasonRow');
    if (this.value === 'أخرى') {
        customReasonRow.style.display = 'block';
        document.getElementById('custom_reason').required = true;
    } else {
        customReasonRow.style.display = 'none';
        document.getElementById('custom_reason').required = false;
    }
});

// إظهار/إخفاء حقول القرار
document.getElementById('is_accepted').addEventListener('change', function() {
    const decisionFields = document.getElementById('decisionFields');
    if (this.checked) {
        decisionFields.style.display = 'block';
        document.getElementById('decision_number').required = true;
        document.getElementById('decision_date').required = true;
    } else {
        decisionFields.style.display = 'none';
        document.getElementById('decision_number').required = false;
        document.getElementById('decision_date').required = false;
    }
});

document.getElementById('resignationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const requestDate = document.getElementById('request_date').value;
    const reason = document.getElementById('reason').value;
    const isAccepted = document.getElementById('is_accepted').checked;
    
    if (!requestDate || !reason) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من السبب المخصص إذا كان مطلوباً
    if (reason === 'أخرى') {
        const customReason = document.getElementById('custom_reason').value.trim();
        if (!customReason) {
            alert('يرجى إدخال تفاصيل سبب الاستقالة');
            return;
        }
    }
    
    // التحقق من بيانات القرار إذا كان مقبولاً
    if (isAccepted) {
        const decisionNumber = document.getElementById('decision_number').value.trim();
        const decisionDate = document.getElementById('decision_date').value;
        
        if (!decisionNumber || !decisionDate) {
            alert('يرجى ملء رقم وتاريخ المقرر عند قبول الطلب');
            return;
        }
    }
    
    // تحديث بيانات التأكيد
    document.getElementById('confirmDate').textContent = requestDate;
    document.getElementById('confirmReason').textContent = reason === 'أخرى' ? 
        document.getElementById('custom_reason').value : reason;
    document.getElementById('confirmStatus').textContent = isAccepted ? 'مقبولة' : 'معلقة';
    
    // إظهار/إخفاء تنبيه القبول
    const acceptedAlert = document.getElementById('acceptedAlert');
    if (isAccepted) {
        acceptedAlert.style.display = 'block';
    } else {
        acceptedAlert.style.display = 'none';
    }
    
    // إظهار مودال التأكيد
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
});

document.getElementById('confirmSubmit').addEventListener('click', function() {
    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
    
    // تعطيل الزر وإظهار مؤشر التحميل
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
    
    // تحديث قيمة السبب إذا كان مخصصاً
    const reasonSelect = document.getElementById('reason');
    if (reasonSelect.value === 'أخرى') {
        reasonSelect.value = document.getElementById('custom_reason').value;
    }
    
    // إرسال النموذج
    document.getElementById('resignationForm').submit();
});

// تحديد تاريخ اليوم كحد أقصى
document.getElementById('request_date').max = new Date().toISOString().split('T')[0];
document.getElementById('decision_date').max = new Date().toISOString().split('T')[0];
</script>
{% endblock %}