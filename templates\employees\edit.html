{% extends "base.html" %}

{% block title %}تعديل الموظف: {{ employee.first_name }} {{ employee.last_name }} - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}تعديل بيانات الموظف: {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="{{ url_for('employees') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الموظفين
                </a>
                <a href="{{ url_for('employee_detail', employee_id=employee.id) }}" class="btn btn-info">
                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                </a>
            </div>
            <div>
                <span class="badge bg-primary fs-6">{{ employee.registration_number }}</span>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الموظف -->
<form method="POST" enctype="multipart/form-data" id="employeeForm">
    <div class="row">
        <!-- البيانات الأساسية -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i>البيانات الأساسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم التسجيل <span class="required">*</span></label>
                                <input type="text" name="registration_number" class="form-control" 
                                       value="{{ employee.registration_number }}" required maxlength="6" pattern="[0-9]{6}">
                                <div class="form-text">6 أرقام فقط - غير مكرر</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الجنس <span class="required">*</span></label>
                                <select name="gender" class="form-select" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر" {% if employee.gender == 'ذكر' %}selected{% endif %}>ذكر</option>
                                    <option value="أنثى" {% if employee.gender == 'أنثى' %}selected{% endif %}>أنثى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اللقب <span class="required">*</span></label>
                                <input type="text" name="last_name" class="form-control" 
                                       value="{{ employee.last_name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم <span class="required">*</span></label>
                                <input type="text" name="first_name" class="form-control" 
                                       value="{{ employee.first_name }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom (فرنسي)</label>
                                <input type="text" name="last_name_fr" class="form-control" 
                                       value="{{ employee.last_name_fr or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Prénom (فرنسي)</label>
                                <input type="text" name="first_name_fr" class="form-control" 
                                       value="{{ employee.first_name_fr or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الميلاد</label>
                                <input type="date" name="birth_date" class="form-control" 
                                       value="{{ employee.birth_date or '' }}" min="1960-01-01" max="2006-12-31">
                                <div class="form-text">من 19 إلى 65 سنة</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">ولاية الميلاد</label>
                                <select name="birth_wilaya_id" class="form-select" 
                                        onchange="updateCommunes(this, document.getElementById('birth_commune_id'))">
                                    <option value="">اختر الولاية</option>
                                    {% for wilaya in wilayas %}
                                    <option value="{{ wilaya.id }}" {% if employee.birth_wilaya_id == wilaya.id %}selected{% endif %}>
                                        {{ wilaya.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">بلدية الميلاد</label>
                                <select name="birth_commune_id" class="form-select" id="birth_commune_id">
                                    <option value="">اختر البلدية</option>
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحالة العائلية</label>
                                <select name="marital_status" class="form-select" id="marital_status" 
                                        onchange="toggleChildrenCount()">
                                    <option value="">اختر الحالة</option>
                                    <option value="أعزب" {% if employee.marital_status == 'أعزب' %}selected{% endif %}>أعزب</option>
                                    <option value="متزوج" {% if employee.marital_status == 'متزوج' %}selected{% endif %}>متزوج</option>
                                    <option value="مطلق" {% if employee.marital_status == 'مطلق' %}selected{% endif %}>مطلق</option>
                                    <option value="أرمل" {% if employee.marital_status == 'أرمل' %}selected{% endif %}>أرمل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4" id="children_group">
                            <div class="mb-3">
                                <label class="form-label">عدد الأبناء</label>
                                <input type="number" name="children_count" class="form-control" 
                                       min="0" max="20" value="{{ employee.children_count or 0 }}" id="children_count">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">عدد الأشخاص المتكفل بهم</label>
                                <input type="number" name="dependents_count" class="form-control" 
                                       min="0" max="20" value="{{ employee.dependents_count or 0 }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">زمرة الدم</label>
                                <select name="blood_type" class="form-select">
                                    <option value="">اختر زمرة الدم</option>
                                    <option value="A+" {% if employee.blood_type == 'A+' %}selected{% endif %}>A+</option>
                                    <option value="A-" {% if employee.blood_type == 'A-' %}selected{% endif %}>A-</option>
                                    <option value="B+" {% if employee.blood_type == 'B+' %}selected{% endif %}>B+</option>
                                    <option value="B-" {% if employee.blood_type == 'B-' %}selected{% endif %}>B-</option>
                                    <option value="AB+" {% if employee.blood_type == 'AB+' %}selected{% endif %}>AB+</option>
                                    <option value="AB-" {% if employee.blood_type == 'AB-' %}selected{% endif %}>AB-</option>
                                    <option value="O+" {% if employee.blood_type == 'O+' %}selected{% endif %}>O+</option>
                                    <option value="O-" {% if employee.blood_type == 'O-' %}selected{% endif %}>O-</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرياضة الممارسة</label>
                                <input type="text" name="sport_practiced" class="form-control" 
                                       value="{{ employee.sport_practiced or '' }}" placeholder="كرة القدم، السباحة...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- صورة الموظف -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-camera me-2"></i>صورة الموظف</h5>
                </div>
                <div class="card-body text-center">
                    <div class="photo-upload-area" onclick="document.getElementById('photo').click()">
                        {% if employee.photo %}
                        <img id="photo_preview" src="{{ employee.photo }}" alt="صورة الموظف" 
                             style="max-width: 100%; max-height: 200px; border-radius: 0.5rem;">
                        <div id="upload_placeholder" style="display: none;">
                        {% else %}
                        <img id="photo_preview" src="" alt="معاينة الصورة" 
                             style="display: none; max-width: 100%; max-height: 200px; border-radius: 0.5rem;">
                        <div id="upload_placeholder">
                        {% endif %}
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p class="text-muted">انقر لتغيير صورة الموظف</p>
                            <small class="text-muted">الحد الأقصى: 16 ميجابايت</small>
                        </div>
                    </div>
                    <input type="file" name="photo" id="photo" accept="image/*" 
                           style="display: none;" onchange="previewPhoto(this)">
                    <div class="form-text mt-2">سيتم تعديل حجم الصورة تلقائياً إلى 300×400 بكسل</div>
                    
                    <!-- حالة الموظف تحت الصورة -->
                    <div class="mt-4">
                        <label class="form-label">حالة الموظف</label>
                        <select name="status" class="form-select employee-status-select" id="employee_status" onchange="handleStatusChange()">
                            <option value="">اختر حالة الموظف</option>
                            <!-- سيتم تحميل الحالات ديناميكياً -->
                        </select>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                عند تغيير الحالة ستفتح نافذة لإدخال تفاصيل الحالة الجديدة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- بيانات الاتصال -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-phone me-2"></i>بيانات الاتصال</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف 1</label>
                        <input type="tel" name="phone1" class="form-control" 
                               value="{{ employee.phone1 or '' }}" placeholder="021-XX-XX-XX">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف 2</label>
                        <input type="tel" name="phone2" class="form-control" 
                               value="{{ employee.phone2 or '' }}" placeholder="05XX-XX-XX-XX">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" class="form-control" 
                               value="{{ employee.email or '' }}" placeholder="<EMAIL>">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">العنوان الرئيسي</label>
                        <textarea name="main_address" class="form-control" rows="2">{{ employee.main_address or '' }}</textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">العنوان الثانوي</label>
                        <textarea name="secondary_address" class="form-control" rows="2">{{ employee.secondary_address or '' }}</textarea>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">اسم الشخص المتصل به في حالة الضرورة</label>
                        <input type="text" name="emergency_contact_name" class="form-control" 
                               value="{{ employee.emergency_contact_name or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">عنوان الشخص المتصل به</label>
                        <textarea name="emergency_contact_address" class="form-control" rows="2">{{ employee.emergency_contact_address or '' }}</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- البيانات المهنية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-briefcase me-2"></i>البيانات المهنية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">السلك</label>
                        <select name="corps_id" class="form-select" onchange="updateRanks(this, document.getElementById('current_rank_id'))">
                            <option value="">اختر السلك</option>
                            {% for corps in corps %}
                            <option value="{{ corps.id }}" {% if employee.corps_id == corps.id %}selected{% endif %}>
                                {{ corps.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الرتبة الحالية</label>
                        <select name="current_rank_id" class="form-select" id="current_rank_id">
                            <option value="">اختر الرتبة</option>
                            <!-- سيتم ملؤها ديناميكياً -->
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الترقية في الرتبة الحالية</label>
                        <input type="date" name="rank_promotion_date" class="form-control"
                               value="{{ employee.rank_promotion_date or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الوظيفة الحالية</label>
                        <input type="text" name="current_position" class="form-control"
                               value="{{ employee.current_position or '' }}" placeholder="مثال: رئيس مصلحة">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الوظيفة الحالية</label>
                        <select name="current_position_id" class="form-select">
                            <option value="">اختر الوظيفة</option>
                            {% for position in positions %}
                            <option value="{{ position.id }}" {% if employee.current_position_id == position.id %}selected{% endif %}>
                                {{ position.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ التعيين في الوظيفة الحالية</label>
                        <input type="date" name="position_assignment_date" class="form-control"
                               value="{{ employee.position_assignment_date or '' }}">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المديرية</label>
                        <select name="current_directorate_id" class="form-select">
                            <option value="">اختر المديرية</option>
                            {% for directorate in directorates %}
                            <option value="{{ directorate.id }}" {% if employee.current_directorate_id == directorate.id %}selected{% endif %}>
                                {{ directorate.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المصلحة</label>
                        <select name="current_service_id" class="form-select">
                            <option value="">اختر المصلحة</option>
                            {% for service in services %}
                            <option value="{{ service.id }}" {% if employee.current_service_id == service.id %}selected{% endif %}>
                                {{ service.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ التوظيف/الدخول في إدارة الجمارك</label>
                        <input type="date" name="hiring_date" class="form-control"
                               value="{{ employee.hiring_date or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رتبة التوظيف</label>
                        <select name="hiring_rank_id" class="form-select">
                            <option value="">اختر رتبة التوظيف</option>
                            {% for rank in ranks %}
                            <option value="{{ rank.id }}" {% if employee.hiring_rank_id == rank.id %}selected{% endif %}>
                                {{ rank.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الوثائق والبطاقات -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-id-card me-2"></i>الوثائق والبطاقات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم الضمان الاجتماعي</label>
                        <input type="text" name="social_security_number" class="form-control"
                               value="{{ employee.social_security_number or '' }}" maxlength="15" pattern="[0-9]{15}">
                        <div class="form-text">15 رقم - سيتم التحقق من صحته</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم الحساب الجاري البريدي</label>
                        <input type="text" name="postal_account_number" class="form-control"
                               value="{{ employee.postal_account_number or '' }}" maxlength="10" pattern="[0-9]{10}">
                        <div class="form-text">10 أرقام - سيتم التحقق من صحته</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم البطاقة المهنية</label>
                        <input type="text" name="professional_card_number" class="form-control"
                               value="{{ employee.professional_card_number or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ صدور البطاقة المهنية</label>
                        <input type="date" name="professional_card_issue_date" class="form-control"
                               value="{{ employee.professional_card_issue_date or '' }}">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">رقم بطاقة التعريف الوطنية</label>
                        <input type="text" name="national_id_number" class="form-control"
                               value="{{ employee.national_id_number or '' }}">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ صدورها</label>
                        <input type="date" name="national_id_issue_date" class="form-control"
                               value="{{ employee.national_id_issue_date or '' }}">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">مكان صدورها</label>
                        <select name="national_id_issue_place_id" class="form-select">
                            <option value="">اختر مكان الصدور</option>
                            {% for wilaya in wilayas %}
                            <option value="{{ wilaya.id }}" {% if employee.national_id_issue_place_id == wilaya.id %}selected{% endif %}>
                                {{ wilaya.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">رقم رخصة السياقة</label>
                        <input type="text" name="driving_license_number" class="form-control"
                               value="{{ employee.driving_license_number or '' }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">الصنف</label>
                        <select name="driving_license_category" class="form-select">
                            <option value="">اختر الصنف</option>
                            <option value="A" {% if employee.driving_license_category == 'A' %}selected{% endif %}>A - دراجة نارية</option>
                            <option value="B" {% if employee.driving_license_category == 'B' %}selected{% endif %}>B - سيارة خفيفة</option>
                            <option value="C" {% if employee.driving_license_category == 'C' %}selected{% endif %}>C - شاحنة</option>
                            <option value="D" {% if employee.driving_license_category == 'D' %}selected{% endif %}>D - حافلة</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الصدور</label>
                        <input type="date" name="driving_license_issue_date" class="form-control"
                               value="{{ employee.driving_license_issue_date or '' }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">مكان الصدور</label>
                        <select name="driving_license_issue_place_id" class="form-select">
                            <option value="">اختر مكان الصدور</option>
                            {% for wilaya in wilayas %}
                            <option value="{{ wilaya.id }}" {% if employee.driving_license_issue_place_id == wilaya.id %}selected{% endif %}>
                                {{ wilaya.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم بطاقة التعاضدية</label>
                        <input type="text" name="mutual_card_number" class="form-control"
                               value="{{ employee.mutual_card_number or '' }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ صدورها</label>
                        <input type="date" name="mutual_card_issue_date" class="form-control"
                               value="{{ employee.mutual_card_issue_date or '' }}">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الوضعية اتجاه الخدمة الوطنية</label>
                        <select name="military_service_status_id" class="form-select" onchange="toggleMilitaryEndDate()">
                            <option value="">اختر الوضعية</option>
                            {% for status in military_statuses %}
                            <option value="{{ status.id }}" {% if employee.military_service_status_id == status.id %}selected{% endif %}>
                                {{ status.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6" id="military_end_date_group">
                    <div class="mb-3">
                        <label class="form-label">إلى غاية (للمؤجل)</label>
                        <input type="date" name="military_service_end_date" class="form-control"
                               value="{{ employee.military_service_end_date or '' }}">
                        <div class="form-text">يظهر فقط إذا كانت الحالة "مؤجل"</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="card">
        <div class="card-body text-center">
            <button type="submit" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-save me-2"></i>حفظ التعديلات
            </button>
            <a href="{{ url_for('employee_detail', employee_id=employee.id) }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </div>
</form>

<style>
    /* تخصيص قائمة حالات الموظفين */
    .employee-status-select {
        background-color: #e8f4fd !important;
        border: 2px solid #17a2b8 !important;
        color: #0c5460 !important;
        font-weight: 600 !important;
    }
    
    .employee-status-select:focus {
        background-color: #d1ecf1 !important;
        border-color: #0c5460 !important;
        box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25) !important;
    }
    
    .employee-status-select option {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 500;
        padding: 0.5rem;
    }
    
    .employee-status-select option:hover {
        background-color: #e9ecef;
    }
    
    .employee-status-select option[data-color] {
        font-weight: 600;
    }
</style>

<script>
// تحديث الرتب عند تغيير السلك
function updateRanks(corpsSelect, rankSelect, selectedRankId = null) {
    const corpsId = corpsSelect.value;

    // مسح الرتب الحالية
    rankSelect.innerHTML = '<option value="">اختر الرتبة</option>';

    if (corpsId) {
        fetch(`/api/ranks/${corpsId}`)
            .then(response => response.json())
            .then(ranks => {
                ranks.forEach(rank => {
                    const option = document.createElement('option');
                    option.value = rank.id;
                    option.textContent = rank.name;
                    if (selectedRankId && rank.id == selectedRankId) {
                        option.selected = true;
                    }
                    rankSelect.appendChild(option);
                });
            })
            .catch(error => console.error('خطأ في جلب الرتب:', error));
    }
}

// إظهار/إخفاء تاريخ انتهاء الخدمة العسكرية للمؤجل
function toggleMilitaryEndDate() {
    const militarySelect = document.querySelector('select[name="military_service_status_id"]');
    const endDateGroup = document.getElementById('military_end_date_group');
    const selectedOption = militarySelect.options[militarySelect.selectedIndex];

    if (selectedOption && selectedOption.text.includes('مؤجل')) {
        endDateGroup.style.display = 'block';
    } else {
        endDateGroup.style.display = 'none';
    }
}

// تحميل حالات الموظفين
function loadEmployeeStatuses() {
    const statusSelect = document.getElementById('employee_status');
    const currentStatus = '{{ employee.status or "" }}';
    
    fetch('/api/employee_statuses')
        .then(response => response.json())
        .then(statuses => {
            statusSelect.innerHTML = '<option value="">اختر حالة الموظف</option>';
            statuses.forEach(status => {
                if (status.is_active) {
                    const option = document.createElement('option');
                    option.value = status.name;
                    option.textContent = status.name;
                    option.setAttribute('data-color', status.color);
                    option.style.color = status.color;
                    option.style.fontWeight = '600';
                    
                    if (currentStatus && status.name === currentStatus) {
                        option.selected = true;
                    }
                    
                    statusSelect.appendChild(option);
                }
            });
        })
        .catch(error => {
            console.error('خطأ في جلب حالات الموظفين:', error);
            // في حالة الخطأ، استخدم الحالات الافتراضية
            const defaultStatuses = ['نشط', 'معلق', 'متقاعد', 'مستقيل', 'متوفي'];
            statusSelect.innerHTML = '<option value="">اختر حالة الموظف</option>';
            defaultStatuses.forEach(status => {
                const option = document.createElement('option');
                option.value = status;
                option.textContent = status;
                
                if (currentStatus && status === currentStatus) {
                    option.selected = true;
                }
                
                statusSelect.appendChild(option);
            });
        });
}

// تحميل البلديات عند تحميل الصفحة إذا كانت الولاية محددة
document.addEventListener('DOMContentLoaded', function() {
    const wilayaSelect = document.querySelector('select[name="birth_wilaya_id"]');
    const communeSelect = document.getElementById('birth_commune_id');

    if (wilayaSelect.value) {
        updateCommunes(wilayaSelect, communeSelect, {{ employee.birth_commune_id or 'null' }});
    }

    // تحميل الرتب إذا كان السلك محدد
    const corpsSelect = document.querySelector('select[name="corps_id"]');
    const rankSelect = document.getElementById('current_rank_id');

    if (corpsSelect.value) {
        updateRanks(corpsSelect, rankSelect, {{ employee.current_rank_id or 'null' }});
    }

    // تحميل حالات الموظفين
    loadEmployeeStatuses();

    toggleChildrenCount();
    toggleMilitaryEndDate();
});

// معالجة تغيير حالة الموظف
function handleStatusChange() {
    const statusSelect = document.getElementById('employee_status');
    const selectedStatus = statusSelect.value;
    const currentStatus = '{{ employee.status or "" }}';

    // إذا لم تتغير الحالة، لا نفعل شيء
    if (selectedStatus === currentStatus || !selectedStatus) {
        return;
    }

    // تأكيد التغيير
    if (!confirm(`هل أنت متأكد من تغيير حالة الموظف إلى "${selectedStatus}"؟\nسيتم فتح نافذة لإدخال تفاصيل الحالة الجديدة.`)) {
        // إعادة تعيين القيمة السابقة
        statusSelect.value = currentStatus;
        return;
    }

    // فتح النافذة المناسبة حسب نوع الحالة
    openStatusModal(selectedStatus);
}

// فتح نافذة الحالة المناسبة
function openStatusModal(status) {
    const employeeId = {{ employee.id }};
    let modalUrl = '';

    // تحديد الرابط المناسب حسب نوع الحالة
    switch(status) {
        case 'مستودع':
            modalUrl = `/employee_status/leave_of_absence/add/${employeeId}`;
            break;
        case 'متوفى':
            modalUrl = `/employee_status/death/add/${employeeId}`;
            break;
        case 'مستقيل':
            modalUrl = `/employee_status/resignation/add/${employeeId}`;
            break;
        case 'متقاعد':
            modalUrl = `/employee_status/retirement/add/${employeeId}`;
            break;
        case 'موقوف':
            modalUrl = `/employee_status/suspension/add/${employeeId}`;
            break;
        case 'محول خارجياً':
            modalUrl = `/employee_status/external_transfer/add/${employeeId}`;
            break;
        case 'منتدب':
            modalUrl = `/employee_status/assignment/add/${employeeId}`;
            break;
        case 'في عطلة طويلة الأمد':
            modalUrl = `/employee_status/long_term_leave/add/${employeeId}`;
            break;
        default:
            // للحالات الأخرى، نحفظ التغيير مباشرة
            saveStatusChange(status);
            return;
    }

    // فتح النافذة في تبويب جديد
    if (modalUrl) {
        window.open(modalUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    }
}

// حفظ تغيير الحالة مباشرة (للحالات البسيطة)
function saveStatusChange(status) {
    const employeeId = {{ employee.id }};

    fetch(`/employees/${employeeId}/update_status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: status,
            change_date: new Date().toISOString().split('T')[0]
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('تم تحديث حالة الموظف بنجاح');
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
            // إعادة تعيين القيمة السابقة
            document.getElementById('employee_status').value = '{{ employee.status or "" }}';
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في تحديث الحالة');
        // إعادة تعيين القيمة السابقة
        document.getElementById('employee_status').value = '{{ employee.status or "" }}';
    });
}

// تحديث البلديات مع تحديد البلدية المحفوظة
function updateCommunes(wilayaSelect, communeSelect, selectedCommuneId = null) {
    const wilayaId = wilayaSelect.value;
    
    // مسح البلديات الحالية
    communeSelect.innerHTML = '<option value="">اختر البلدية</option>';
    
    if (wilayaId) {
        fetch(`/api/communes/${wilayaId}`)
            .then(response => response.json())
            .then(communes => {
                communes.forEach(commune => {
                    const option = document.createElement('option');
                    option.value = commune.id;
                    option.textContent = commune.name;
                    if (selectedCommuneId && commune.id == selectedCommuneId) {
                        option.selected = true;
                    }
                    communeSelect.appendChild(option);
                });
            })
            .catch(error => console.error('خطأ في جلب البلديات:', error));
    }
}

// إخفاء/إظهار عدد الأطفال حسب الحالة العائلية
function toggleChildrenCount() {
    const maritalStatus = document.getElementById('marital_status');
    const childrenGroup = document.getElementById('children_group');
    
    if (maritalStatus.value === 'أعزب') {
        childrenGroup.style.display = 'none';
        document.getElementById('children_count').value = 0;
    } else {
        childrenGroup.style.display = 'block';
    }
}

// معاينة الصورة قبل الرفع
function previewPhoto(input) {
    const preview = document.getElementById('photo_preview');
    const placeholder = document.getElementById('upload_placeholder');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            placeholder.style.display = 'none';
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// التحقق من صحة النموذج
document.getElementById('employeeForm').addEventListener('submit', function(e) {
    const registrationNumber = document.querySelector('input[name="registration_number"]').value;
    const firstName = document.querySelector('input[name="first_name"]').value;
    const lastName = document.querySelector('input[name="last_name"]').value;
    const gender = document.querySelector('select[name="gender"]').value;
    
    if (!registrationNumber || !firstName || !lastName || !gender) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة (رقم التسجيل، الاسم، اللقب، الجنس)');
        return false;
    }
    
    if (!/^\d{6}$/.test(registrationNumber)) {
        e.preventDefault();
        alert('رقم التسجيل يجب أن يكون 6 أرقام فقط');
        return false;
    }
    
    // التحقق من العمر
    const birthDate = document.querySelector('input[name="birth_date"]').value;
    if (birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        const age = today.getFullYear() - birth.getFullYear();
        
        if (age < 19 || age > 65) {
            e.preventDefault();
            alert('عمر الموظف يجب أن يكون بين 19 و 65 سنة');
            return false;
        }
    }
});
</script>
{% endblock %}
