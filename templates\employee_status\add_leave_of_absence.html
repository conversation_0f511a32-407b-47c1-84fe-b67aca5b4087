{% extends "base.html" %}

{% block title %}إضافة استيداع - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-clock"></i>
                        إضافة استيداع
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user"></i> معلومات الموظف:</h6>
                        <p class="mb-1"><strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                        <p class="mb-1"><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p class="mb-0"><strong>الحالة الحالية:</strong> {{ employee.status }}</p>
                    </div>

                    <!-- رصيد الاستيداع -->
                    <div class="alert alert-warning" id="leave-balance">
                        <h6><i class="fas fa-hourglass-half"></i> رصيد الاستيداع:</h6>
                        <p class="mb-0">جاري التحقق من الرصيد...</p>
                    </div>

                    <form method="POST">
                        <div class="row">
                            <!-- الفترة -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="period_info">
                                        <i class="fas fa-list-ol"></i>
                                        الفترة
                                    </label>
                                    <input type="text" class="form-control" id="period_info" 
                                           value="سيتم تحديدها تلقائياً" readonly>
                                    <small class="form-text text-muted">
                                        يتم حساب رقم الفترة تلقائياً حسب عدد فترات الاستيداع السابقة
                                    </small>
                                </div>
                            </div>

                            <!-- المدة -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="duration_months">
                                        <i class="fas fa-calendar-alt"></i>
                                        المدة (بالأشهر) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="duration_months" 
                                           name="duration_months" min="1" max="60" required>
                                    <small class="form-text text-muted">
                                        الحد الأقصى 60 شهر (5 سنوات) في الحياة الوظيفية
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- سبب الاستيداع -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="reason_id">
                                        <i class="fas fa-question-circle"></i>
                                        سبب الاستيداع <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="reason_id" name="reason_id" required>
                                        <option value="">اختر السبب...</option>
                                        {% for reason in reasons %}
                                        <option value="{{ reason.id }}">{{ reason.reason }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <!-- تاريخ البداية -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="start_date">
                                        <i class="fas fa-calendar-plus"></i>
                                        تاريخ بداية الاستيداع <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="start_date" 
                                           name="start_date" required>
                                </div>
                            </div>
                        </div>

                        <!-- تاريخ النهاية المحسوب -->
                        <div class="form-group">
                            <label for="calculated_end_date">
                                <i class="fas fa-calendar-minus"></i>
                                تاريخ نهاية الاستيداع (محسوب تلقائياً)
                            </label>
                            <input type="text" class="form-control" id="calculated_end_date" 
                                   readonly placeholder="سيتم حسابه عند اختيار التاريخ والمدة">
                        </div>

                        <div class="row">
                            <!-- رقم المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">
                                        <i class="fas fa-file-alt"></i>
                                        رقم المقرر/الوثيقة
                                    </label>
                                    <input type="text" class="form-control" id="decision_number" 
                                           name="decision_number">
                                </div>
                            </div>

                            <!-- تاريخ المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">
                                        <i class="fas fa-calendar"></i>
                                        تاريخ المقرر/الوثيقة
                                    </label>
                                    <input type="date" class="form-control" id="decision_date" 
                                           name="decision_date">
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i>
                                إضافة الاستيداع
                            </button>
                            <a href="{{ url_for('employees') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // جلب رصيد الاستيداع
    fetch(`/employee_status/api/employee/{{ employee.id }}/leave_balance`)
        .then(response => response.json())
        .then(data => {
            const balanceDiv = document.getElementById('leave-balance');
            if (data.error) {
                balanceDiv.innerHTML = `
                    <h6><i class="fas fa-exclamation-triangle"></i> خطأ في جلب الرصيد:</h6>
                    <p class="mb-0 text-danger">${data.error}</p>
                `;
            } else {
                balanceDiv.innerHTML = `
                    <h6><i class="fas fa-hourglass-half"></i> رصيد الاستيداع:</h6>
                    <p class="mb-1"><strong>المستخدم:</strong> ${data.total_used_months} شهر</p>
                    <p class="mb-0"><strong>المتبقي:</strong> ${data.remaining_months} شهر (${data.remaining_years.toFixed(1)} سنة)</p>
                `;
                
                // تحديث الحد الأقصى للمدة
                document.getElementById('duration_months').max = data.remaining_months;
            }
        })
        .catch(error => {
            console.error('خطأ في جلب رصيد الاستيداع:', error);
        });

    // حساب تاريخ النهاية عند تغيير التاريخ أو المدة
    function calculateEndDate() {
        const startDate = document.getElementById('start_date').value;
        const duration = document.getElementById('duration_months').value;
        
        if (startDate && duration) {
            const start = new Date(startDate);
            const end = new Date(start);
            end.setMonth(end.getMonth() + parseInt(duration));
            
            document.getElementById('calculated_end_date').value = 
                end.toISOString().split('T')[0];
        }
    }

    document.getElementById('start_date').addEventListener('change', calculateEndDate);
    document.getElementById('duration_months').addEventListener('input', calculateEndDate);
});
</script>
{% endblock %}