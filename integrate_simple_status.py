#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دمج النظام البسيط لحالات الموظفين مع التطبيق الرئيسي
Integrate Simple Status System with Main App
"""

def integrate_simple_status_system():
    """دمج النظام البسيط مع التطبيق الرئيسي"""
    print("🔗 دمج النظام البسيط لحالات الموظفين")
    print("=" * 60)
    
    # 1. إنشاء النظام البسيط
    print("1️⃣  إنشاء النظام البسيط...")
    from simple_status_system import create_simple_status_tables, SimpleStatusManager
    
    if create_simple_status_tables():
        print("✅ تم إنشاء جداول النظام البسيط")
    else:
        print("❌ فشل في إنشاء الجداول")
        return False
    
    # 2. تحديث التطبيق الرئيسي
    print("2️⃣  تحديث app.py...")
    update_main_app()
    
    # 3. إضافة أزرار الحالات لقائمة الموظفين
    print("3️⃣  تحديث قالب قائمة الموظفين...")
    update_employees_template()
    
    # 4. اختبار النظام
    print("4️⃣  اختبار النظام...")
    test_integration()
    
    print("✅ تم الدمج بنجاح!")
    return True

def update_main_app():
    """تحديث ملف app.py الرئيسي"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة الاستيراد
        import_line = "from simple_status_routes import register_simple_status_routes"
        
        if import_line not in content:
            # البحث عن مكان الاستيراد
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "from flask import Flask" in line:
                    lines.insert(i + 1, import_line)
                    break
            content = '\n'.join(lines)
        
        # إضافة تسجيل المسارات
        register_line = "register_simple_status_routes(app)"
        
        if register_line not in content:
            # البحث عن مكان التسجيل
            main_position = content.find('if __name__ == "__main__"')
            if main_position != -1:
                content = content[:main_position] + f"\n# تسجيل مسارات حالات الموظفين البسيطة\n{register_line}\n\n" + content[main_position:]
        
        # حفظ الملف المحدث
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث app.py")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث app.py: {e}")

def update_employees_template():
    """تحديث قالب قائمة الموظفين"""
    template_path = 'templates/employees/list.html'
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة أزرار الحالات البسيطة
        status_buttons = '''
                                    <!-- أزرار حالات الموظفين -->
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-info dropdown-toggle" 
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fas fa-user-cog"></i> الحالات
                                        </button>
                                        <div class="dropdown-menu">
                                            <h6 class="dropdown-header">الحالات المؤقتة</h6>
                                            <a class="dropdown-item" href="{{ url_for('simple_status.add_leave_of_absence', employee_id=employee.id) }}">
                                                <i class="fas fa-user-clock text-warning"></i> استيداع
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <h6 class="dropdown-header">الحالات النهائية</h6>
                                            <a class="dropdown-item" href="{{ url_for('simple_status.add_death', employee_id=employee.id) }}"
                                               onclick="return confirm('هل أنت متأكد من تسجيل وفاة هذا الموظف؟ سيتم نقله لجدول منفصل.')">
                                                <i class="fas fa-cross text-dark"></i> وفاة
                                            </a>
                                        </div>
                                    </div>
        '''
        
        # البحث عن مكان إضافة الأزرار
        if 'أزرار حالات الموظفين' not in content:
            # البحث عن أزرار الإجراءات الموجودة
            action_pattern = 'class="btn btn-sm btn-primary"'
            if action_pattern in content:
                # إضافة الأزرار قبل أزرار التحرير
                content = content.replace(
                    '<a href="{{ url_for(\'view_employee\', id=employee.id) }}" class="btn btn-sm btn-primary">',
                    status_buttons + '\n                                    <a href="{{ url_for(\'view_employee\', id=employee.id) }}" class="btn btn-sm btn-primary">'
                )
        
        # حفظ الملف المحدث
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث قالب قائمة الموظفين")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث القالب: {e}")

def add_navigation_menu():
    """إضافة قائمة التنقل"""
    template_path = 'templates/base.html'
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة عنصر قائمة جديد
        menu_item = '''
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('simple_status.dashboard') }}">
                                <i class="fas fa-users-cog"></i>
                                حالات الموظفين
                            </a>
                        </li>
        '''
        
        # البحث عن مكان إضافة القائمة
        if 'حالات الموظفين' not in content:
            # البحث عن قائمة الموظفين الموجودة
            employees_menu = 'href="{{ url_for(\'employees\') }}"'
            if employees_menu in content:
                # إضافة القائمة الجديدة بعد قائمة الموظفين
                content = content.replace(
                    '</li>\n                    </ul>',
                    menu_item + '\n                    </ul>'
                )
        
        # حفظ الملف
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة قائمة التنقل")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة قائمة التنقل: {e}")

def test_integration():
    """اختبار التكامل"""
    try:
        from simple_status_system import SimpleStatusManager
        
        manager = SimpleStatusManager()
        stats = manager.get_statistics()
        
        print("📊 إحصائيات النظام المدمج:")
        print(f"   النشطين: {stats.get('active', 0)}")
        print(f"   المستودعين: {stats.get('leave_of_absence', 0)}")
        print(f"   المتوفين: {stats.get('deceased', 0)}")
        print(f"   الإجمالي: {stats.get('grand_total', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_integration_summary():
    """إنشاء ملخص التكامل"""
    summary = """
# 🎯 ملخص دمج النظام البسيط لحالات الموظفين

## ✅ ما تم إنجازه:

### 1. 📊 النظام الأساسي:
- ✅ إنشاء 13 جدول جديد لإدارة الحالات
- ✅ تصنيف الحالات إلى مؤقتة ونهائية
- ✅ نظام تتبع تاريخي

### 2. 🔄 الحالات المؤقتة (تبقى في جدول الموظفين):
- ✅ الاستيداع (مع حساب الرصيد 5 سنوات)
- ✅ التوقيف
- ✅ الاستقالة
- ✅ الخدمة الوطنية
- ✅ عطلة طويلة الأمد
- ✅ الانتداب
- ✅ الدراسة/التكوين
- ✅ العزل

### 3. 🗑️ الحالات النهائية (تُنقل لجداول منفصلة):
- ✅ الوفاة → جدول deceased_employees
- ✅ التحويل الخارجي → جدول external_transfers
- ✅ العزل → جدول dismissed_employees (للمحذوفين نهائياً)
- ✅ التقاعد → جدول retired_employees

### 4. 🌐 الواجهات:
- ✅ لوحة تحكم بسيطة
- ✅ نموذج إضافة استيداع
- ✅ نموذج إضافة وفاة
- ✅ عرض قائمة المتوفين

### 5. 📈 الإحصائيات:
- ✅ إحصائيات مفصلة لكل حالة
- ✅ فصل بين النشطين والمحذوفين
- ✅ API للبيانات

## 🚀 للاستخدام:

### 1. تشغيل النظام:
```bash
python integrate_simple_status.py
python app.py
```

### 2. الوصول للنظام:
- الصفحة الرئيسية: http://localhost:5000/
- لوحة الحالات: http://localhost:5000/status/
- المتوفين: http://localhost:5000/status/deceased

### 3. الميزات الرئيسية:
- ✅ حساب رصيد الاستيداع تلقائياً (5 سنوات كحد أقصى)
- ✅ نقل الموظفين المتوفين لجدول منفصل
- ✅ الاحتفاظ بجميع البيانات في JSON
- ✅ تتبع تاريخ التغييرات
- ✅ إحصائيات دقيقة

## 📋 الملفات المنشأة:
1. simple_status_system.py - النظام الأساسي
2. simple_status_routes.py - المسارات والواجهات
3. templates/simple_status/ - القوالب
4. integrate_simple_status.py - ملف التكامل

## 🎉 النظام جاهز للاستخدام!

### 📝 الحقول المطلوبة لكل حالة:

#### 1. الاستيداع:
- الفترة (تُحسب تلقائياً)
- المدة بالأشهر (الحد الأقصى 60 شهر)
- سبب الاستيداع (من قائمة قابلة للتعديل)
- تاريخ بداية الاستيداع
- تاريخ نهاية الاستيداع (محسوب تلقائياً)
- رقم المقرر/الوثيقة
- تاريخ المقرر/الوثيقة

#### 2. الوفاة:
- تاريخ الوفاة
- السبب (عادية - حادث عمل)
- رقم شهادة الوفاة

### 🔄 آلية العمل:
1. **الحالات المؤقتة**: تُحدث حالة الموظف في جدول employees
2. **الحالات النهائية**: تنقل الموظف لجدول منفصل وتحذفه من جدول employees
3. **الإحصائيات**: تحسب النشطين من جدول employees والمحذوفين من الجداول المنفصلة
4. **التاريخ**: يُسجل كل تغيير في جدول status_history

## 🎯 النظام مكتمل وجاهز للاستخدام الفوري!
"""
    
    with open('SIMPLE_STATUS_INTEGRATION.md', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print("✅ تم إنشاء ملخص التكامل")

def main():
    """الدالة الرئيسية"""
    print("🌟 دمج النظام البسيط لحالات الموظفين")
    print("=" * 70)
    
    try:
        # تنفيذ التكامل
        if integrate_simple_status_system():
            
            # إضافة قائمة التنقل
            add_navigation_menu()
            
            # إنشاء ملخص التكامل
            create_integration_summary()
            
            print(f"\n🎉 تم الدمج بنجاح!")
            print(f"🚀 لتشغيل النظام:")
            print(f"   python app.py")
            
            print(f"\n🌐 الروابط المهمة:")
            print(f"   - لوحة الحالات: http://localhost:5000/status/")
            print(f"   - إضافة استيداع: http://localhost:5000/status/leave_of_absence/add/[employee_id]")
            print(f"   - إضافة وفاة: http://localhost:5000/status/death/add/[employee_id]")
            print(f"   - المتوفين: http://localhost:5000/status/deceased")
            
            print(f"\n📋 الميزات الجاهزة:")
            print(f"   ✅ إدارة الاستيداع مع حساب الرصيد")
            print(f"   ✅ إدارة الوفيات مع النقل للجدول المنفصل")
            print(f"   ✅ إحصائيات شاملة")
            print(f"   ✅ واجهات سهلة الاستخدام")
            print(f"   ✅ أزرار في قائمة الموظفين")
            
        else:
            print("❌ فشل في التكامل")
            
    except Exception as e:
        print(f"❌ خطأ في التكامل: {e}")

if __name__ == "__main__":
    main()