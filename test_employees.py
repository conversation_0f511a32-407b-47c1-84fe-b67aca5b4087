#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لوظائف الموظفين
"""

import requests
import json

def test_employee_actions():
    """اختبار إجراءات الموظفين"""
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار إجراءات الموظفين...")
    
    # اختبار API الحالات
    try:
        response = requests.get(f"{base_url}/api/employee_statuses")
        if response.status_code == 200:
            statuses = response.json()
            print(f"✅ API الحالات يعمل - تم العثور على {len(statuses)} حالة")
            for status in statuses[:3]:  # عرض أول 3 حالات
                print(f"   - {status['name']}: {status['color']}")
        else:
            print(f"❌ API الحالات لا يعمل - كود الخطأ: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API الحالات: {e}")
    
    # اختبار صفحة قائمة الموظفين
    try:
        response = requests.get(f"{base_url}/employees")
        if response.status_code == 200:
            print("✅ صفحة قائمة الموظفين تعمل")
        else:
            print(f"❌ صفحة قائمة الموظفين لا تعمل - كود الخطأ: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بصفحة الموظفين: {e}")
    
    # اختبار صفحة إضافة موظف
    try:
        response = requests.get(f"{base_url}/add_employee")
        if response.status_code == 200:
            print("✅ صفحة إضافة موظف تعمل")
        else:
            print(f"❌ صفحة إضافة موظف لا تعمل - كود الخطأ: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بصفحة إضافة موظف: {e}")
    
    print("\n📋 ملخص الاختبار:")
    print("   - تم إضافة مسارات التعديل والحذف والطباعة")
    print("   - تم إنشاء قالب الطباعة")
    print("   - تم إضافة API للحالات والبلديات")
    print("   - تم تصحيح مراجع JavaScript")
    
    print("\n🎯 الإجراءات المتاحة الآن:")
    print("   👁️  عرض التفاصيل: /employee/<id>")
    print("   ✏️  تعديل البيانات: /employees/<id>/edit")
    print("   🖨️  طباعة البيانات: /employees/<id>/print")
    print("   🗑️  حذف الموظف: /employees/<id>/delete (POST)")

if __name__ == "__main__":
    test_employee_actions()