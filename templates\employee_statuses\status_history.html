{% extends "base.html" %}

{% block title %}تاريخ حالات الموظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}تاريخ حالات الموظف{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4 info-card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user me-2"></i>{{ employee.first_name }} {{ employee.last_name }}
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 150px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 150px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الرتبة:</strong> {{ employee.rank_name or 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>السلك:</strong> {{ employee.corps_name or 'غير محدد' }}</p>
                        <p><strong>المصلحة:</strong> {{ employee.service_name or 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>الحالة الحالية:</strong></p>
                        <span class="status-badge {{ 'active' if employee.status == 'نشط' else 'suspended' if employee.status == 'معلق' else 'deceased' if employee.status == 'متوفي' else 'leave' if 'استيداع' in (employee.status or '') else 'assigned' if employee.status == 'منتدب' else 'resigned' if employee.status == 'مستقيل' else 'transferred' if employee.status == 'تحويل' else 'retired' if employee.status == 'متقاعد' else 'suspended' }}">
                            {{ employee.status or 'غير محدد' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="{{ url_for('employees_view', employee_id=employee.id) }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>العودة لملف الموظف
                </a>
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-info">
                    <i class="fas fa-exchange-alt me-2"></i>تغيير الحالة
                </a>
            </div>
            <div>
                <button class="btn btn-outline-primary" onclick="printHistory()">
                    <i class="fas fa-print me-2"></i>طباعة التاريخ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- تاريخ الحالات -->
<div class="row">
    <!-- الوفاة -->
    {% if death_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-times-circle me-2"></i>سجل الوفاة
                </h6>
            </div>
            <div class="card-body">
                {% for record in death_records %}
                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                    <div>
                        <strong>تاريخ الوفاة:</strong> {{ record.death_date }}<br>
                        <strong>السبب:</strong> {{ record.cause }}<br>
                        {% if record.certificate_number %}
                        <strong>رقم الشهادة:</strong> {{ record.certificate_number }}
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-danger">متوفي</span><br>
                        <small class="text-muted">{{ record.created_at }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- الاستيداع -->
    {% if leave_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-pause me-2"></i>سجل الاستيداع
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-status">
                        <thead>
                            <tr>
                                <th>الفترة</th>
                                <th>المدة</th>
                                <th>السبب</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in leave_records %}
                            <tr>
                                <td>{{ record.period_number }}</td>
                                <td>{{ record.duration_months }} شهر</td>
                                <td>{{ record.reason }}</td>
                                <td>{{ record.start_date }}</td>
                                <td>{{ record.end_date }}</td>
                                <td><span class="badge bg-warning">{{ record.status }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- التوقيف -->
    {% if suspension_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-stop-circle me-2"></i>سجل التوقيف
                </h6>
            </div>
            <div class="card-body">
                {% for record in suspension_records %}
                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                    <div>
                        <strong>تاريخ التوقيف:</strong> {{ record.suspension_date }}<br>
                        <strong>السبب:</strong> {{ record.reason }}<br>
                        {% if record.decision_number %}
                        <strong>رقم المقرر:</strong> {{ record.decision_number }}
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-danger">موقف</span><br>
                        <small class="text-muted">{{ record.created_at }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- الانتداب -->
    {% if assignment_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-user-tie me-2"></i>سجل الانتداب
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-status">
                        <thead>
                            <tr>
                                <th>تاريخ الانتداب</th>
                                <th>المدة</th>
                                <th>المكان</th>
                                <th>السبب</th>
                                <th>تاريخ الانتهاء</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in assignment_records %}
                            <tr>
                                <td>{{ record.assignment_date }}</td>
                                <td>{{ record.duration_months }} شهر</td>
                                <td>{{ record.location }}</td>
                                <td>{{ record.reason }}</td>
                                <td>{{ record.end_date }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- الاستقالة -->
    {% if resignation_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-sign-out-alt me-2"></i>سجل الاستقالة
                </h6>
            </div>
            <div class="card-body">
                {% for record in resignation_records %}
                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                    <div>
                        <strong>تاريخ الطلب:</strong> {{ record.request_date }}<br>
                        <strong>السبب:</strong> {{ record.reason }}<br>
                        <strong>الحالة:</strong> 
                        <span class="badge bg-{{ 'success' if record.is_accepted else 'warning' }}">
                            {{ 'مقبولة' if record.is_accepted else 'معلقة' }}
                        </span>
                        {% if record.decision_number %}
                        <br><strong>رقم المقرر:</strong> {{ record.decision_number }}
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-secondary">استقالة</span><br>
                        <small class="text-muted">{{ record.created_at }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- التحويل الخارجي -->
    {% if transfer_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>سجل التحويل الخارجي
                </h6>
            </div>
            <div class="card-body">
                {% for record in transfer_records %}
                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                    <div>
                        <strong>تاريخ إنهاء المهام:</strong> {{ record.end_date }}<br>
                        <strong>المديرية الجديدة:</strong> {{ record.target_directorate }}<br>
                        {% if record.decision_number %}
                        <strong>رقم المقرر:</strong> {{ record.decision_number }}
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-info">تحويل</span><br>
                        <small class="text-muted">{{ record.created_at }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- العطلة طويلة الأمد -->
    {% if long_leave_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-calendar-times me-2"></i>سجل العطل طويلة الأمد
                </h6>
            </div>
            <div class="card-body">
                {% for record in long_leave_records %}
                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                    <div>
                        <strong>تاريخ البداية:</strong> {{ record.start_date }}<br>
                        <strong>تاريخ المراجعة:</strong> {{ record.review_date }}<br>
                        <strong>الهيئة المانحة:</strong> {{ record.granting_authority }}<br>
                        {% if record.document_number %}
                        <strong>رقم الوثيقة:</strong> {{ record.document_number }}
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-warning">معلق</span><br>
                        <small class="text-muted">{{ record.created_at }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- التقاعد -->
    {% if retirement_records %}
    <div class="col-12 mb-4">
        <div class="card info-card">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">
                    <i class="fas fa-user-clock me-2"></i>سجل التقاعد
                </h6>
            </div>
            <div class="card-body">
                {% for record in retirement_records %}
                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                    <div>
                        <strong>تاريخ التقاعد:</strong> {{ record.retirement_date }}<br>
                        {% if record.decision_number %}
                        <strong>رقم المقرر:</strong> {{ record.decision_number }}<br>
                        {% endif %}
                        {% if record.retirement_card_number %}
                        <strong>رقم بطاقة التقاعد:</strong> {{ record.retirement_card_number }}
                        {% endif %}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-dark">متقاعد</span><br>
                        <small class="text-muted">{{ record.created_at }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- رسالة في حالة عدم وجود سجلات -->
    {% if not (death_records or leave_records or suspension_records or assignment_records or resignation_records or transfer_records or long_leave_records or retirement_records) %}
    <div class="col-12">
        <div class="card info-card">
            <div class="card-body text-center py-5">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد سجلات لتغيير الحالة</h5>
                <p class="text-muted">لم يتم تسجيل أي تغييرات على حالة هذا الموظف حتى الآن.</p>
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة تغيير حالة
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
function printHistory() {
    window.print();
}

// تأثيرات الانتقال
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.info-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in');
    });
});
</script>

<style>
@media print {
    .btn, .card-header .btn {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .info-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}