#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية - نسخة بسيطة
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'customs_secret_key_2024'

# إعداد قاعدة البيانات
DATABASE = 'customs_employees.db'

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """إنشاء قاعدة البيانات والجداول"""
    conn = get_db_connection()
    
    # جدول الموظفين
    conn.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            status TEXT DEFAULT 'نشط',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ تم إنشاء قاعدة البيانات بنجاح")

# الصفحة الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام إدارة موظفي الجمارك الجزائرية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    </head>
    <body>
        <div class="container mt-5">
            <div class="text-center">
                <h1 class="mb-4">🏛️ نظام إدارة موظفي الجمارك الجزائرية</h1>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="/employees" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-users me-2"></i>قائمة الموظفين
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="/add_employee" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-plus me-2"></i>إضافة موظف
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="/employee_statuses" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-chart-bar me-2"></i>حالات الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

# قائمة الموظفين
@app.route('/employees')
def employees():
    """قائمة الموظفين"""
    conn = get_db_connection()
    try:
        employees = conn.execute('SELECT * FROM employees ORDER BY id DESC').fetchall()
        employees_list = ""
        for emp in employees:
            employees_list += f'''
            <tr>
                <td>{emp['registration_number']}</td>
                <td>{emp['first_name']} {emp['last_name']}</td>
                <td><span class="badge bg-success">{emp['status']}</span></td>
            </tr>
            '''
        
        return f'''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>قائمة الموظفين</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        </head>
        <body>
            <div class="container mt-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>قائمة الموظفين</h2>
                    <div>
                        <a href="/" class="btn btn-secondary me-2">العودة للرئيسية</a>
                        <a href="/add_employee" class="btn btn-primary">إضافة موظف</a>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم التسجيل</th>
                                <th>الاسم الكامل</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {employees_list if employees_list else '<tr><td colspan="3" class="text-center">لا توجد بيانات</td></tr>'}
                        </tbody>
                    </table>
                </div>
            </div>
        </body>
        </html>
        '''
    except Exception as e:
        return f"خطأ: {e}"
    finally:
        conn.close()

# إضافة موظف
@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    """إضافة موظف جديد"""
    if request.method == 'POST':
        conn = get_db_connection()
        try:
            registration_number = request.form.get('registration_number', '').strip()
            first_name = request.form.get('first_name', '').strip()
            last_name = request.form.get('last_name', '').strip()
            
            if not registration_number or not first_name or not last_name:
                return "خطأ: جميع الحقول مطلوبة"
            
            conn.execute('''
                INSERT INTO employees (registration_number, first_name, last_name)
                VALUES (?, ?, ?)
            ''', (registration_number, first_name, last_name))
            
            conn.commit()
            return redirect('/employees')
            
        except Exception as e:
            return f"خطأ: {e}"
        finally:
            conn.close()
    
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>إضافة موظف</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h2>إضافة موظف جديد</h2>
            <form method="POST" class="mt-4">
                <div class="mb-3">
                    <label class="form-label">رقم التسجيل *</label>
                    <input type="text" name="registration_number" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">الاسم الأول *</label>
                    <input type="text" name="first_name" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">اسم العائلة *</label>
                    <input type="text" name="last_name" class="form-control" required>
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">إضافة الموظف</button>
                    <a href="/" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </body>
    </html>
    '''

# صفحة حالات الموظفين
@app.route('/employee_statuses')
def employee_statuses():
    """صفحة إدارة حالات الموظفين"""
    conn = get_db_connection()
    
    try:
        # حساب عدد الموظفين لكل حالة
        active_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'نشط'").fetchone()[0]
        assignment_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'منتدب'").fetchone()[0]
        study_count = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'في دراسة/تكوين'").fetchone()[0]
        
        return f'''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>حالات الموظفين</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
            <style>
                .status-card {{
                    background: white;
                    border-radius: 15px;
                    padding: 35px;
                    text-align: center;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                    transition: all 0.4s ease;
                    border: 2px solid transparent;
                    margin-bottom: 20px;
                }}
                .status-card:hover {{
                    transform: translateY(-10px);
                    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
                }}
                .status-icon {{
                    font-size: 4rem;
                    margin-bottom: 20px;
                }}
                .status-name {{
                    font-size: 1.5rem;
                    font-weight: bold;
                    margin-bottom: 15px;
                }}
                .status-number {{
                    font-size: 3rem;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}
                body {{
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                }}
            </style>
        </head>
        <body>
            <div class="container mt-4">
                <div class="text-center mb-5">
                    <h1 class="text-primary">🏛️ إدارة حالات الموظفين</h1>
                    <p class="text-muted">عرض إحصائيات حالات الموظفين مصنفة حسب دخولها في التعداد الحقيقي</p>
                    <a href="/" class="btn btn-secondary">العودة للرئيسية</a>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-success text-white text-center">
                        <h3>الحالات التي تدخل في التعداد الحقيقي للموظفين</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="status-card">
                                    <i class="fas fa-check-circle status-icon text-success"></i>
                                    <div class="status-name text-success">نشط</div>
                                    <div class="status-number text-success">{active_count}</div>
                                    <div class="text-muted">موظف نشط</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="status-card">
                                    <i class="fas fa-user-tie status-icon text-info"></i>
                                    <div class="status-name text-info">منتدب</div>
                                    <div class="status-number text-info">{assignment_count}</div>
                                    <div class="text-muted">موظف منتدب</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="status-card">
                                    <i class="fas fa-graduation-cap status-icon text-primary"></i>
                                    <div class="status-name text-primary">في دراسة/تكوين</div>
                                    <div class="status-number text-primary">{study_count}</div>
                                    <div class="text-muted">موظف في دراسة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header bg-warning text-dark text-center">
                        <h3>الحالات التي لا تدخل في التعداد الحقيقي للموظفين</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="status-card">
                                    <i class="fas fa-pause status-icon text-secondary"></i>
                                    <div class="status-name text-secondary">مستودع</div>
                                    <div class="status-number text-secondary">0</div>
                                    <div class="text-muted">موظف مستودع</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-card">
                                    <i class="fas fa-stop-circle status-icon text-danger"></i>
                                    <div class="status-name text-danger">موقوف</div>
                                    <div class="status-number text-danger">0</div>
                                    <div class="text-muted">موظف موقوف</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-card">
                                    <i class="fas fa-user-clock status-icon text-info"></i>
                                    <div class="status-name text-info">متقاعد</div>
                                    <div class="status-number text-info">0</div>
                                    <div class="text-muted">موظف متقاعد</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-card">
                                    <i class="fas fa-heart status-icon text-dark"></i>
                                    <div class="status-name text-dark">متوفى</div>
                                    <div class="status-number text-dark">0</div>
                                    <div class="text-muted">موظف متوفى</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''
        
    except Exception as e:
        return f"خطأ: {e}"
    finally:
        conn.close()

if __name__ == '__main__':
    # إنشاء قاعدة البيانات عند التشغيل
    init_database()
    
    print("\n" + "="*60)
    print("🚀 نظام إدارة موظفي الجمارك الجزائرية")
    print("="*60)
    print("🌐 الصفحة الرئيسية: http://localhost:5000")
    print("👥 قائمة الموظفين: http://localhost:5000/employees")
    print("➕ إضافة موظف: http://localhost:5000/add_employee")
    print("📊 حالات الموظفين: http://localhost:5000/employee_statuses")
    print("="*60)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("="*60 + "\n")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
