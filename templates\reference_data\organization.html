{% extends "base.html" %}

{% block title %}إدارة المديريات والمصالح - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة الهيكل التنظيمي{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <a href="{{ url_for('settings') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
        </a>
    </div>
</div>

<!-- المديريات -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-building me-2"></i>المديريات الجهوية</h5>
        <button class="btn btn-primary btn-sm" onclick="addDirectorate()">
            <i class="fas fa-plus me-2"></i>إضافة مديرية جديدة
        </button>
    </div>
    <div class="card-body">
        {% if directorates %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>اسم المديرية</th>
                        <th>الرمز</th>
                        <th>الولاية</th>
                        <th>عدد المصالح</th>
                        <th>عدد الموظفين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for directorate in directorates %}
                    <tr>
                        <td><strong>{{ directorate.name }}</strong></td>
                        <td>
                            <span class="badge bg-info">{{ directorate.code or 'غير محدد' }}</span>
                        </td>
                        <td>{{ directorate.wilaya_name or 'غير محدد' }}</td>
                        <td>
                            <span class="badge bg-success">{{ directorate.services_count }}</span>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ directorate.employees_count }}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض المصالح" onclick="viewServices({{ directorate.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل" onclick="editDirectorate({{ directorate.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف" onclick="deleteDirectorate({{ directorate.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-building fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مديريات مسجلة</h5>
            <button class="btn btn-primary" onclick="addDirectorate()">
                <i class="fas fa-plus me-2"></i>إضافة مديرية جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- المصالح -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-sitemap me-2"></i>المصالح</h5>
        <button class="btn btn-success btn-sm" onclick="addService()">
            <i class="fas fa-plus me-2"></i>إضافة مصلحة جديدة
        </button>
    </div>
    <div class="card-body">
        {% if services %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>اسم المصلحة</th>
                        <th>المديرية</th>
                        <th>عدد الموظفين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for service in services %}
                    <tr>
                        <td><strong>{{ service.name }}</strong></td>
                        <td>{{ service.directorate_name or 'غير محدد' }}</td>
                        <td>
                            <span class="badge bg-primary">{{ service.employees_count }}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-warning" title="تعديل" onclick="editService({{ service.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف" onclick="deleteService({{ service.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مصالح مسجلة</h5>
            <button class="btn btn-success" onclick="addService()">
                <i class="fas fa-plus me-2"></i>إضافة مصلحة جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal إضافة/تعديل مديرية -->
<div class="modal fade" id="directorateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="directorateModalTitle">
                    <i class="fas fa-building me-2"></i>إضافة مديرية جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="directorateForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المديرية <span class="required">*</span></label>
                        <input type="text" name="name" class="form-control" required placeholder="مثال: المديرية الجهوية للجمارك - الجزائر">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرمز</label>
                        <input type="text" name="code" class="form-control" placeholder="مثال: DRD-ALG">
                        <div class="form-text">رمز مختصر للمديرية</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الولاية</label>
                        <select name="wilaya_id" class="form-select">
                            <option value="">اختر الولاية</option>
                            {% for wilaya in wilayas %}
                            <option value="{{ wilaya.id }}">{{ wilaya.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ المديرية</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إضافة/تعديل مصلحة -->
<div class="modal fade" id="serviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serviceModalTitle">
                    <i class="fas fa-sitemap me-2"></i>إضافة مصلحة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="serviceForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المصلحة <span class="required">*</span></label>
                        <input type="text" name="name" class="form-control" required placeholder="مثال: مصلحة التوظيف والتكوين">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المديرية</label>
                        <select name="directorate_id" class="form-select">
                            <option value="">اختر المديرية</option>
                            {% for directorate in directorates %}
                            <option value="{{ directorate.id }}">{{ directorate.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ المصلحة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal عرض مصالح المديرية -->
<div class="modal fade" id="viewServicesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-sitemap me-2"></i>مصالح المديرية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="servicesModalBody">
                <!-- سيتم ملؤه ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addDirectorate() {
    document.getElementById('directorateModalTitle').innerHTML = '<i class="fas fa-building me-2"></i>إضافة مديرية جديدة';
    document.getElementById('directorateForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('directorateModal'));
    modal.show();
}

function editDirectorate(id) {
    document.getElementById('directorateModalTitle').innerHTML = '<i class="fas fa-building me-2"></i>تعديل المديرية';
    // يمكن إضافة تحميل بيانات المديرية للتعديل
    const modal = new bootstrap.Modal(document.getElementById('directorateModal'));
    modal.show();
}

function deleteDirectorate(id) {
    if (confirm('هل أنت متأكد من حذف هذه المديرية؟ سيتم حذف جميع المصالح المرتبطة بها.')) {
        alert('تم حذف المديرية بنجاح');
        location.reload();
    }
}

function addService() {
    document.getElementById('serviceModalTitle').innerHTML = '<i class="fas fa-sitemap me-2"></i>إضافة مصلحة جديدة';
    document.getElementById('serviceForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
    modal.show();
}

function editService(id) {
    document.getElementById('serviceModalTitle').innerHTML = '<i class="fas fa-sitemap me-2"></i>تعديل المصلحة';
    // يمكن إضافة تحميل بيانات المصلحة للتعديل
    const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
    modal.show();
}

function deleteService(id) {
    if (confirm('هل أنت متأكد من حذف هذه المصلحة؟')) {
        alert('تم حذف المصلحة بنجاح');
        location.reload();
    }
}

function viewServices(directorateId) {
    // جلب مصالح المديرية وعرضها
    fetch(`/api/services/${directorateId}`)
        .then(response => response.json())
        .then(services => {
            let content = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>اسم المصلحة</th><th>عدد الموظفين</th></tr></thead><tbody>';
            services.forEach(service => {
                content += `<tr><td>${service.name}</td><td><span class="badge bg-primary">${service.employees_count || 0}</span></td></tr>`;
            });
            content += '</tbody></table></div>';
            
            if (services.length === 0) {
                content = '<div class="text-center py-3"><i class="fas fa-sitemap fa-2x text-muted mb-2"></i><p class="text-muted">لا توجد مصالح في هذه المديرية</p></div>';
            }
            
            document.getElementById('servicesModalBody').innerHTML = content;
            const modal = new bootstrap.Modal(document.getElementById('viewServicesModal'));
            modal.show();
        })
        .catch(error => {
            console.error('خطأ في جلب المصالح:', error);
            alert('حدث خطأ في جلب بيانات المصالح');
        });
}

// معالجة نماذج الحفظ
document.getElementById('directorateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ المديرية بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('directorateModal')).hide();
    location.reload();
});

document.getElementById('serviceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ المصلحة بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('serviceModal')).hide();
    location.reload();
});
</script>
{% endblock %}
