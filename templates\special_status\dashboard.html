{% extends "base.html" %}

{% block page_title %}الحالات الخاصة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-times me-2"></i>
                        لوحة تحكم الحالات الخاصة
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إحصائيات الحالات الخاصة -->
                        <div class="col-md-4 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ stats.resignations or 0 }}</h4>
                                            <p class="mb-0">الاستقالات</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-minus fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('special_status.resignations') }}" class="text-white">
                                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card bg-dark text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ stats.deaths or 0 }}</h4>
                                            <p class="mb-0">الوفيات</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-heart fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('special_status.deaths') }}" class="text-white">
                                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ stats.retirements or 0 }}</h4>
                                            <p class="mb-0">المتقاعدين</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('special_status.retirements') }}" class="text-white">
                                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ stats.external_transfers or 0 }}</h4>
                                            <p class="mb-0">التحويلات الخارجية</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-exchange-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('special_status.external_transfers') }}" class="text-white">
                                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ stats.leave_of_absence or 0 }}</h4>
                                            <p class="mb-0">المستودعين</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('special_status.leave_of_absence') }}" class="text-white">
                                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ stats.suspensions or 0 }}</h4>
                                            <p class="mb-0">الموقوفين</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-slash fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ url_for('special_status.suspensions') }}" class="text-white">
                                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- روابط سريعة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>إجراءات سريعة</h5>
                            <div class="btn-group-vertical w-100" role="group">
                                <a href="{{ url_for('special_status.add_resignation') }}" class="btn btn-outline-danger mb-2">
                                    <i class="fas fa-user-minus me-2"></i>إضافة استقالة جديدة
                                </a>
                                <a href="{{ url_for('special_status.add_death') }}" class="btn btn-outline-dark mb-2">
                                    <i class="fas fa-heart me-2"></i>إضافة وفاة جديدة
                                </a>
                                <a href="{{ url_for('special_status.add_retirement') }}" class="btn btn-outline-info mb-2">
                                    <i class="fas fa-user-check me-2"></i>إضافة تقاعد جديد
                                </a>
                                <a href="{{ url_for('special_status.add_external_transfer') }}" class="btn btn-outline-warning mb-2">
                                    <i class="fas fa-exchange-alt me-2"></i>إضافة تحويل خارجي جديد
                                </a>
                                <a href="{{ url_for('special_status.add_leave_of_absence') }}" class="btn btn-outline-secondary mb-2">
                                    <i class="fas fa-user-clock me-2"></i>إضافة استيداع جديد
                                </a>
                                <a href="{{ url_for('special_status.add_suspension') }}" class="btn btn-outline-danger mb-2">
                                    <i class="fas fa-user-slash me-2"></i>إضافة توقيف جديد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
