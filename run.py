#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة موظفي الجمارك الجزائرية
Algerian Customs Employee Management System - Run Script
"""

import os
import sys
import subprocess

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    required_packages = [
        'flask',
        'pillow',
        'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("⚠️  المكتبات التالية مفقودة:")
        for package in missing_packages:
            print(f"   - {package}")
        
        print("\n🔧 تثبيت المكتبات المفقودة...")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")
                return False
    
    return True

def check_database():
    """التحقق من وجود قاعدة البيانات"""
    if not os.path.exists('customs_employees.db'):
        print("🔄 إنشاء قاعدة البيانات...")
        try:
            subprocess.check_call([sys.executable, 'init_database.py'])
            print("✅ تم إنشاء قاعدة البيانات")
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return False
    else:
        print("✅ قاعدة البيانات موجودة")
    
    return True

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    print("🚀 بدء تشغيل نظام إدارة موظفي الجمارك الجزائرية")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("❌ فشل في التحقق من المتطلبات")
        return
    
    # التحقق من قاعدة البيانات
    if not check_database():
        print("❌ فشل في التحقق من قاعدة البيانات")
        return
    
    # تشغيل التطبيق
    print("🌐 تشغيل الخادم...")
    print("=" * 60)
    
    try:
        import app
        print("✅ تم تحميل التطبيق بنجاح")
        print("🌐 الخادم يعمل على: http://localhost:5000")
        print("👋 اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)
        app.app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()