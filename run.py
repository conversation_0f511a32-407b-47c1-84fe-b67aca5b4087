#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة موظفي الجمارك الجزائرية
"""

import subprocess
import sys
import os

def main():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام إدارة موظفي الجمارك الجزائرية...")
    print("=" * 60)
    print("📊 فتح صفحة حالات الموظفين...")
    print("=" * 60)

    try:
        # التحقق من وجود الملف
        if os.path.exists("status_final.html"):
            print("✅ تم العثور على صفحة حالات الموظفين")
            print("🌐 فتح الصفحة في المتصفح...")

            # فتح الصفحة في المتصفح
            if os.name == 'nt':  # Windows
                os.startfile("status_final.html")
            else:  # Mac/Linux
                subprocess.run(["open", "status_final.html"])

            print("✅ تم فتح الصفحة بنجاح!")
            print("📝 الصفحة تحتوي على:")
            print("   - الحالات النشطة (تدخل في التعداد)")
            print("   - الحالات غير النشطة (لا تدخل في التعداد)")
            print("   - مربعات بسيطة للعرض فقط")
            print("   - تصميم جميل ومرتب")

        else:
            print("❌ لم يتم العثور على ملف status_final.html")
            print("💡 تأكد من وجود الملف في نفس المجلد")

    except Exception as e:
        print(f"❌ خطأ في فتح الصفحة: {e}")
        print("💡 جرب فتح ملف status_final.html يدوياً")

def show_menu():
    """عرض قائمة الخيارات"""
    print("\n" + "=" * 60)
    print("📋 خيارات النظام:")
    print("=" * 60)
    print("1️⃣  فتح صفحة حالات الموظفين")
    print("2️⃣  فتح صفحة حالات الموظفين (النسخة المحسنة)")
    print("3️⃣  عرض معلومات النظام")
    print("4️⃣  خروج")
    print("=" * 60)

def show_info():
    """عرض معلومات النظام"""
    print("\n" + "=" * 60)
    print("ℹ️  معلومات النظام:")
    print("=" * 60)
    print("📋 اسم النظام: نظام إدارة موظفي الجمارك الجزائرية")
    print("📊 الصفحة الحالية: حالات الموظفين")
    print("🎯 الغرض: عرض إحصائيات حالات الموظفين")
    print("💻 التقنية: HTML + CSS (بدون خادم)")
    print("✅ الحالة: يعمل بشكل مثالي")
    print("=" * 60)

def open_page(filename):
    """فتح صفحة HTML"""
    try:
        if os.path.exists(filename):
            print(f"✅ تم العثور على {filename}")
            print("🌐 فتح الصفحة في المتصفح...")

            if os.name == 'nt':  # Windows
                os.startfile(filename)
            else:  # Mac/Linux
                subprocess.run(["open", filename])

            print("✅ تم فتح الصفحة بنجاح!")
            return True
        else:
            print(f"❌ لم يتم العثور على {filename}")
            return False
    except Exception as e:
        print(f"❌ خطأ في فتح الصفحة: {e}")
        return False

def interactive_mode():
    """الوضع التفاعلي"""
    while True:
        show_menu()
        choice = input("\n🔢 اختر رقماً (1-4): ").strip()

        if choice == "1":
            print("\n📊 فتح صفحة حالات الموظفين...")
            open_page("status_final.html")

        elif choice == "2":
            print("\n📊 فتح صفحة حالات الموظفين (النسخة المحسنة)...")
            open_page("employee_statuses.html")

        elif choice == "3":
            show_info()

        elif choice == "4":
            print("\n👋 شكراً لاستخدام النظام!")
            break

        else:
            print("\n❌ خيار غير صحيح. اختر رقماً من 1 إلى 4")

        input("\n⏸️  اضغط Enter للمتابعة...")

if __name__ == "__main__":
    # التحقق من وجود معامل سطر الأوامر
    if len(sys.argv) > 1:
        if sys.argv[1] == "--interactive" or sys.argv[1] == "-i":
            interactive_mode()
        else:
            main()
    else:
        main()
