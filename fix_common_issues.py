#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح المشاكل الشائعة في نظام إدارة موظفي الجمارك الجزائرية
Fix Common Issues for Algerian Customs Employee Management System
"""

import os
import sqlite3
import subprocess
import sys

def fix_database_issue():
    """إصلاح مشاكل قاعدة البيانات"""
    print("🔧 إصلاح مشاكل قاعدة البيانات...")
    
    db_path = 'customs_employees.db'
    
    # حذف قاعدة البيانات القديمة إذا كانت تالفة
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='wilayas'")
            result = conn.fetchone()
            conn.close()
            
            if not result:
                print("⚠️  قاعدة البيانات تالفة، سيتم إعادة إنشاؤها...")
                os.remove(db_path)
            else:
                print("✅ قاعدة البيانات سليمة")
                return True
        except:
            print("⚠️  قاعدة البيانات تالفة، سيتم إعادة إنشاؤها...")
            os.remove(db_path)
    
    # إعادة إنشاء قاعدة البيانات
    try:
        print("🔄 إنشاء قاعدة بيانات جديدة...")
        result = subprocess.run([sys.executable, 'init_database.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            return True
        else:
            print(f"❌ فشل في إنشاء قاعدة البيانات: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def fix_missing_directories():
    """إنشاء المجلدات المفقودة"""
    print("📁 التحقق من المجلدات المطلوبة...")
    
    required_dirs = [
        'static',
        'static/css',
        'static/uploads',
        'templates',
        'templates/employees',
        'templates/employee_status'
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"✅ تم إنشاء مجلد: {dir_path}")
        else:
            print(f"✅ مجلد موجود: {dir_path}")

def fix_import_issues():
    """إصلاح مشاكل الاستيراد"""
    print("📦 التحقق من المكتبات المطلوبة...")
    
    required_packages = ['flask', 'pillow', 'werkzeug']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'pillow':
                __import__('PIL')
            else:
                __import__(package)
            print(f"✅ {package} متوفر")
        except ImportError:
            print(f"❌ {package} مفقود")
            missing_packages.append(package)
    
    if missing_packages:
        print("🔧 تثبيت المكتبات المفقودة...")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")
                return False
    
    return True

def fix_route_issues():
    """إصلاح مشاكل المسارات"""
    print("🛣️  التحقق من مشاكل المسارات...")
    
    # التحقق من وجود الملفات الأساسية
    required_files = ['app.py', 'status_integration.py']
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ ملف مفقود: {file_path}")
            return False
        else:
            print(f"✅ ملف موجود: {file_path}")
    
    return True

def run_system_check():
    """تشغيل فحص شامل للنظام"""
    print("🔍 تشغيل فحص شامل للنظام...")
    
    try:
        result = subprocess.run([sys.executable, 'check_system.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ فحص النظام نجح")
            print(result.stdout)
            return True
        else:
            print("⚠️  فحص النظام أظهر مشاكل:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ في فحص النظام: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح المشاكل"""
    print("🔧 أداة إصلاح المشاكل الشائعة")
    print("=" * 50)
    
    all_fixed = True
    
    # إصلاح المجلدات المفقودة
    fix_missing_directories()
    
    # إصلاح مشاكل الاستيراد
    if not fix_import_issues():
        all_fixed = False
    
    # إصلاح مشاكل قاعدة البيانات
    if not fix_database_issue():
        all_fixed = False
    
    # إصلاح مشاكل المسارات
    if not fix_route_issues():
        all_fixed = False
    
    # تشغيل فحص شامل
    if not run_system_check():
        all_fixed = False
    
    print("\n" + "=" * 50)
    if all_fixed:
        print("🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("🚀 يمكنك الآن تشغيل النظام: python app.py")
    else:
        print("⚠️  بعض المشاكل لم يتم إصلاحها")
        print("💡 جرب تشغيل الأداة مرة أخرى أو راجع الأخطاء أعلاه")
    
    print("=" * 50)

if __name__ == '__main__':
    main()