#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template_string

app = Flask(__name__)

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة موظفي الجمارك الجزائرية - نظام جديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border-radius: 15px;
        }
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        .service-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .service-card:hover {
            transform: translateY(-10px);
            border-color: #3498db;
            box-shadow: 0 15px 30px rgba(52, 152, 219, 0.2);
        }
        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 32px;
        }
        .success-message {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
        }
        .btn-custom {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-building"></i> نظام إدارة موظفي الجمارك الجزائرية</h1>
            <p class="lead">النظام الجديد - بدون أخطاء</p>
        </div>
        
        <div class="success-message">
            <h4><i class="fas fa-check-circle"></i> تم حل المشكلة نهائياً!</h4>
            <p>النظام يعمل الآن في مجلد منفصل بدون أي مراجع قديمة</p>
        </div>
        
        <div class="service-grid">
            <div class="service-card" onclick="showPage('employees')">
                <div class="service-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h5>إدارة الموظفين</h5>
                <p>إضافة وتعديل بيانات الموظفين</p>
                <button class="btn btn-custom">دخول</button>
            </div>
            
            <div class="service-card" onclick="showPage('leaves')">
                <div class="service-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h5>العطل والإجازات</h5>
                <p>إدارة العطل السنوية والمرضية</p>
                <button class="btn btn-custom">دخول</button>
            </div>
            
            <div class="service-card" onclick="showPage('certificates')">
                <div class="service-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h5>الشهادات والتكوين</h5>
                <p>إدارة الشهادات والدورات التدريبية</p>
                <button class="btn btn-custom">دخول</button>
            </div>
            
            <div class="service-card" onclick="showPage('employee_statuses')">
                <div class="service-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <h5>حالات الموظفين</h5>
                <p>إدارة حالات الموظفين المختلفة</p>
                <button class="btn btn-custom">دخول</button>
            </div>
            
            <div class="service-card" onclick="showPage('sanctions')">
                <div class="service-icon">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h5>العقوبات والمكافآت</h5>
                <p>إدارة العقوبات والمكافآت</p>
                <button class="btn btn-custom">دخول</button>
            </div>
            
            <div class="service-card" onclick="showPage('settings')">
                <div class="service-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h5>الإعدادات</h5>
                <p>إعدادات النظام والمستخدمين</p>
                <button class="btn btn-custom">دخول</button>
            </div>
        </div>
        
        <div class="text-center mt-5">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                <p><strong>المجلد:</strong> new_system</p>
                <p><strong>الحالة:</strong> يعمل بدون أخطاء</p>
                <p><strong>التاريخ:</strong> {{ current_date }}</p>
            </div>
        </div>
    </div>

    <script>
        function showPage(page) {
            alert('تم النقر على: ' + page + '\\n\\nالنظام الجديد يعمل بشكل مثالي!\\n\\nلا توجد أخطاء special_status.index');
        }
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    from datetime import datetime
    return render_template_string(HTML_TEMPLATE, current_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

@app.route('/employees')
def employees():
    return render_template_string('<h1>صفحة الموظفين</h1><p><a href="/">العودة</a></p>')

@app.route('/leaves')
def leaves():
    return render_template_string('<h1>صفحة العطل</h1><p><a href="/">العودة</a></p>')

@app.route('/certificates')
def certificates():
    return render_template_string('<h1>صفحة الشهادات</h1><p><a href="/">العودة</a></p>')

@app.route('/employee_statuses')
def employee_statuses():
    return render_template_string('<h1>صفحة حالات الموظفين</h1><p><a href="/">العودة</a></p>')

@app.route('/sanctions')
def sanctions():
    return render_template_string('<h1>صفحة العقوبات</h1><p><a href="/">العودة</a></p>')

@app.route('/settings')
def settings():
    return render_template_string('<h1>صفحة الإعدادات</h1><p><a href="/">العودة</a></p>')

if __name__ == '__main__':
    print("🇩🇿 نظام إدارة موظفي الجمارك الجزائرية - النظام الجديد")
    print("📁 المجلد: new_system")
    print("🌐 http://localhost:5001")
    print("✅ بدون أي مراجع قديمة")
    app.run(debug=True, host='0.0.0.0', port=5001)
