#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية - النسخة الأصلية
Algerian Customs Employee Management System - Original Version
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from simple_status_routes import register_simple_status_routes
from complete_final_status_routes import register_final_status_routes
from special_status_routes import special_status_bp
import sqlite3
import os
from datetime import datetime, date, timedelta
import re
from werkzeug.utils import secure_filename
from PIL import Image
import io
import base64

# استيراد وحدة إدارة حالات الموظفين
try:
    from employee_status_manager import EmployeeStatusManager, create_status_manager
    from status_integration import initialize_status_integration, register_template_helpers
    from employee_status_api import register_api_routes
    STATUS_MODULES_AVAILABLE = True
    print("✅ تم تحميل وحدات إدارة الحالات")
except ImportError as e:
    print(f"⚠️  تحذير: لم يتم تحميل وحدات الحالات: {e}")
    STATUS_MODULES_AVAILABLE = False

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'customs-algeria-2025'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# تهيئة وحدة إدارة حالات الموظفين إذا كانت متاحة
if STATUS_MODULES_AVAILABLE:
    try:
        status_integration = initialize_status_integration(app)
        register_template_helpers(app)
        register_api_routes(app)
        status_manager = create_status_manager()
        print("✅ تم تهيئة وحدات إدارة الحالات")
    except Exception as e:
        print(f"⚠️  خطأ في تهيئة وحدات الحالات: {e}")
        STATUS_MODULES_AVAILABLE = False

# تسجيل مسارات حالات الموظفين البسيطة
register_simple_status_routes(app)
register_final_status_routes(app)

# تسجيل مسارات الحالات الخاصة
app.register_blueprint(special_status_bp)

def get_db_connection():
    """اتصال بقاعدة البيانات"""
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    return conn

def validate_registration_number(reg_num):
    """التحقق من صحة رقم التسجيل (6 أرقام)"""
    if not reg_num or len(reg_num) != 6 or not reg_num.isdigit():
        return False
    return True

def validate_social_security_number(ssn, birth_year, gender):
    """التحقق من صحة رقم الضمان الاجتماعي الجزائري"""
    # إذا كان الحقل فارغاً، فهو صحيح (اختياري)
    if not ssn or ssn.strip() == '':
        return True
    
    # التحقق من الطول
    if len(ssn) != 15:
        return False
    
    # التحقق من أن جميع الأرقام صحيحة
    if not ssn.isdigit():
        return False
    
    # إذا لم تتوفر سنة الميلاد أو الجنس، نتحقق فقط من المفتاح
    if birth_year and gender:
        # استخراج سنة الميلاد من الرقم
        year_from_ssn = int(ssn[1:3])
        birth_year_short = int(str(birth_year)[-2:])
        if year_from_ssn != birth_year_short:
            return False
        
        # التحقق من الجنس (الرقم الأول)
        gender_digit = int(ssn[0])
        if gender == 'ذكر' and gender_digit != 1:
            return False
        elif gender == 'أنثى' and gender_digit != 2:
            return False
    
    # حساب مفتاح التحقق (الرقمان الأخيران)
    key_digits = ssn[:13]
    calculated_key = 97 - (int(key_digits) % 97)
    actual_key = int(ssn[13:15])
    
    return calculated_key == actual_key

def validate_postal_account(account_num):
    """التحقق من صحة رقم الحساب الجاري البريدي الجزائري"""
    # إذا كان الحقل فارغاً، فهو صحيح (اختياري)
    if not account_num or account_num.strip() == '':
        return True
    
    # التحقق من الطول
    if len(account_num) != 10:
        return False
    
    # التحقق من أن جميع الأرقام صحيحة
    if not account_num.isdigit():
        return False
    
    # حساب مفتاح التحقق
    account_digits = account_num[:8]
    calculated_key = 97 - (int(account_digits) % 97)
    actual_key = int(account_num[8:10])
    
    return calculated_key == actual_key

def calculate_age(birth_date):
    """حساب العمر"""
    if not birth_date:
        return None
    
    if isinstance(birth_date, str):
        birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
    
    today = date.today()
    age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
    return age

def calculate_service_years(hire_date):
    """حساب سنوات الخدمة"""
    if not hire_date:
        return None
    
    if isinstance(hire_date, str):
        hire_date = datetime.strptime(hire_date, '%Y-%m-%d').date()
    
    today = date.today()
    years = today.year - hire_date.year - ((today.month, today.day) < (hire_date.month, hire_date.day))
    return years

# إضافة الدوال للقوالب
@app.template_filter('calculate_age')
def calculate_age_filter(birth_date):
    return calculate_age(birth_date)

@app.template_filter('calculate_service_years')
def calculate_service_years_filter(hire_date):
    return calculate_service_years(hire_date)

def validate_age(birth_date):
    """التحقق من أن العمر بين 19 و 65 سنة"""
    age = calculate_age(birth_date)
    if age is None:
        return False
    return 19 <= age <= 65

def process_employee_photo(photo_file):
    """معالجة صورة الموظف"""
    if not photo_file:
        return None
    
    try:
        # فتح الصورة
        image = Image.open(photo_file)
        
        # تحويل إلى RGB إذا كانت RGBA
        if image.mode == 'RGBA':
            image = image.convert('RGB')
        
        # تغيير حجم الصورة إلى 300x400 بكسل
        image = image.resize((300, 400), Image.Resampling.LANCZOS)
        
        # حفظ الصورة في buffer
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='JPEG', quality=85)
        img_buffer.seek(0)
        
        # تحويل إلى base64
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
        return f"data:image/jpeg;base64,{img_base64}"
        
    except Exception as e:
        print(f"خطأ في معالجة الصورة: {e}")
        return None

def get_notifications():
    """حساب التنبيهات العملية والمهمة"""
    notifications = []

    try:
        conn = get_db_connection()
        today = date.today()

        # تنبيهات العطل المنتهية الصلاحية أو القريبة من الانتهاء
        try:
            expiring_leaves = conn.execute('''
                SELECT e.first_name, e.last_name, al.end_date, al.leave_type
                FROM annual_leaves al
                JOIN employees e ON al.employee_id = e.id
                WHERE al.end_date BETWEEN date('now') AND date('now', '+7 days')
                ORDER BY al.end_date
                LIMIT 3
            ''').fetchall()

            for leave in expiring_leaves:
                days_left = (datetime.strptime(leave['end_date'], '%Y-%m-%d').date() - today).days
                if days_left == 0:
                    time_text = 'ينتهي اليوم'
                    type_class = 'danger'
                elif days_left == 1:
                    time_text = 'ينتهي غداً'
                    type_class = 'warning'
                else:
                    time_text = f'ينتهي خلال {days_left} أيام'
                    type_class = 'warning'
                
                notifications.append({
                    'type': type_class,
                    'icon': 'fas fa-calendar-times',
                    'title': 'انتهاء عطلة',
                    'message': f"عطلة {leave['first_name']} {leave['last_name']} {time_text}",
                    'time': time_text
                })
        except:
            pass

        # تنبيهات الموظفين الجدد (أحدث موظفين فقط)
        try:
            recent_employees = conn.execute('''
                SELECT first_name, last_name, id
                FROM employees
                ORDER BY id DESC
                LIMIT 2
            ''').fetchall()

            for emp in recent_employees:
                notifications.append({
                    'type': 'success',
                    'icon': 'fas fa-user-plus',
                    'title': 'موظف مسجل',
                    'message': f"{emp['first_name']} {emp['last_name']}",
                    'time': 'مسجل حديثاً'
                })
        except:
            pass

        conn.close()
    except:
        pass

    # إذا لم توجد تنبيهات، أضف تنبيه واحد بسيط
    if not notifications:
        notifications.append({
            'type': 'info',
            'icon': 'fas fa-info-circle',
            'title': 'لا توجد تنبيهات جديدة',
            'message': 'جميع العمليات تسير بشكل طبيعي',
            'time': 'الآن'
        })

    return notifications[:5]  # أحدث 5 تنبيهات فقط

def get_updated_employee_stats():
    """إحصائيات محدثة للموظفين مع استبعاد المحولين خارجياً"""
    conn = get_db_connection()
    
    # إجمالي الموظفين
    stats = {}
    stats['total_employees'] = conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0]
    
    # الموظفين النشطين (باستثناء المحولين خارجياً والمتوفين والمستقيلين والمتقاعدين)
    stats['active_employees'] = conn.execute('''
        SELECT COUNT(*) FROM employees 
        WHERE status NOT IN ('محول خارجياً', 'متوفى', 'مستقيل', 'متقاعد')
    ''').fetchone()[0]
    
    # الموظفين المحولين خارجياً
    stats['external_transfers'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status = 'محول خارجياً'
    ''').fetchone()[0]
    
    # الموظفين المتوفين
    stats['deceased'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status = 'متوفى'
    ''').fetchone()[0]
    
    # الموظفين المستقيلين
    stats['resigned'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status IN ('مستقيل', 'استقالة معلقة')
    ''').fetchone()[0]
    
    # الموظفين المتقاعدين
    stats['retired'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status = 'متقاعد'
    ''').fetchone()[0]
    
    # الموظفين الموقوفين
    stats['suspended'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status = 'موقوف'
    ''').fetchone()[0]
    
    # الموظفين المستودعين
    stats['on_leave_of_absence'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status = 'مستودع'
    ''').fetchone()[0]
    
    # الموظفين في عطلة طويلة الأمد
    stats['on_long_term_leave'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status = 'في عطلة طويلة الأمد'
    ''').fetchone()[0]
    
    # الموظفين المنتدبين
    stats['on_assignment'] = conn.execute('''
        SELECT COUNT(*) FROM employees WHERE status = 'منتدب'
    ''').fetchone()[0]
    
    conn.close()
    return stats

# ================================
# المسارات الأساسية
# ================================

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    conn = get_db_connection()

    # إحصائيات عامة
    stats = {}
    stats['total_employees'] = conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0]
    stats['active_employees'] = conn.execute('SELECT COUNT(*) FROM employees WHERE status = "نشط"').fetchone()[0]
    
    try:
        stats['total_leaves'] = (
            conn.execute('SELECT COUNT(*) FROM annual_leaves').fetchone()[0] +
            conn.execute('SELECT COUNT(*) FROM sick_leaves').fetchone()[0] +
            conn.execute('SELECT COUNT(*) FROM other_leaves').fetchone()[0]
        )
        stats['active_leaves'] = conn.execute('''
            SELECT COUNT(*) FROM annual_leaves
            WHERE start_date <= date('now') AND end_date >= date('now')
        ''').fetchone()[0]
        stats['total_certificates'] = conn.execute('SELECT COUNT(*) FROM certificates').fetchone()[0]
        stats['total_training'] = conn.execute('SELECT COUNT(*) FROM training').fetchone()[0]
        stats['total_promotions'] = (
            conn.execute('SELECT COUNT(*) FROM rank_promotions').fetchone()[0] +
            conn.execute('SELECT COUNT(*) FROM grade_promotions').fetchone()[0]
        )
        stats['total_transfers'] = conn.execute('SELECT COUNT(*) FROM transfers').fetchone()[0]
        stats['total_sanctions'] = conn.execute('SELECT COUNT(*) FROM sanctions').fetchone()[0]
    except:
        # في حالة عدم وجود الجداول
        stats.update({
            'total_leaves': 0,
            'active_leaves': 0,
            'total_certificates': 0,
            'total_training': 0,
            'total_promotions': 0,
            'total_transfers': 0,
            'total_sanctions': 0
        })

    # الحصول على التنبيهات
    notifications = get_notifications()
    
    # إحصائيات إدارة الحالات إذا كانت متاحة
    status_stats = {}
    if STATUS_MODULES_AVAILABLE:
        try:
            status_stats = status_manager.get_status_statistics()
        except:
            status_stats = {}

    conn.close()

    return render_template('index.html', 
                         stats=stats, 
                         notifications=notifications,
                         status_stats=status_stats,
                         status_available=STATUS_MODULES_AVAILABLE)

@app.route('/employees')
def employees():
    """قائمة الموظفين"""
    conn = get_db_connection()
    
    # البحث والتصفية
    search = request.args.get('search', '')
    department = request.args.get('department', '')
    status = request.args.get('status', '')
    
    query = '''
        SELECT e.*, w.name as birth_wilaya_name, c.name as birth_commune_name,
               r.name as rank_name, corps.name as corps_name, s.name as service_name
        FROM employees e
        LEFT JOIN wilayas w ON e.birth_wilaya_id = w.id
        LEFT JOIN communes c ON e.birth_commune_id = c.id
        LEFT JOIN ranks r ON e.current_rank_id = r.id
        LEFT JOIN corps ON e.corps_id = corps.id
        LEFT JOIN services s ON e.current_service_id = s.id
        WHERE 1=1
    '''
    params = []
    
    if search:
        query += ' AND (e.first_name LIKE ? OR e.last_name LIKE ? OR e.registration_number LIKE ?)'
        params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])
    
    if department:
        query += ' AND s.name = ?'
        params.append(department)
    
    if status:
        query += ' AND e.status = ?'
        params.append(status)
    
    query += ' ORDER BY e.created_at DESC'
    
    employees = conn.execute(query, params).fetchall()
    
    # الحصول على قائمة الأقسام للتصفية
    departments = conn.execute('SELECT DISTINCT name FROM services ORDER BY name').fetchall()
    
    # الحصول على قائمة الحالات للتصفية
    statuses = conn.execute('SELECT DISTINCT status FROM employees WHERE status IS NOT NULL ORDER BY status').fetchall()
    
    conn.close()
    
    return render_template('employees/list.html', 
                         employees=employees,
                         departments=departments,
                         statuses=statuses,
                         search=search,
                         selected_department=department,
                         status_filter=status)

@app.route('/employee/<int:employee_id>')
def employee_detail(employee_id):
    """تفاصيل الموظف"""
    conn = get_db_connection()
    
    employee = conn.execute('''
        SELECT e.*, w.name as birth_wilaya_name, c.name as birth_commune_name,
               r.name as rank_name, corps.name as corps_name, s.name as service_name
        FROM employees e
        LEFT JOIN wilayas w ON e.birth_wilaya_id = w.id
        LEFT JOIN communes c ON e.birth_commune_id = c.id
        LEFT JOIN ranks r ON e.current_rank_id = r.id
        LEFT JOIN corps ON e.corps_id = corps.id
        LEFT JOIN services s ON e.current_service_id = s.id
        WHERE e.id = ?
    ''', (employee_id,)).fetchone()
    
    if not employee:
        flash('الموظف غير موجود', 'error')
        return redirect(url_for('employees'))
    
    conn.close()
    
    return render_template('employees/view.html', 
                         employee=employee,
                         status_available=STATUS_MODULES_AVAILABLE)

@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    """إضافة موظف جديد"""
    if request.method == 'POST':
        # جمع البيانات من النموذج
        data = {
            'registration_number': request.form.get('registration_number'),
            'first_name': request.form.get('first_name'),
            'last_name': request.form.get('last_name'),
            'birth_date': request.form.get('birth_date'),
            'birth_wilaya_id': request.form.get('birth_wilaya_id'),
            'birth_commune_id': request.form.get('birth_commune_id'),
            'gender': request.form.get('gender'),
            'social_security_number': request.form.get('social_security_number'),
            'hire_date': request.form.get('hire_date'),
            'current_rank_id': request.form.get('current_rank_id'),
            'corps_id': request.form.get('corps_id'),
            'current_service_id': request.form.get('current_service_id'),
            'postal_account': request.form.get('postal_account'),
            'phone': request.form.get('phone'),
            'email': request.form.get('email'),
            'address': request.form.get('address')
        }
        
        # التحقق من صحة البيانات
        errors = []
        
        # التحقق من رقم التسجيل (إجباري)
        if not validate_registration_number(data['registration_number']):
            errors.append('رقم التسجيل يجب أن يكون 6 أرقام')
        
        # التحقق من رقم الضمان الاجتماعي (اختياري)
        birth_year = None
        if data['birth_date']:
            birth_year = int(data['birth_date'][:4])
        
        if not validate_social_security_number(data['social_security_number'], birth_year, data['gender']):
            errors.append('رقم الضمان الاجتماعي غير صحيح')
        
        # التحقق من الحساب الجاري البريدي (اختياري)
        if not validate_postal_account(data['postal_account']):
            errors.append('رقم الحساب الجاري البريدي غير صحيح')
        
        # التحقق من العمر - يجب أن يكون بين 19 و 65 سنة
        if data['birth_date'] and not validate_age(data['birth_date']):
            errors.append('العمر يجب أن يكون بين 19 و 65 سنة')
        
        if errors:
            for error in errors:
                flash(error, 'error')
        else:
            conn = get_db_connection()
            
            try:
                # معالجة الصورة إذا تم رفعها
                photo_data = None
                if 'photo' in request.files and request.files['photo'].filename:
                    photo_file = request.files['photo']
                    photo_data = process_employee_photo(photo_file)
                
                # إدراج البيانات
                conn.execute('''
                    INSERT INTO employees (
                        registration_number, first_name, last_name, birth_date,
                        birth_wilaya_id, birth_commune_id, gender, social_security_number,
                        hire_date, current_rank_id, corps_id, current_service_id,
                        postal_account, phone, email, address, photo
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['registration_number'], data['first_name'], data['last_name'],
                    data['birth_date'], data['birth_wilaya_id'], data['birth_commune_id'],
                    data['gender'], data['social_security_number'], data['hire_date'],
                    data['current_rank_id'], data['corps_id'], data['current_service_id'],
                    data['postal_account'], data['phone'], data['email'], data['address'],
                    photo_data
                ))
                
                conn.commit()
                flash('تم إضافة الموظف بنجاح', 'success')
                return redirect(url_for('employees'))
                
            except sqlite3.IntegrityError as e:
                if 'registration_number' in str(e):
                    flash('رقم التسجيل موجود بالفعل', 'error')
                elif 'social_security_number' in str(e):
                    flash('رقم الضمان الاجتماعي موجود بالفعل', 'error')
                else:
                    flash('خطأ في إدراج البيانات', 'error')
            except Exception as e:
                flash(f'خطأ غير متوقع: {str(e)}', 'error')
            finally:
                conn.close()
    
    # الحصول على البيانات المرجعية
    conn = get_db_connection()
    
    try:
        wilayas = conn.execute('SELECT * FROM wilayas ORDER BY name').fetchall()
        ranks = conn.execute('SELECT * FROM ranks ORDER BY name').fetchall()
        corps = conn.execute('SELECT * FROM corps ORDER BY name').fetchall()
        services = conn.execute('SELECT * FROM services ORDER BY name').fetchall()
    except:
        # في حالة عدم وجود الجداول، إنشاء قوائم فارغة
        wilayas = []
        ranks = []
        corps = []
        services = []
    
    conn.close()
    
    return render_template('employees/add.html',
                         wilayas=wilayas,
                         ranks=ranks,
                         corps=corps,
                         services=services)

@app.route('/employees/<int:employee_id>/edit', methods=['GET', 'POST'])
def edit_employee(employee_id):
    """تعديل بيانات الموظف"""
    conn = get_db_connection()
    
    # التحقق من وجود الموظف
    employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
    if not employee:
        flash('الموظف غير موجود', 'error')
        return redirect(url_for('employees'))
    
    if request.method == 'POST':
        # جمع البيانات من النموذج
        data = {
            'registration_number': request.form.get('registration_number'),
            'first_name': request.form.get('first_name'),
            'last_name': request.form.get('last_name'),
            'first_name_fr': request.form.get('first_name_fr'),
            'last_name_fr': request.form.get('last_name_fr'),
            'birth_date': request.form.get('birth_date'),
            'birth_wilaya_id': request.form.get('birth_wilaya_id'),
            'birth_commune_id': request.form.get('birth_commune_id'),
            'gender': request.form.get('gender'),
            'social_security_number': request.form.get('social_security_number'),
            'marital_status': request.form.get('marital_status'),
            'children_count': request.form.get('children_count'),
            'dependents_count': request.form.get('dependents_count'),
            'blood_type': request.form.get('blood_type'),
            'sport_practiced': request.form.get('sport_practiced'),
            'hire_date': request.form.get('hire_date'),
            'current_rank_id': request.form.get('current_rank_id'),
            'corps_id': request.form.get('corps_id'),
            'current_service_id': request.form.get('current_service_id'),
            'postal_account': request.form.get('postal_account'),
            'phone1': request.form.get('phone1'),
            'phone2': request.form.get('phone2'),
            'email': request.form.get('email'),
            'address': request.form.get('address'),
            'status': request.form.get('status')
        }
        
        # التحقق من صحة البيانات
        errors = []
        
        # التحقق من رقم التسجيل (إجباري)
        if not validate_registration_number(data['registration_number']):
            errors.append('رقم التسجيل يجب أن يكون 6 أرقام')
        
        # التحقق من رقم الضمان الاجتماعي (اختياري)
        birth_year = None
        if data['birth_date']:
            birth_year = int(data['birth_date'][:4])
        
        if not validate_social_security_number(data['social_security_number'], birth_year, data['gender']):
            errors.append('رقم الضمان الاجتماعي غير صحيح')
        
        # التحقق من الحساب الجاري البريدي (اختياري)
        if not validate_postal_account(data['postal_account']):
            errors.append('رقم الحساب الجاري البريدي غير صحيح')
        
        # التحقق من العمر - يجب أن يكون بين 19 و 65 سنة
        if data['birth_date'] and not validate_age(data['birth_date']):
            errors.append('العمر يجب أن يكون بين 19 و 65 سنة')
        
        if errors:
            for error in errors:
                flash(error, 'error')
        else:
            try:
                # معالجة الصورة إذا تم رفعها
                photo_data = employee['photo']  # الاحتفاظ بالصورة الحالية
                if 'photo' in request.files and request.files['photo'].filename:
                    photo_file = request.files['photo']
                    new_photo = process_employee_photo(photo_file)
                    if new_photo:
                        photo_data = new_photo
                
                # تحديث البيانات
                conn.execute('''
                    UPDATE employees SET
                        registration_number = ?, first_name = ?, last_name = ?, first_name_fr = ?, last_name_fr = ?,
                        birth_date = ?, birth_wilaya_id = ?, birth_commune_id = ?, gender = ?, 
                        social_security_number = ?, marital_status = ?, children_count = ?, dependents_count = ?,
                        blood_type = ?, sport_practiced = ?, hire_date = ?, current_rank_id = ?, corps_id = ?, 
                        current_service_id = ?, postal_account = ?, phone1 = ?, phone2 = ?, email = ?, 
                        address = ?, photo = ?, status = ?
                    WHERE id = ?
                ''', (
                    data['registration_number'], data['first_name'], data['last_name'], 
                    data['first_name_fr'], data['last_name_fr'], data['birth_date'], 
                    data['birth_wilaya_id'], data['birth_commune_id'], data['gender'], 
                    data['social_security_number'], data['marital_status'], data['children_count'], 
                    data['dependents_count'], data['blood_type'], data['sport_practiced'], 
                    data['hire_date'], data['current_rank_id'], data['corps_id'], 
                    data['current_service_id'], data['postal_account'], data['phone1'], 
                    data['phone2'], data['email'], data['address'], photo_data, data['status'], 
                    employee_id
                ))
                
                conn.commit()
                flash('تم تحديث بيانات الموظف بنجاح', 'success')
                return redirect(url_for('employee_detail', employee_id=employee_id))
                
            except sqlite3.IntegrityError as e:
                if 'registration_number' in str(e):
                    flash('رقم التسجيل موجود بالفعل', 'error')
                elif 'social_security_number' in str(e):
                    flash('رقم الضمان الاجتماعي موجود بالفعل', 'error')
                else:
                    flash('خطأ في تحديث البيانات', 'error')
            except Exception as e:
                flash(f'خطأ غير متوقع: {str(e)}', 'error')
    
    # الحصول على البيانات المرجعية
    try:
        wilayas = conn.execute('SELECT * FROM wilayas ORDER BY name').fetchall()
        ranks = conn.execute('SELECT * FROM ranks ORDER BY name').fetchall()
        corps = conn.execute('SELECT * FROM corps ORDER BY name').fetchall()
        services = conn.execute('SELECT * FROM services ORDER BY name').fetchall()
        
        # الحصول على بيانات الموظف مع الأسماء
        employee_data = conn.execute('''
            SELECT e.*, w.name as birth_wilaya_name, c.name as birth_commune_name,
                   r.name as rank_name, corps.name as corps_name, s.name as service_name
            FROM employees e
            LEFT JOIN wilayas w ON e.birth_wilaya_id = w.id
            LEFT JOIN communes c ON e.birth_commune_id = c.id
            LEFT JOIN ranks r ON e.current_rank_id = r.id
            LEFT JOIN corps ON e.corps_id = corps.id
            LEFT JOIN services s ON e.current_service_id = s.id
            WHERE e.id = ?
        ''', (employee_id,)).fetchone()
        
    except:
        wilayas = []
        ranks = []
        corps = []
        services = []
        employee_data = employee
    
    conn.close()
    
    return render_template('employees/edit.html',
                         employee=employee_data,
                         wilayas=wilayas,
                         ranks=ranks,
                         corps=corps,
                         services=services)

@app.route('/employees/<int:employee_id>/delete', methods=['POST'])
def delete_employee(employee_id):
    """حذف الموظف"""
    conn = get_db_connection()
    
    try:
        # التحقق من وجود الموظف
        employee = conn.execute('SELECT first_name, last_name FROM employees WHERE id = ?', (employee_id,)).fetchone()
        if not employee:
            return jsonify({'success': False, 'message': 'الموظف غير موجود'})
        
        # حذف الموظف
        conn.execute('DELETE FROM employees WHERE id = ?', (employee_id,))
        conn.commit()
        
        return jsonify({
            'success': True, 
            'message': f'تم حذف الموظف {employee["first_name"]} {employee["last_name"]} بنجاح'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في حذف الموظف: {str(e)}'})
    finally:
        conn.close()

@app.route('/employees/<int:employee_id>/print')
def print_employee(employee_id):
    """طباعة بيانات الموظف"""
    conn = get_db_connection()
    
    employee = conn.execute('''
        SELECT e.*, w.name as birth_wilaya_name, c.name as birth_commune_name,
               r.name as rank_name, corps.name as corps_name, s.name as service_name
        FROM employees e
        LEFT JOIN wilayas w ON e.birth_wilaya_id = w.id
        LEFT JOIN communes c ON e.birth_commune_id = c.id
        LEFT JOIN ranks r ON e.current_rank_id = r.id
        LEFT JOIN corps ON e.corps_id = corps.id
        LEFT JOIN services s ON e.current_service_id = s.id
        WHERE e.id = ?
    ''', (employee_id,)).fetchone()
    
    if not employee:
        flash('الموظف غير موجود', 'error')
        return redirect(url_for('employees'))
    
    conn.close()
    
    return render_template('employees/print.html', employee=employee)

# API endpoints
@app.route('/api/employee_statuses')
def api_employee_statuses():
    """API للحصول على قائمة حالات الموظفين"""
    # حالات افتراضية
    default_statuses = [
        {'name': 'نشط', 'color': '#28a745'},
        {'name': 'معلق', 'color': '#ffc107'},
        {'name': 'موقوف', 'color': '#dc3545'},
        {'name': 'مستودع', 'color': '#6c757d'},
        {'name': 'منتدب', 'color': '#17a2b8'},
        {'name': 'في عطلة طويلة الأمد', 'color': '#fd7e14'},
        {'name': 'محول خارجياً', 'color': '#6f42c1'},
        {'name': 'متقاعد', 'color': '#20c997'},
        {'name': 'مستقيل', 'color': '#e83e8c'},
        {'name': 'متوفى', 'color': '#343a40'}
    ]
    
    # إذا كانت وحدة إدارة الحالات متاحة، استخدمها
    if STATUS_MODULES_AVAILABLE:
        try:
            statuses = status_manager.get_all_statuses()
            return jsonify([{
                'name': status['name'],
                'color': status.get('color', '#6c757d')
            } for status in statuses])
        except:
            pass
    
    return jsonify(default_statuses)

@app.route('/api/communes/<int:wilaya_id>')
def api_communes(wilaya_id):
    """API للحصول على بلديات الولاية"""
    conn = get_db_connection()
    try:
        communes = conn.execute(
            'SELECT * FROM communes WHERE wilaya_id = ? ORDER BY name',
            (wilaya_id,)
        ).fetchall()
        return jsonify([{
            'id': commune['id'],
            'name': commune['name']
        } for commune in communes])
    except:
        return jsonify([])
    finally:
        conn.close()

# مسارات أساسية أخرى
@app.route('/leaves')
def leaves():
    """إدارة العطل"""
    return render_template('leaves/index.html')

@app.route('/certificates')
def certificates():
    """إدارة الشهادات"""
    return render_template('certificates/index.html')

@app.route('/promotions')
def promotions():
    """إدارة الترقيات"""
    return render_template('promotions/index.html')

@app.route('/transfers')
def transfers():
    """إدارة التحويلات"""
    return render_template('transfers/index.html')

@app.route('/sanctions')
def sanctions():
    """إدارة العقوبات"""
    return render_template('sanctions/index.html')

@app.route('/statistics')
def statistics():
    """الإحصائيات"""
    return render_template('statistics/index.html')

@app.route('/settings')
def settings():
    """الإعدادات"""
    return render_template('settings/index.html')

# مسار إضافي لحالات الموظفين (للتوافق مع القوالب)
@app.route('/employee_statuses')
def employee_statuses():
    """إدارة حالات الموظفين - توجيه إلى لوحة التحكم"""
    if STATUS_MODULES_AVAILABLE:
        return redirect(url_for('employee_status_main'))
    else:
        flash('وحدة إدارة الحالات غير متاحة', 'warning')
        return redirect(url_for('index'))

# مسار لملخص حالات الموظفين
@app.route('/employee_status_summary')
def employee_status_summary():
    """ملخص حالات الموظفين"""
    if STATUS_MODULES_AVAILABLE:
        return render_template('employee_status/summary.html')
    else:
        flash('وحدة إدارة الحالات غير متاحة', 'warning')
        return redirect(url_for('index'))

# مسارات إضافية للوحدات المتقدمة
@app.route('/employee_status')
def employee_status_main():
    """لوحة تحكم حالات الموظفين"""
    if STATUS_MODULES_AVAILABLE:
        return render_template('employee_status/dashboard.html')
    else:
        flash('وحدة إدارة الحالات غير متاحة', 'warning')
        return redirect(url_for('index'))

@app.route('/external_transfers')
def external_transfers():
    """التحويلات الخارجية"""
    if STATUS_MODULES_AVAILABLE:
        return render_template('employee_status/transfers.html')
    else:
        flash('وحدة إدارة الحالات غير متاحة', 'warning')
        return redirect(url_for('index'))

@app.route('/reports')
def status_reports():
    """تقارير حالات الموظفين"""
    if STATUS_MODULES_AVAILABLE:
        return render_template('employee_status/reports.html')
    else:
        flash('وحدة إدارة الحالات غير متاحة', 'warning')
        return redirect(url_for('index'))

if __name__ == '__main__':
    print("🚀 تشغيل نظام إدارة موظفي الجمارك الجزائرية - النسخة الأصلية")
    print("=" * 70)
    print("🌐 الوصول للنظام: http://localhost:5000")
    print("👥 قائمة الموظفين: http://localhost:5000/employees")
    print("➕ إضافة موظف: http://localhost:5000/add_employee")
    print("📋 إدارة العطل: http://localhost:5000/leaves")
    print("🎓 إدارة الشهادات: http://localhost:5000/certificates")
    print("📈 إدارة الترقيات: http://localhost:5000/promotions")
    print("🔄 إدارة التحويلات: http://localhost:5000/transfers")
    print("⚖️  إدارة العقوبات: http://localhost:5000/sanctions")
    print("📊 الإحصائيات: http://localhost:5000/statistics")
    print("⚙️  الإعدادات: http://localhost:5000/settings")
    
    if STATUS_MODULES_AVAILABLE:
        print("🎛️  لوحة تحكم الحالات: http://localhost:5000/employee_status")
        print("📋 ملخص الحالات: http://localhost:5000/employee_status_summary")
        print("🔄 التحويلات الخارجية: http://localhost:5000/external_transfers")
        print("📈 التقارير: http://localhost:5000/reports")
        print("🔌 واجهة API: http://localhost:5000/api/")
    else:
        print("⚠️  وحدات إدارة الحالات المتقدمة غير متاحة")
    
    print("=" * 70)
    
    app.run(debug=True, host='0.0.0.0', port=5000)