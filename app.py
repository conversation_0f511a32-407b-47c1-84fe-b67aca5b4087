#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية
Algerian Customs Employee Management System

تطبيق Flask حديث بتصميم مستوحى من موقع بريد الجزائر
Modern Flask application inspired by Algeria Post website design
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os
from datetime import datetime, date
import json

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'customs_management_2025_algeria'

# إعدادات التطبيق
app.config['DATABASE'] = 'customs_employees.db'
app.config['UPLOAD_FOLDER'] = 'static/uploads'

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(app.config['DATABASE'])
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات"""
    conn = get_db_connection()
    
    # جدول الموظفين الأساسي
    conn.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            first_name_ar TEXT NOT NULL,
            last_name_ar TEXT NOT NULL,
            first_name_fr TEXT,
            last_name_fr TEXT,
            birth_date DATE,
            age INTEGER,
            phone TEXT,
            email TEXT,
            address TEXT,
            wilaya TEXT,
            commune TEXT,
            department TEXT,
            position TEXT,
            grade TEXT,
            hire_date DATE,
            photo_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول العطل السنوية
    conn.execute('''
        CREATE TABLE IF NOT EXISTS annual_leaves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER,
            year INTEGER,
            total_days INTEGER,
            used_days INTEGER DEFAULT 0,
            remaining_days INTEGER,
            start_date DATE,
            end_date DATE,
            destination TEXT,
            decision_number TEXT,
            decision_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # جدول العطل المرضية
    conn.execute('''
        CREATE TABLE IF NOT EXISTS sick_leaves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER,
            leave_type TEXT,
            start_date DATE,
            end_date DATE,
            days_count INTEGER,
            medical_supervision BOOLEAN DEFAULT 0,
            doctor_opinion TEXT,
            salary_deduction BOOLEAN DEFAULT 0,
            deduction_amount REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # جدول العطل الأخرى
    conn.execute('''
        CREATE TABLE IF NOT EXISTS other_leaves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER,
            leave_type TEXT,
            start_date DATE,
            end_date DATE,
            days_count INTEGER,
            reason TEXT,
            destination TEXT,
            decision_number TEXT,
            decision_date DATE,
            salary_deduction BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # جدول حالات الموظفين
    conn.execute('''
        CREATE TABLE IF NOT EXISTS employee_statuses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER,
            status_type TEXT NOT NULL,
            start_date DATE,
            end_date DATE,
            decision_number TEXT,
            decision_date DATE,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # جدول الشهادات
    conn.execute('''
        CREATE TABLE IF NOT EXISTS certificates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER,
            certificate_name TEXT NOT NULL,
            institution TEXT,
            issue_date DATE,
            certificate_type TEXT,
            grade TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    # جدول التكوين
    conn.execute('''
        CREATE TABLE IF NOT EXISTS training (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER,
            training_name TEXT NOT NULL,
            institution TEXT,
            start_date DATE,
            end_date DATE,
            duration_days INTEGER,
            training_type TEXT,
            certificate_obtained BOOLEAN DEFAULT 0,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees (id)
        )
    ''')
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    conn = get_db_connection()
    
    # إحصائيات سريعة
    stats = {
        'total_employees': conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0],
        'active_leaves': conn.execute('SELECT COUNT(*) FROM annual_leaves WHERE start_date <= date("now") AND end_date >= date("now")').fetchone()[0],
        'pending_certificates': conn.execute('SELECT COUNT(*) FROM certificates WHERE created_at >= date("now", "-30 days")').fetchone()[0],
        'recent_training': conn.execute('SELECT COUNT(*) FROM training WHERE created_at >= date("now", "-30 days")').fetchone()[0]
    }
    
    conn.close()
    return render_template('dashboard.html', stats=stats)

@app.route('/employees')
def employees():
    """صفحة إدارة الموظفين"""
    conn = get_db_connection()
    employees = conn.execute('SELECT * FROM employees ORDER BY registration_number').fetchall()
    conn.close()
    return render_template('employees/index.html', employees=employees)

@app.route('/leaves')
def leaves():
    """صفحة إدارة العطل والإجازات"""
    return render_template('leaves/index.html')

@app.route('/certificates')
def certificates():
    """صفحة إدارة الشهادات والتكوين"""
    return render_template('certificates/index.html')

@app.route('/employee_statuses')
def employee_statuses():
    """صفحة حالات الموظفين"""
    return render_template('employee_statuses/index.html')

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    return render_template('settings/index.html')

if __name__ == '__main__':
    # إنشاء مجلدات التحميل
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # تهيئة قاعدة البيانات
    init_database()
    
    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5000)
