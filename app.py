#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة موظفي الجمارك الجزائرية - نسخة نظيفة
Clean Algerian Customs Employee Management System
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os
from datetime import datetime, date

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'clean_customs_2025'

# إعدادات التطبيق
app.config['DATABASE'] = 'customs_employees.db'
app.config['UPLOAD_FOLDER'] = 'static/uploads'

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(app.config['DATABASE'])
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات"""
    conn = get_db_connection()
    
    # جدول الموظفين الأساسي
    conn.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            first_name_ar TEXT NOT NULL,
            last_name_ar TEXT NOT NULL,
            first_name_fr TEXT,
            last_name_fr TEXT,
            birth_date DATE,
            age INTEGER,
            phone TEXT,
            email TEXT,
            address TEXT,
            wilaya TEXT,
            commune TEXT,
            department TEXT,
            position TEXT,
            grade TEXT,
            hire_date DATE,
            photo_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    conn = get_db_connection()
    
    # إحصائيات سريعة
    stats = {
        'total_employees': conn.execute('SELECT COUNT(*) FROM employees').fetchone()[0],
        'active_leaves': 0,
        'pending_certificates': 0,
        'recent_training': 0
    }
    
    conn.close()
    return render_template('dashboard.html', stats=stats)

@app.route('/employees')
def employees():
    """صفحة إدارة الموظفين"""
    conn = get_db_connection()
    employees = conn.execute('SELECT * FROM employees ORDER BY registration_number').fetchall()
    conn.close()
    return render_template('employees/index.html', employees=employees)

@app.route('/leaves')
def leaves():
    """صفحة إدارة العطل والإجازات"""
    return render_template('leaves/index.html')

@app.route('/certificates')
def certificates():
    """صفحة إدارة الشهادات والتكوين"""
    return render_template('certificates/index.html')

@app.route('/employee_statuses')
def employee_statuses():
    """صفحة حالات الموظفين"""
    return render_template('employee_statuses/index.html')

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    return render_template('settings/index.html')

@app.route('/promotions')
def promotions():
    """صفحة الترقيات"""
    return render_template('promotions/index.html')

@app.route('/transfers')
def transfers():
    """صفحة التنقلات والحركات"""
    return render_template('transfers/index.html')

@app.route('/statistics')
def statistics():
    """صفحة الإحصائيات والتقارير"""
    return render_template('statistics/index.html')

@app.route('/sanctions')
def sanctions():
    """صفحة العقوبات والمكافآت"""
    return render_template('sanctions/index.html')

if __name__ == '__main__':
    # إنشاء مجلدات التحميل
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # تهيئة قاعدة البيانات
    init_database()
    
    # تشغيل التطبيق
    print("🇩🇿 نظام إدارة موظفي الجمارك الجزائرية")
    print("🌐 http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
