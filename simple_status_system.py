#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام بسيط لإدارة حالات الموظفين
Simple Employee Status Management System
"""

import sqlite3
from datetime import datetime, date, timedelta
import json

def create_simple_status_tables():
    """إنشاء جداول بسيطة لحالات الموظفين"""
    print("🚀 إنشاء جداول حالات الموظفين البسيطة...")
    
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    
    try:
        # 1. جدول أسباب الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_reasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reason_text TEXT NOT NULL UNIQUE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج الأسباب الافتراضية
        reasons = [
            'رعاية الأطفال',
            'الدراسة',
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        for reason in reasons:
            cursor.execute('INSERT OR IGNORE INTO leave_reasons (reason_text) VALUES (?)', (reason,))
        
        # 2. جدول الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_leave_absence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                period_number INTEGER NOT NULL,
                duration_months INTEGER NOT NULL,
                reason_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                total_previous_months INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                FOREIGN KEY (reason_id) REFERENCES leave_reasons (id)
            )
        ''')
        
        # 3. جدول التوقيف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_suspensions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                suspension_date DATE NOT NULL,
                suspension_reason TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                end_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 4. جدول العزل
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_dismissals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                dismissal_date DATE NOT NULL,
                dismissal_reason TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 5. جدول الاستقالة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_resignations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                request_date DATE NOT NULL,
                resignation_reason TEXT NOT NULL,
                is_accepted INTEGER,
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 6. جدول الخدمة الوطنية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_national_services (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                service_location TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 7. جدول عطلة طويلة الأمد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_long_leaves (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                review_date DATE NOT NULL,
                document_number TEXT NOT NULL,
                document_date DATE NOT NULL,
                granting_authority TEXT NOT NULL,
                leave_reason TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 8. جدول الانتداب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_assignments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                assignment_date DATE NOT NULL,
                duration_months INTEGER NOT NULL,
                assignment_reason TEXT NOT NULL,
                assignment_location TEXT NOT NULL,
                end_date DATE NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # 9. جدول الدراسة/التكوين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_studies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                study_type TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                institution TEXT,
                expected_end_date DATE,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # ================================
        # جداول الحالات النهائية (المحذوفة)
        # ================================
        
        # 1. جدول الوفيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS deceased_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                death_date DATE NOT NULL,
                death_cause TEXT NOT NULL CHECK (death_cause IN ('عادية', 'حادث عمل')),
                death_certificate_number TEXT NOT NULL,
                employee_data TEXT,
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # 2. جدول التحويل الخارجي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS external_transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                transfer_date DATE NOT NULL,
                destination_directorate TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                employee_data TEXT,
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # 3. جدول العزل (المحذوفين)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dismissed_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                dismissal_date DATE NOT NULL,
                dismissal_reason TEXT NOT NULL,
                decision_number TEXT NOT NULL,
                decision_date DATE NOT NULL,
                employee_data TEXT,
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # 4. جدول التقاعد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS retired_employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_employee_id INTEGER NOT NULL,
                registration_number TEXT NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                birth_date DATE,
                hire_date DATE,
                retirement_date DATE NOT NULL,
                retirement_decision_number TEXT NOT NULL,
                retirement_decision_date DATE NOT NULL,
                retirement_card_number TEXT,
                retirement_card_issue_date DATE,
                employee_data TEXT,
                transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transferred_by TEXT
            )
        ''')
        
        # جدول تاريخ تغييرات الحالات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                old_status TEXT,
                new_status TEXT NOT NULL,
                change_date DATE NOT NULL,
                change_reason TEXT,
                decision_number TEXT,
                decision_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT
            )
        ''')
        
        conn.commit()
        print("✅ تم إنشاء جميع الجداول بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

class SimpleStatusManager:
    """مدير حالات الموظفين البسيط"""
    
    def __init__(self, db_path='customs_employees.db'):
        self.db_path = db_path
    
    def get_db_connection(self):
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def add_leave_of_absence(self, employee_id, data):
        """إضافة استيداع"""
        conn = self.get_db_connection()
        try:
            # التحقق من وجود الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # حساب إجمالي أشهر الاستيداع السابقة
            total_previous = conn.execute('''
                SELECT COALESCE(SUM(duration_months), 0) 
                FROM employee_leave_absence 
                WHERE employee_id = ? AND is_active = 1
            ''', (employee_id,)).fetchone()[0]
            
            # التحقق من عدم تجاوز 60 شهر (5 سنوات)
            new_duration = int(data['duration_months'])
            if total_previous + new_duration > 60:
                remaining = 60 - total_previous
                return False, f"تجاوز الحد الأقصى للاستيداع. المتبقي: {remaining} شهر"
            
            # حساب تاريخ النهاية
            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            end_date = start_date + timedelta(days=new_duration * 30)
            
            # حساب رقم الفترة
            period_count = conn.execute('''
                SELECT COUNT(*) FROM employee_leave_absence WHERE employee_id = ?
            ''', (employee_id,)).fetchone()[0]
            
            # إدراج البيانات
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO employee_leave_absence 
                (employee_id, period_number, duration_months, reason_id, start_date, 
                 end_date, decision_number, decision_date, total_previous_months, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, period_count + 1, new_duration, data['reason_id'],
                start_date, end_date, data.get('decision_number'),
                data.get('decision_date'), total_previous, data.get('created_by')
            ))
            
            # تحديث حالة الموظف
            conn.execute('UPDATE employees SET status = ? WHERE id = ?', ('مستودع', employee_id))
            
            # إضافة سجل في التاريخ
            self.add_status_history(conn, employee_id, employee['status'], 'مستودع',
                                  start_date, 'استيداع', data.get('decision_number'),
                                  data.get('decision_date'), data.get('created_by'))
            
            conn.commit()
            return True, "تم إضافة الاستيداع بنجاح"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    def transfer_to_death(self, employee_id, data):
        """نقل الموظف لجدول الوفيات"""
        conn = self.get_db_connection()
        try:
            # الحصول على بيانات الموظف
            employee = conn.execute('SELECT * FROM employees WHERE id = ?', (employee_id,)).fetchone()
            if not employee:
                return False, "الموظف غير موجود"
            
            # تحويل بيانات الموظف إلى JSON
            employee_data = dict(employee)
            employee_json = json.dumps(employee_data, ensure_ascii=False, default=str)
            
            cursor = conn.cursor()
            
            # إدراج في جدول الوفيات
            cursor.execute('''
                INSERT INTO deceased_employees 
                (original_employee_id, registration_number, first_name, last_name,
                 birth_date, hire_date, death_date, death_cause, death_certificate_number,
                 employee_data, transferred_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_id, employee['registration_number'], employee['first_name'],
                employee['last_name'], employee['birth_date'], employee['hire_date'],
                data['death_date'], data['death_cause'], data['death_certificate_number'],
                employee_json, data.get('transferred_by')
            ))
            
            # إضافة سجل في التاريخ
            self.add_status_history(conn, employee_id, employee['status'], 'متوفى',
                                  data['death_date'], 'وفاة', None, None, 
                                  data.get('transferred_by'))
            
            # حذف من جدول الموظفين
            conn.execute('DELETE FROM employees WHERE id = ?', (employee_id,))
            
            conn.commit()
            return True, "تم نقل الموظف لجدول الوفيات"
            
        except Exception as e:
            conn.rollback()
            return False, f"خطأ: {str(e)}"
        finally:
            conn.close()
    
    def add_status_history(self, conn, employee_id, old_status, new_status, 
                          change_date, change_reason, decision_number, 
                          decision_date, created_by):
        """إضافة سجل في تاريخ تغييرات الحالات"""
        conn.execute('''
            INSERT INTO status_history 
            (employee_id, old_status, new_status, change_date, change_reason,
             decision_number, decision_date, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (employee_id, old_status, new_status, change_date, change_reason,
              decision_number, decision_date, created_by))
    
    def get_statistics(self):
        """إحصائيات الموظفين"""
        conn = self.get_db_connection()
        try:
            stats = {}
            
            # الموظفين النشطين
            stats['active'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'نشط'").fetchone()[0]
            stats['leave_of_absence'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'مستودع'").fetchone()[0]
            stats['suspension'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'موقوف'").fetchone()[0]
            stats['resignation'] = conn.execute("SELECT COUNT(*) FROM employees WHERE status = 'مستقيل'").fetchone()[0]
            
            # الحالات النهائية
            stats['deceased'] = conn.execute('SELECT COUNT(*) FROM deceased_employees').fetchone()[0]
            stats['external_transfer'] = conn.execute('SELECT COUNT(*) FROM external_transfers').fetchone()[0]
            stats['dismissed'] = conn.execute('SELECT COUNT(*) FROM dismissed_employees').fetchone()[0]
            stats['retired'] = conn.execute('SELECT COUNT(*) FROM retired_employees').fetchone()[0]
            
            # الإجمالي
            stats['total_active'] = stats['active'] + stats['leave_of_absence'] + stats['suspension'] + stats['resignation']
            stats['total_removed'] = stats['deceased'] + stats['external_transfer'] + stats['dismissed'] + stats['retired']
            stats['grand_total'] = stats['total_active'] + stats['total_removed']
            
            return stats
            
        except Exception as e:
            print(f"خطأ في الإحصائيات: {e}")
            return {}
        finally:
            conn.close()
    
    def get_leave_balance(self, employee_id):
        """حساب رصيد الاستيداع للموظف"""
        conn = self.get_db_connection()
        try:
            total_used = conn.execute('''
                SELECT COALESCE(SUM(duration_months), 0) 
                FROM employee_leave_absence 
                WHERE employee_id = ? AND is_active = 1
            ''', (employee_id,)).fetchone()[0]
            
            remaining = 60 - total_used
            
            return {
                'employee_id': employee_id,
                'total_used_months': total_used,
                'remaining_months': remaining,
                'remaining_years': remaining / 12
            }
        except Exception as e:
            return {'error': str(e)}
        finally:
            conn.close()

def test_simple_system():
    """اختبار النظام البسيط"""
    print("\n🧪 اختبار النظام البسيط...")
    
    manager = SimpleStatusManager()
    stats = manager.get_statistics()
    
    print("📊 إحصائيات النظام:")
    print(f"   النشطين: {stats.get('active', 0)}")
    print(f"   المستودعين: {stats.get('leave_of_absence', 0)}")
    print(f"   الموقوفين: {stats.get('suspension', 0)}")
    print(f"   المتوفين: {stats.get('deceased', 0)}")
    print(f"   الإجمالي النشط: {stats.get('total_active', 0)}")
    print(f"   الإجمالي العام: {stats.get('grand_total', 0)}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🌟 النظام البسيط لإدارة حالات الموظفين")
    print("=" * 60)
    
    # إنشاء الجداول
    if create_simple_status_tables():
        print("✅ تم إنشاء الجداول بنجاح")
        
        # اختبار النظام
        if test_simple_system():
            print("\n🎉 النظام البسيط جاهز للاستخدام!")
            
            print(f"\n📋 الميزات المتاحة:")
            print(f"   ✅ إدارة الاستيداع (مع حساب الرصيد 5 سنوات)")
            print(f"   ✅ إدارة التوقيف")
            print(f"   ✅ إدارة العزل")
            print(f"   ✅ إدارة الاستقالة")
            print(f"   ✅ إدارة الخدمة الوطنية")
            print(f"   ✅ إدارة عطلة طويلة الأمد")
            print(f"   ✅ إدارة الانتداب")
            print(f"   ✅ إدارة الدراسة/التكوين")
            print(f"   ✅ نقل الحالات النهائية لجداول منفصلة")
            print(f"   ✅ تتبع تاريخ التغييرات")
            print(f"   ✅ إحصائيات شاملة")
            
            return True
        else:
            print("❌ فشل في اختبار النظام")
            return False
    else:
        print("❌ فشل في إنشاء الجداول")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 الخطوة التالية:")
        print(f"   يمكنك الآن استخدام النظام من خلال:")
        print(f"   manager = SimpleStatusManager()")
        print(f"   stats = manager.get_statistics()")
    else:
        print(f"\n❌ يرجى مراجعة الأخطاء وإعادة المحاولة")