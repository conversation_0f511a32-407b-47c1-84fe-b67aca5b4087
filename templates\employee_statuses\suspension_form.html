{% extends "base.html" %}

{% block title %}توقيف الموظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}توقيف الموظف{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">
            <i class="fas fa-stop-circle me-2"></i>توقيف الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الرتبة:</strong> {{ employee.rank or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-success">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج التوقيف -->
<div class="card">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>بيانات التوقيف
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="suspensionForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="suspension_date" class="form-label">تاريخ التوقيف <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="suspension_date" name="suspension_date" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب التوقيف <span class="text-danger">*</span></label>
                        <select class="form-select" id="reason" name="reason" required>
                            <option value="">اختر سبب التوقيف</option>
                            <option value="مخالفة إدارية">مخالفة إدارية</option>
                            <option value="مخالفة مالية">مخالفة مالية</option>
                            <option value="عدم احترام التوقيت">عدم احترام التوقيت</option>
                            <option value="سوء السلوك">سوء السلوك</option>
                            <option value="إهمال في العمل">إهمال في العمل</option>
                            <option value="مخالفة قانونية">مخالفة قانونية</option>
                            <option value="تحقيق إداري">تحقيق إداري</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- حقل السبب المخصص -->
            <div class="row" id="customReasonRow" style="display: none;">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="custom_reason" class="form-label">تفاصيل السبب</label>
                        <textarea class="form-control" id="custom_reason" name="custom_reason" rows="3" 
                                  placeholder="أدخل تفاصيل سبب التوقيف"></textarea>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_number" class="form-label">رقم مقرر التوقيف</label>
                        <input type="text" class="form-control" id="decision_number" name="decision_number" 
                               placeholder="أدخل رقم مقرر التوقيف">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_date" class="form-label">تاريخ مقرر التوقيف</label>
                        <input type="date" class="form-control" id="decision_date" name="decision_date">
                    </div>
                </div>
            </div>
            
            <!-- تحذير -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> توقيف الموظف سيؤدي إلى تعليق جميع مهامه وحقوقه الوظيفية مؤقتاً حتى انتهاء فترة التوقيف أو صدور قرار بإنهائه.
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-danger" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل التوقيف
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal تأكيد التوقيف -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد توقيف الموظف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من توقيف الموظف:</p>
                <div class="text-center p-3 border rounded bg-light">
                    <h6><strong>{{ employee.first_name }} {{ employee.last_name }}</strong></h6>
                    <p class="mb-0">رقم التسجيل: {{ employee.registration_number }}</p>
                </div>
                <div class="mt-3">
                    <p><strong>تاريخ التوقيف:</strong> <span id="confirmDate"></span></p>
                    <p><strong>السبب:</strong> <span id="confirmReason"></span></p>
                </div>
                <div class="alert alert-info mt-3 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم تعليق جميع الحقوق والواجبات الوظيفية للموظف حتى انتهاء فترة التوقيف.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>تأكيد التوقيف
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إظهار/إخفاء حقل السبب المخصص
document.getElementById('reason').addEventListener('change', function() {
    const customReasonRow = document.getElementById('customReasonRow');
    if (this.value === 'أخرى') {
        customReasonRow.style.display = 'block';
        document.getElementById('custom_reason').required = true;
    } else {
        customReasonRow.style.display = 'none';
        document.getElementById('custom_reason').required = false;
    }
});

document.getElementById('suspensionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const suspensionDate = document.getElementById('suspension_date').value;
    const reason = document.getElementById('reason').value;
    
    if (!suspensionDate || !reason) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من السبب المخصص إذا كان مطلوباً
    if (reason === 'أخرى') {
        const customReason = document.getElementById('custom_reason').value.trim();
        if (!customReason) {
            alert('يرجى إدخال تفاصيل سبب التوقيف');
            return;
        }
    }
    
    // تحديث بيانات التأكيد
    document.getElementById('confirmDate').textContent = suspensionDate;
    document.getElementById('confirmReason').textContent = reason === 'أخرى' ? 
        document.getElementById('custom_reason').value : reason;
    
    // إظهار مودال التأكيد
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
});

document.getElementById('confirmSubmit').addEventListener('click', function() {
    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
    
    // تعطيل الزر وإظهار مؤشر التحميل
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
    
    // تحديث قيمة السبب إذا كان مخصصاً
    const reasonSelect = document.getElementById('reason');
    if (reasonSelect.value === 'أخرى') {
        reasonSelect.value = document.getElementById('custom_reason').value;
    }
    
    // إرسال النموذج
    document.getElementById('suspensionForm').submit();
});

// تحديد تاريخ اليوم كحد أقصى
document.getElementById('suspension_date').max = new Date().toISOString().split('T')[0];
document.getElementById('decision_date').max = new Date().toISOString().split('T')[0];
</script>
{% endblock %}