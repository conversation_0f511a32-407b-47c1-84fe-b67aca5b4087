# دليل استخدام وحدة إدارة حالات الموظفين

## نظرة عامة

تم إنشاء وحدة شاملة لإدارة جميع حالات الموظفين في نظام إدارة موظفي الجمارك الجزائرية. هذه الوحدة تدمج جميع الحالات في ملف واحد مع معالجة خاصة للتحويلات الخارجية.

## الملفات المنشأة

### 1. الملفات الأساسية
- `employee_status_manager.py` - الوحدة الرئيسية لإدارة الحالات
- `status_integration.py` - ملف التكامل مع التطبيق الرئيسي
- `update_app_integration.py` - دليل التحديثات المطلوبة

### 2. القوالب (Templates)
- `templates/employee_status/dashboard.html` - لوحة تحكم الحالات
- `templates/employee_status/external_transfers.html` - قائمة التحويلات الخارجية
- `templates/employee_status/summary.html` - ملخص شامل للحالات
- `templates/employee_status/history.html` - تاريخ حالات موظف معين

## الحالات المدعومة

### 1. العطلة طويلة الأمد
- إضافة عطلة طويلة الأمد للموظف
- إنهاء العطلة وإعادة تفعيل الموظف
- تتبع تواريخ البداية والانتهاء المتوقعة والفعلية

### 2. الاستقالة
- تسجيل طلب الاستقالة
- الموافقة على الاستقالة أو رفضها
- تتبع فترة الإشعار وآخر يوم عمل

### 3. الاستيداع
- تسجيل استيداع الموظف
- إنهاء الاستيداع وإعادة تفعيل الموظف
- تحديد نوع الاستيداع والمدة المتوقعة

### 4. الوفاة
- تسجيل وفاة الموظف
- حفظ تفاصيل شهادة الوفاة
- تحديث حالة الموظف نهائياً

### 5. التوقيف
- تسجيل توقيف الموظف (مؤقت أو دائم)
- إنهاء التوقيف وإعادة تفعيل الموظف
- تحديد مدة التوقيف والأسباب

### 6. التقاعد
- تسجيل تقاعد الموظف
- حفظ تفاصيل المعاش وسنوات الخدمة
- أنواع التقاعد المختلفة

### 7. التحويل الخارجي (معالجة خاصة)
- نقل الموظف خارج تعداد المؤسسة
- حفظ تفاصيل الجهة المحول إليها
- **الموظف لا يظهر في الإحصائيات العادية**

### 8. الانتداب
- تسجيل انتداب الموظف
- إنهاء الانتداب وإعادة الموظف
- تحديد مكان ونوع الانتداب

## المسارات الجديدة

### المسارات الرئيسية
- `/employee_status` - لوحة تحكم حالات الموظفين
- `/employee_status_summary` - ملخص شامل للحالات
- `/external_transfers` - قائمة التحويلات الخارجية
- `/employee_status/<employee_id>` - تاريخ حالات موظف معين

### مسارات إضافة الحالات
- `/employee_status/long_term_leave/add/<employee_id>`
- `/employee_status/resignation/add/<employee_id>`
- `/employee_status/leave_of_absence/add/<employee_id>`
- `/employee_status/death/add/<employee_id>`
- `/employee_status/suspension/add/<employee_id>`
- `/employee_status/retirement/add/<employee_id>`
- `/employee_status/external_transfer/add/<employee_id>`
- `/employee_status/assignment/add/<employee_id>`

### مسارات API
- `/api/employee_status/<employee_id>` - حالة موظف معين
- `/api/employee_status/statistics` - إحصائيات الحالات
- `/api/employees/by_status/<status>` - موظفين حسب الحالة

## الميزات الخاصة

### 1. التحويل الخارجي
- الموظفون المحولون خارجياً **لا يظهرون** في:
  - عدد الموظفين النشطين
  - الإحصائيات العادية للمؤسسة
  - التقارير الداخلية
- يمكن الوصول إليهم من خلال:
  - قائمة التحويلات الخارجية المنفصلة
  - تقارير خاصة بالتحويلات

### 2. الإحصائيات المحدثة
- إجمالي الموظفين
- الموظفين النشطين (باستثناء المحولين خارجياً)
- تفصيل حسب كل حالة
- إحصائيات منفصلة للتحويلات الخارجية

### 3. تاريخ الحالات
- عرض تسلسلي زمني لجميع حالات الموظف
- إمكانية إنهاء الحالات النشطة
- تتبع التواريخ والقرارات والملاحظات

## كيفية الاستخدام

### 1. تشغيل النظام
```bash
python app.py
```

### 2. الوصول للوحة التحكم
- انتقل إلى `/employee_status` لعرض لوحة تحكم الحالات
- أو `/employee_status_summary` لعرض ملخص شامل

### 3. إضافة حالة جديدة
1. انتقل إلى صفحة الموظف
2. اختر "تاريخ الحالات"
3. اختر نوع الحالة المطلوبة
4. املأ البيانات المطلوبة
5. احفظ التغييرات

### 4. عرض التحويلات الخارجية
- انتقل إلى `/external_transfers`
- ستجد قائمة بجميع الموظفين المحولين خارجياً
- هؤلاء الموظفون لا يظهرون في التعداد العادي

## الفوائد

### 1. التنظيم
- جميع حالات الموظفين في مكان واحد
- سهولة التتبع والإدارة
- تقليل التعقيد في الكود

### 2. المرونة
- إمكانية إضافة حالات جديدة بسهولة
- تخصيص معالجة كل حالة
- دعم الحالات المؤقتة والدائمة

### 3. الدقة
- منع التضارب بين الحالات
- التحقق من صحة البيانات
- تتبع دقيق للتواريخ والقرارات

### 4. التقارير
- إحصائيات دقيقة ومفصلة
- تقارير منفصلة للتحويلات الخارجية
- تحليل شامل لحالات الموظفين

## ملاحظات مهمة

### 1. التحويل الخارجي
- **تأكد** من أن التحويل الخارجي يعني خروج الموظف من المؤسسة
- الموظف المحول خارجياً لن يظهر في الإحصائيات العادية
- يمكن الوصول إليه فقط من خلال قائمة التحويلات الخارجية

### 2. النسخ الاحتياطية
- احتفظ بنسخة احتياطية من قاعدة البيانات قبل التحديث
- اختبر النظام على بيانات تجريبية أولاً

### 3. التدريب
- تأكد من تدريب المستخدمين على النظام الجديد
- وضح الفرق بين الحالات المختلفة
- اشرح أهمية التحويل الخارجي

## الدعم والصيانة

### إضافة حالة جديدة
1. أضف الحالة في `status_types` في `EmployeeStatusManager`
2. أنشئ دالة معالجة في نفس الكلاس
3. أضف المسار في `status_integration.py`
4. أنشئ القالب المناسب

### تعديل حالة موجودة
1. عدّل الدالة المناسبة في `EmployeeStatusManager`
2. حدّث القالب إذا لزم الأمر
3. اختبر التغييرات

### إضافة تقرير جديد
1. أنشئ دالة في `EmployeeStatusManager`
2. أضف المسار في `status_integration.py`
3. أنشئ القالب للتقرير

## الخلاصة

هذه الوحدة توفر حلاً شاملاً ومنظماً لإدارة جميع حالات الموظفين مع معالجة خاصة للتحويلات الخارجية. النظام مرن وقابل للتوسع ويوفر تتبعاً دقيقاً لجميع التغييرات في حالات الموظفين.