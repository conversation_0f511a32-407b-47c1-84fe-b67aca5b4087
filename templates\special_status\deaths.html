{% extends "base.html" %}

{% block page_title %}الوفيات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-heart me-2"></i>
                            سجل الوفيات
                        </h3>
                        <div>
                            <a href="{{ url_for('special_status.add_death') }}" class="btn btn-light">
                                <i class="fas fa-plus me-2"></i>تسجيل وفاة جديدة
                            </a>
                            <a href="{{ url_for('special_status.index') }}" class="btn btn-outline-light">
                                <i class="fas fa-arrow-right me-2"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if deaths %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم التسجيل</th>
                                    <th>الاسم الكامل</th>
                                    <th>الرتبة</th>
                                    <th>المصلحة</th>
                                    <th>تاريخ الوفاة</th>
                                    <th>مكان الوفاة</th>
                                    <th>سبب الوفاة</th>
                                    <th>رقم الشهادة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for death in deaths %}
                                <tr>
                                    <td>{{ death.registration_number }}</td>
                                    <td>{{ death.first_name }} {{ death.last_name }}</td>
                                    <td>{{ death.rank_name or 'غير محدد' }}</td>
                                    <td>{{ death.service_name or 'غير محدد' }}</td>
                                    <td>{{ death.death_date }}</td>
                                    <td>{{ death.death_place or 'غير محدد' }}</td>
                                    <td>{{ death.death_cause or 'غير محدد' }}</td>
                                    <td>{{ death.certificate_number or 'غير محدد' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="viewDeath({{ death.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="editDeath({{ death.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="printDeath({{ death.id }})">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد وفيات مسجلة</h5>
                        <p class="text-muted">يمكنك تسجيل وفاة جديدة من خلال الزر أعلاه</p>
                        <a href="{{ url_for('special_status.add_death') }}" class="btn btn-dark">
                            <i class="fas fa-plus me-2"></i>تسجيل وفاة جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الوفاة -->
<div class="modal fade" id="deathModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-dark text-white">
                <h5 class="modal-title">تفاصيل الوفاة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="deathDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-dark" onclick="printCurrentDeath()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentDeathId = null;

function viewDeath(id) {
    currentDeathId = id;
    // عرض تفاصيل الوفاة
    fetch(`/special_status/api/death/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('خطأ: ' + data.error);
                return;
            }
            
            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>رقم التسجيل:</strong> ${data.registration_number}<br>
                        <strong>الاسم:</strong> ${data.first_name} ${data.last_name}<br>
                        <strong>الرتبة:</strong> ${data.rank_name || 'غير محدد'}<br>
                        <strong>المصلحة:</strong> ${data.service_name || 'غير محدد'}<br>
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ الوفاة:</strong> ${data.death_date}<br>
                        <strong>مكان الوفاة:</strong> ${data.death_place || 'غير محدد'}<br>
                        <strong>سبب الوفاة:</strong> ${data.death_cause || 'غير محدد'}<br>
                        <strong>رقم الشهادة:</strong> ${data.certificate_number || 'غير محدد'}<br>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <strong>مكان الدفن:</strong> ${data.burial_place || 'غير محدد'}<br>
                        <strong>رقم القرار:</strong> ${data.decision_number || 'غير محدد'}<br>
                        <strong>تاريخ القرار:</strong> ${data.decision_date || 'غير محدد'}<br>
                    </div>
                    <div class="col-md-6">
                        <strong>معلومات الاتصال بالعائلة:</strong><br>
                        <p class="border p-2 bg-light">${data.family_contact || 'غير محدد'}</p>
                    </div>
                </div>
                ${data.notes ? `
                <div class="row">
                    <div class="col-12">
                        <strong>ملاحظات:</strong><br>
                        <p class="border p-2 bg-light">${data.notes}</p>
                    </div>
                </div>
                ` : ''}
            `;
            
            document.getElementById('deathDetails').innerHTML = details;
            new bootstrap.Modal(document.getElementById('deathModal')).show();
        })
        .catch(error => {
            alert('خطأ في تحميل البيانات: ' + error);
        });
}

function editDeath(id) {
    // تحرير الوفاة
    window.location.href = `/special_status/deaths/edit/${id}`;
}

function printDeath(id) {
    // طباعة تفاصيل الوفاة
    window.open(`/special_status/deaths/print/${id}`, '_blank');
}

function printCurrentDeath() {
    if (currentDeathId) {
        printDeath(currentDeathId);
    }
}
</script>

<style>
.table th {
    background-color: #343a40 !important;
    color: white !important;
}

.btn-group .btn {
    margin: 0 1px;
}

.modal-header.bg-dark {
    border-bottom: 1px solid #495057;
}
</style>
{% endblock %}
