{% extends "base.html" %}

{% block page_title %}الحالات الخاصة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-times me-2"></i>
                        نظام إدارة الحالات الخاصة للموظفين
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إحصائيات سريعة -->
                        <div class="col-md-3 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-minus fa-2x mb-2"></i>
                                    <h4>{{ stats.resignations or 0 }}</h4>
                                    <p class="mb-0">الاستقالات</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-dark text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-heart fa-2x mb-2"></i>
                                    <h4>{{ stats.deaths or 0 }}</h4>
                                    <p class="mb-0">الوفيات</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-check fa-2x mb-2"></i>
                                    <h4>{{ stats.retirements or 0 }}</h4>
                                    <p class="mb-0">المتقاعدين</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                                    <h4>{{ stats.external_transfers or 0 }}</h4>
                                    <p class="mb-0">التحويلات الخارجية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة الحالات الخاصة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="mb-3">إدارة الحالات الخاصة</h5>
                        </div>
                    </div>

                    <div class="row">
                        <!-- الاستقالات -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-minus me-2"></i>
                                        الاستقالات
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إدارة استقالات الموظفين وتتبع حالاتها</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.resignations') }}" class="btn btn-outline-danger">
                                            <i class="fas fa-list me-2"></i>عرض الاستقالات
                                        </a>
                                        <a href="{{ url_for('special_status.add_resignation') }}" class="btn btn-danger">
                                            <i class="fas fa-plus me-2"></i>إضافة استقالة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الوفيات -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-dark">
                                <div class="card-header bg-dark text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-heart me-2"></i>
                                        الوفيات
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">تسجيل وإدارة حالات وفاة الموظفين</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.deaths') }}" class="btn btn-outline-dark">
                                            <i class="fas fa-list me-2"></i>عرض الوفيات
                                        </a>
                                        <a href="{{ url_for('special_status.add_death') }}" class="btn btn-dark">
                                            <i class="fas fa-plus me-2"></i>تسجيل وفاة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- التقاعد -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-check me-2"></i>
                                        التقاعد
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إدارة حالات تقاعد الموظفين</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.retirements') }}" class="btn btn-outline-info">
                                            <i class="fas fa-list me-2"></i>عرض المتقاعدين
                                        </a>
                                        <a href="{{ url_for('special_status.add_retirement') }}" class="btn btn-info">
                                            <i class="fas fa-plus me-2"></i>إضافة تقاعد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- التحويل الخارجي -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        التحويل الخارجي
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إدارة تحويل الموظفين لجهات خارجية</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.external_transfers') }}" class="btn btn-outline-warning">
                                            <i class="fas fa-list me-2"></i>عرض التحويلات
                                        </a>
                                        <a href="{{ url_for('special_status.add_external_transfer') }}" class="btn btn-warning">
                                            <i class="fas fa-plus me-2"></i>إضافة تحويل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الاستيداع -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-clock me-2"></i>
                                        الاستيداع
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إدارة حالات استيداع الموظفين</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.leave_of_absence') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-list me-2"></i>عرض المستودعين
                                        </a>
                                        <a href="{{ url_for('special_status.add_leave_of_absence') }}" class="btn btn-secondary">
                                            <i class="fas fa-plus me-2"></i>إضافة استيداع
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- التوقيف -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-slash me-2"></i>
                                        التوقيف
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">إدارة حالات توقيف الموظفين</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('special_status.suspensions') }}" class="btn btn-outline-danger">
                                            <i class="fas fa-list me-2"></i>عرض الموقوفين
                                        </a>
                                        <a href="{{ url_for('special_status.add_suspension') }}" class="btn btn-danger">
                                            <i class="fas fa-plus me-2"></i>إضافة توقيف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">ملخص الحالات النشطة</h6>
                                    <div class="row text-center">
                                        <div class="col-md-6">
                                            <span class="badge bg-secondary fs-6">
                                                {{ stats.leave_of_absence or 0 }} مستودع نشط
                                            </span>
                                        </div>
                                        <div class="col-md-6">
                                            <span class="badge bg-danger fs-6">
                                                {{ stats.suspensions or 0 }} موقوف نشط
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
