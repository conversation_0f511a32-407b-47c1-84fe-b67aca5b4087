{% extends "base.html" %}

{% block title %}إضافة عطلة مرضية - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إضافة عطلة مرضية{% endblock %}

{% block content %}
<!-- شريط التنقل -->
<div class="card mb-4">
    <div class="card-body">
        <a href="{{ url_for('leaves') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للعطل والإجازات
        </a>
    </div>
</div>

<!-- نموذج إضافة العطلة المرضية -->
<form method="POST" id="sickLeaveForm">
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-user-md me-2"></i>بيانات العطلة المرضية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الموظف <span class="required">*</span></label>
                        <select name="employee_id" class="form-select" required>
                            <option value="">اختر الموظف</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}">
                                {{ employee.registration_number }} - {{ employee.first_name }} {{ employee.last_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">نوع العطلة المرضية <span class="required">*</span></label>
                        <select name="sick_leave_type" class="form-select" required>
                            <option value="">اختر النوع</option>
                            <option value="عطلة مرضية عادية">عطلة مرضية عادية</option>
                            <option value="عطلة مرضية طويلة المدى">عطلة مرضية طويلة المدى</option>
                            <option value="عطلة أمومة">عطلة أمومة</option>
                            <option value="عطلة حادث عمل">عطلة حادث عمل</option>
                            <option value="عطلة مرض مهني">عطلة مرض مهني</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ بداية العطلة <span class="required">*</span></label>
                        <input type="date" name="start_date" class="form-control" required id="start_date" onchange="calculateEndDate()">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">عدد أيام العطلة <span class="required">*</span></label>
                        <input type="number" name="days_count" class="form-control" required min="1" max="365" id="days_count" onchange="calculateEndDate()">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">تاريخ نهاية العطلة</label>
                        <input type="date" name="end_date" class="form-control" readonly id="end_date">
                        <div class="form-text">محسوب تلقائياً</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">مؤشرة</label>
                        <select name="is_indexed" class="form-select">
                            <option value="1">نعم</option>
                            <option value="0">لا</option>
                        </select>
                        <div class="form-text">هل العطلة مؤشرة أم لا</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">الرقابة الطبية الداخلية</label>
                        <select name="internal_medical_supervision" class="form-select">
                            <option value="1">نعم</option>
                            <option value="0">لا</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">رأي الطبيب المستشار</label>
                        <select name="doctor_opinion" class="form-select">
                            <option value="">غير محدد</option>
                            <option value="مقبولة">مقبولة</option>
                            <option value="غير مقبولة">غير مقبولة</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم الخصم</label>
                        <input type="text" name="deduction_number" class="form-control" placeholder="رقم الخصم إن وجد">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الخصم</label>
                        <input type="date" name="deduction_date" class="form-control">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">رقم الشهادة الطبية</label>
                        <input type="text" name="medical_certificate_number" class="form-control" placeholder="رقم الشهادة الطبية">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">اسم الطبيب</label>
                        <input type="text" name="doctor_name" class="form-control" placeholder="اسم الطبيب المعالج">
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="3" placeholder="ملاحظات إضافية حول العطلة المرضية"></textarea>
            </div>
        </div>
    </div>
    
    <!-- أزرار الحفظ -->
    <div class="card mt-4">
        <div class="card-body text-center">
            <button type="submit" class="btn btn-warning btn-lg me-3">
                <i class="fas fa-save me-2"></i>حفظ العطلة المرضية
            </button>
            <a href="{{ url_for('leaves') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </div>
</form>
{% endblock %}

{% block scripts %}
<script>
// حساب تاريخ النهاية
function calculateEndDate() {
    const startDate = document.getElementById('start_date').value;
    const daysCount = parseInt(document.getElementById('days_count').value) || 0;
    const endDateInput = document.getElementById('end_date');
    
    if (startDate && daysCount > 0) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setDate(start.getDate() + daysCount - 1);
        
        const endDateStr = end.toISOString().split('T')[0];
        endDateInput.value = endDateStr;
    } else {
        endDateInput.value = '';
    }
}

// التحقق من صحة النموذج
document.getElementById('sickLeaveForm').addEventListener('submit', function(e) {
    const employeeId = document.querySelector('select[name="employee_id"]').value;
    const leaveType = document.querySelector('select[name="sick_leave_type"]').value;
    const startDate = document.getElementById('start_date').value;
    const daysCount = parseInt(document.getElementById('days_count').value) || 0;
    
    if (!employeeId || !leaveType || !startDate || daysCount <= 0) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    if (daysCount > 365) {
        e.preventDefault();
        alert('عدد أيام العطلة المرضية لا يمكن أن يتجاوز 365 يوم');
        return false;
    }
});

// تحديد تاريخ اليوم كحد أدنى
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').min = today;
});
</script>
{% endblock %}
