#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مساعدات وأدوات إضافية لوحدة إدارة حالات الموظفين
Additional helpers and utilities for Employee Status Management
"""

from datetime import datetime, date, timedelta
import sqlite3
import json
from typing import Dict, List, Optional, Tuple, Any

class EmployeeStatusHelpers:
    """كلاس المساعدات والأدوات الإضافية"""
    
    def __init__(self, db_path: str = 'employees.db'):
        self.db_path = db_path
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def calculate_service_years(self, hire_date: str, end_date: str = None) -> float:
        """حساب سنوات الخدمة"""
        try:
            start = datetime.strptime(hire_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d') if end_date else datetime.now()
            
            years = (end - start).days / 365.25
            return round(years, 2)
        except:
            return 0.0
    
    def calculate_age(self, birth_date: str, reference_date: str = None) -> int:
        """حساب العمر"""
        try:
            birth = datetime.strptime(birth_date, '%Y-%m-%d')
            ref = datetime.strptime(reference_date, '%Y-%m-%d') if reference_date else datetime.now()
            
            age = (ref - birth).days // 365
            return age
        except:
            return 0
    
    def get_retirement_eligibility(self, employee_id: int) -> Dict[str, Any]:
        """فحص أهلية التقاعد"""
        conn = self.get_db_connection()
        
        try:
            employee = conn.execute(
                'SELECT birth_date, hire_date FROM employees WHERE id = ?',
                (employee_id,)
            ).fetchone()
            
            if not employee:
                return {'eligible': False, 'reason': 'الموظف غير موجود'}
            
            today = datetime.now().strftime('%Y-%m-%d')
            age = self.calculate_age(employee['birth_date'], today)
            service_years = self.calculate_service_years(employee['hire_date'], today)
            
            eligibility = {
                'eligible': False,
                'age': age,
                'service_years': service_years,
                'retirement_types': [],
                'recommendations': []
            }
            
            # تقاعد عادي (60 سنة)
            if age >= 60:
                eligibility['eligible'] = True
                eligibility['retirement_types'].append('تقاعد عادي')
                eligibility['recommendations'].append('مؤهل للتقاعد العادي')
            
            # تقاعد بدون شرط السن (32 سنة خدمة)
            if service_years >= 32:
                eligibility['eligible'] = True
                eligibility['retirement_types'].append('تقاعد بدون شرط السن')
                eligibility['recommendations'].append('مؤهل للتقاعد بدون شرط السن')
            
            # تقاعد مبكر (55 سنة + 15 سنة خدمة)
            if age >= 55 and service_years >= 15:
                eligibility['eligible'] = True
                eligibility['retirement_types'].append('تقاعد مبكر')
                eligibility['recommendations'].append('مؤهل للتقاعد المبكر')
            
            # تقاعد نسبي (15 سنة خدمة)
            if service_years >= 15:
                eligibility['retirement_types'].append('تقاعد نسبي')
                eligibility['recommendations'].append('مؤهل للتقاعد النسبي')
            
            if not eligibility['eligible']:
                years_to_60 = max(0, 60 - age)
                years_to_32_service = max(0, 32 - service_years)
                
                if years_to_60 <= years_to_32_service:
                    eligibility['recommendations'].append(f'سيكون مؤهلاً للتقاعد العادي خلال {years_to_60} سنة')
                else:
                    eligibility['recommendations'].append(f'سيكون مؤهلاً للتقاعد بدون شرط السن خلال {years_to_32_service} سنة')
            
            return eligibility
            
        finally:
            conn.close()
    
    def get_status_conflicts(self, employee_id: int, new_status: str) -> List[str]:
        """فحص تضارب الحالات"""
        conn = self.get_db_connection()
        conflicts = []
        
        try:
            # فحص الحالات النشطة
            active_statuses = []
            
            # فحص العطل طويلة الأمد
            long_leaves = conn.execute('''
                SELECT * FROM employee_long_term_leaves 
                WHERE employee_id = ? AND end_date IS NULL
            ''', (employee_id,)).fetchall()
            
            if long_leaves:
                active_statuses.append('عطلة طويلة الأمد')
            
            # فحص الاستيداع
            absences = conn.execute('''
                SELECT * FROM employee_leave_of_absence 
                WHERE employee_id = ? AND end_date IS NULL
            ''', (employee_id,)).fetchall()
            
            if absences:
                active_statuses.append('استيداع')
            
            # فحص التوقيف
            suspensions = conn.execute('''
                SELECT * FROM employee_suspensions 
                WHERE employee_id = ? AND end_date IS NULL
            ''', (employee_id,)).fetchall()
            
            if suspensions:
                active_statuses.append('توقيف')
            
            # فحص الانتداب
            assignments = conn.execute('''
                SELECT * FROM employee_assignments 
                WHERE employee_id = ? AND end_date IS NULL
            ''', (employee_id,)).fetchall()
            
            if assignments:
                active_statuses.append('انتداب')
            
            # فحص التضارب
            if active_statuses:
                if new_status in ['وفاة', 'تقاعد', 'استقالة', 'تحويل خارجي']:
                    conflicts.append(f'الموظف لديه حالة نشطة: {", ".join(active_statuses)}')
                elif new_status in active_statuses:
                    conflicts.append(f'الموظف لديه بالفعل حالة {new_status} نشطة')
            
            return conflicts
            
        finally:
            conn.close()
    
    def generate_status_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """إنشاء تقرير الحالات لفترة معينة"""
        conn = self.get_db_connection()
        
        try:
            report = {
                'period': {'start': start_date, 'end': end_date},
                'summary': {},
                'details': {},
                'trends': {}
            }
            
            # إحصائيات العطل طويلة الأمد
            long_leaves = conn.execute('''
                SELECT COUNT(*) as count, leave_type
                FROM employee_long_term_leaves 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY leave_type
            ''', (start_date, end_date)).fetchall()
            
            report['details']['long_term_leaves'] = [dict(row) for row in long_leaves]
            
            # إحصائيات الاستقالات
            resignations = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_resignations 
                WHERE created_at BETWEEN ? AND ?
            ''', (start_date, end_date)).fetchone()
            
            report['summary']['resignations'] = resignations['count'] if resignations else 0
            
            # إحصائيات التقاعد
            retirements = conn.execute('''
                SELECT COUNT(*) as count, retirement_type
                FROM employee_retirements 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY retirement_type
            ''', (start_date, end_date)).fetchall()
            
            report['details']['retirements'] = [dict(row) for row in retirements]
            
            # إحصائيات التحويلات الخارجية
            transfers = conn.execute('''
                SELECT COUNT(*) as count
                FROM employee_external_transfers 
                WHERE created_at BETWEEN ? AND ?
            ''', (start_date, end_date)).fetchone()
            
            report['summary']['external_transfers'] = transfers['count'] if transfers else 0
            
            return report
            
        finally:
            conn.close()
    
    def get_upcoming_events(self, days_ahead: int = 30) -> List[Dict[str, Any]]:
        """الحصول على الأحداث القادمة"""
        conn = self.get_db_connection()
        future_date = (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
        events = []
        
        try:
            # العطل المنتهية قريباً
            ending_leaves = conn.execute('''
                SELECT e.first_name, e.last_name, e.registration_number,
                       l.expected_end_date, l.leave_type
                FROM employee_long_term_leaves l
                JOIN employees e ON l.employee_id = e.id
                WHERE l.end_date IS NULL 
                AND l.expected_end_date <= ?
                AND l.expected_end_date >= date('now')
            ''', (future_date,)).fetchall()
            
            for leave in ending_leaves:
                events.append({
                    'type': 'انتهاء عطلة',
                    'employee': f"{leave['first_name']} {leave['last_name']}",
                    'registration_number': leave['registration_number'],
                    'date': leave['expected_end_date'],
                    'details': leave['leave_type']
                })
            
            # الموظفين المقاربين للتقاعد
            retiring_soon = conn.execute('''
                SELECT first_name, last_name, registration_number, birth_date, hire_date
                FROM employees
                WHERE status = 'نشط'
            ''').fetchall()
            
            for emp in retiring_soon:
                age = self.calculate_age(emp['birth_date'])
                service_years = self.calculate_service_years(emp['hire_date'])
                
                if age >= 58 or service_years >= 30:  # قريب من التقاعد
                    events.append({
                        'type': 'مقارب للتقاعد',
                        'employee': f"{emp['first_name']} {emp['last_name']}",
                        'registration_number': emp['registration_number'],
                        'date': None,
                        'details': f'العمر: {age} سنة، الخدمة: {service_years:.1f} سنة'
                    })
            
            return sorted(events, key=lambda x: x['date'] or '9999-12-31')
            
        finally:
            conn.close()
    
    def export_status_data(self, status_type: str, format: str = 'json') -> str:
        """تصدير بيانات الحالات"""
        conn = self.get_db_connection()
        
        try:
            data = []
            
            if status_type == 'long_term_leaves':
                rows = conn.execute('''
                    SELECT e.first_name, e.last_name, e.registration_number,
                           l.leave_type, l.start_date, l.expected_end_date, l.end_date,
                           l.reason, l.created_at
                    FROM employee_long_term_leaves l
                    JOIN employees e ON l.employee_id = e.id
                    ORDER BY l.created_at DESC
                ''').fetchall()
                
                data = [dict(row) for row in rows]
            
            elif status_type == 'resignations':
                rows = conn.execute('''
                    SELECT e.first_name, e.last_name, e.registration_number,
                           r.resignation_date, r.last_working_day, r.reason,
                           r.status, r.created_at
                    FROM employee_resignations r
                    JOIN employees e ON r.employee_id = e.id
                    ORDER BY r.created_at DESC
                ''').fetchall()
                
                data = [dict(row) for row in rows]
            
            elif status_type == 'external_transfers':
                rows = conn.execute('''
                    SELECT e.first_name, e.last_name, e.registration_number,
                           t.transfer_date, t.destination_organization,
                           t.reason, t.created_at
                    FROM employee_external_transfers t
                    JOIN employees e ON t.employee_id = e.id
                    ORDER BY t.created_at DESC
                ''').fetchall()
                
                data = [dict(row) for row in rows]
            
            if format == 'json':
                return json.dumps(data, ensure_ascii=False, indent=2, default=str)
            elif format == 'csv':
                if not data:
                    return ''
                
                import csv
                import io
                
                output = io.StringIO()
                if data:
                    writer = csv.DictWriter(output, fieldnames=data[0].keys())
                    writer.writeheader()
                    writer.writerows(data)
                
                return output.getvalue()
            
            return str(data)
            
        finally:
            conn.close()
    
    def validate_status_transition(self, employee_id: int, from_status: str, to_status: str) -> Tuple[bool, str]:
        """التحقق من صحة انتقال الحالة"""
        
        # القواعد المسموحة للانتقال
        allowed_transitions = {
            'نشط': ['عطلة طويلة الأمد', 'استيداع', 'توقيف', 'انتداب', 'استقالة', 'تقاعد', 'وفاة', 'تحويل خارجي'],
            'عطلة طويلة الأمد': ['نشط', 'استقالة', 'تقاعد', 'وفاة'],
            'استيداع': ['نشط', 'استقالة', 'تقاعد', 'وفاة'],
            'توقيف': ['نشط', 'استقالة', 'تقاعد', 'وفاة'],
            'انتداب': ['نشط', 'استقالة', 'تقاعد', 'وفاة'],
            'استقالة معلقة': ['نشط', 'استقالة', 'وفاة'],
            'استقالة': [],  # حالة نهائية
            'تقاعد': [],   # حالة نهائية
            'وفاة': [],    # حالة نهائية
            'تحويل خارجي': []  # حالة نهائية
        }
        
        if from_status not in allowed_transitions:
            return False, f'الحالة الحالية غير معروفة: {from_status}'
        
        if to_status not in allowed_transitions[from_status]:
            return False, f'لا يمكن الانتقال من {from_status} إلى {to_status}'
        
        # فحص التضارب
        conflicts = self.get_status_conflicts(employee_id, to_status)
        if conflicts:
            return False, f'تضارب في الحالات: {"; ".join(conflicts)}'
        
        return True, 'الانتقال مسموح'
    
    def get_status_statistics_detailed(self) -> Dict[str, Any]:
        """إحصائيات مفصلة للحالات"""
        conn = self.get_db_connection()
        
        try:
            stats = {
                'overview': {},
                'by_department': {},
                'by_age_group': {},
                'trends': {}
            }
            
            # الإحصائيات العامة
            total_employees = conn.execute('SELECT COUNT(*) as count FROM employees').fetchone()['count']
            
            active_employees = conn.execute(
                "SELECT COUNT(*) as count FROM employees WHERE status = 'نشط'"
            ).fetchone()['count']
            
            stats['overview'] = {
                'total_employees': total_employees,
                'active_employees': active_employees,
                'inactive_employees': total_employees - active_employees
            }
            
            # الإحصائيات حسب القسم
            dept_stats = conn.execute('''
                SELECT department, status, COUNT(*) as count
                FROM employees
                GROUP BY department, status
            ''').fetchall()
            
            for row in dept_stats:
                dept = row['department'] or 'غير محدد'
                if dept not in stats['by_department']:
                    stats['by_department'][dept] = {}
                stats['by_department'][dept][row['status']] = row['count']
            
            # الإحصائيات حسب الفئة العمرية
            employees_with_age = conn.execute('''
                SELECT birth_date, status FROM employees WHERE birth_date IS NOT NULL
            ''').fetchall()
            
            age_groups = {'أقل من 30': 0, '30-40': 0, '40-50': 0, '50-60': 0, 'أكثر من 60': 0}
            
            for emp in employees_with_age:
                age = self.calculate_age(emp['birth_date'])
                if age < 30:
                    group = 'أقل من 30'
                elif age < 40:
                    group = '30-40'
                elif age < 50:
                    group = '40-50'
                elif age < 60:
                    group = '50-60'
                else:
                    group = 'أكثر من 60'
                
                age_groups[group] += 1
            
            stats['by_age_group'] = age_groups
            
            return stats
            
        finally:
            conn.close()

def create_status_helpers(db_path: str = 'employees.db') -> EmployeeStatusHelpers:
    """إنشاء مثيل من مساعدات الحالات"""
    return EmployeeStatusHelpers(db_path)

# دوال مساعدة سريعة
def format_arabic_date(date_str: str) -> str:
    """تنسيق التاريخ بالعربية"""
    if not date_str:
        return ''
    
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        
        arabic_months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        
        day = date_obj.day
        month = arabic_months[date_obj.month - 1]
        year = date_obj.year
        
        return f'{day} {month} {year}'
    except:
        return date_str

def get_status_badge_class(status: str) -> str:
    """الحصول على كلاس CSS للحالة"""
    status_classes = {
        'نشط': 'badge-success',
        'عطلة طويلة الأمد': 'badge-warning',
        'استيداع': 'badge-info',
        'توقيف': 'badge-danger',
        'انتداب': 'badge-primary',
        'استقالة معلقة': 'badge-warning',
        'استقالة': 'badge-secondary',
        'تقاعد': 'badge-dark',
        'وفاة': 'badge-dark',
        'تحويل خارجي': 'badge-light'
    }
    
    return status_classes.get(status, 'badge-secondary')

def calculate_days_between(start_date: str, end_date: str) -> int:
    """حساب الأيام بين تاريخين"""
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        return (end - start).days
    except:
        return 0

if __name__ == "__main__":
    # اختبار المساعدات
    helpers = create_status_helpers()
    
    print("🧪 اختبار مساعدات الحالات...")
    
    # اختبار حساب العمر
    age = helpers.calculate_age('1990-01-01')
    print(f"العمر المحسوب: {age} سنة")
    
    # اختبار حساب سنوات الخدمة
    service = helpers.calculate_service_years('2010-01-01')
    print(f"سنوات الخدمة: {service} سنة")
    
    # اختبار تنسيق التاريخ
    formatted_date = format_arabic_date('2024-01-15')
    print(f"التاريخ المنسق: {formatted_date}")
    
    print("✅ انتهى اختبار المساعدات")