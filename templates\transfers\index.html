{% extends "base.html" %}

{% block title %}التنقلات والحركات - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة التنقلات والحركات{% endblock %}

{% block content %}
<!-- إحصائيات -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="stats-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <i class="fas fa-exchange-alt fa-2x mb-2"></i>
            <h3>{{ transfers|length }}</h3>
            <p>إجمالي التنقلات</p>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>إضافة تنقل جديد
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="addTransfer('internal')">
                    <i class="fas fa-building me-2"></i>تنقل داخلي
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="addTransfer('external')">
                    <i class="fas fa-exchange-alt me-2"></i>تحويل خارجي
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="addTransfer('secondment')">
                    <i class="fas fa-user-tie me-2"></i>انتداب
                </a></li>
            </ul>
        </div>
        <button class="btn btn-success" onclick="exportData()">
            <i class="fas fa-file-excel me-2"></i>تصدير البيانات
        </button>
    </div>
</div>

<!-- جدول التنقلات -->
<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-exchange-alt me-2"></i>التنقلات والحركات</h4>
    </div>
    <div class="card-body">
        {% if transfers %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>من المديرية</th>
                        <th>إلى المديرية</th>
                        <th>تاريخ التنصيب</th>
                        <th>تاريخ انتهاء المهام</th>
                        <th>المدة</th>
                        <th>الوظيفة</th>
                        <th>رقم المقرر</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transfer in transfers %}
                    <tr>
                        <td>
                            <strong>{{ transfer.first_name }} {{ transfer.last_name }}</strong>
                            <br><small class="text-muted">{{ transfer.registration_number }}</small>
                        </td>
                        <td>{{ transfer.from_directorate_name or 'غير محدد' }}</td>
                        <td>{{ transfer.to_directorate_name or 'غير محدد' }}</td>
                        <td>{{ transfer.assignment_date or 'غير محدد' }}</td>
                        <td>{{ transfer.end_date or 'مستمر' }}</td>
                        <td>
                            {% if transfer.assignment_date and transfer.end_date %}
                                {% set start = transfer.assignment_date %}
                                {% set end = transfer.end_date %}
                                <span class="badge bg-info">محسوبة</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>{{ transfer.position or 'غير محدد' }}</td>
                        <td>{{ transfer.decision_number or 'غير محدد' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تنقلات مسجلة</h5>
            <p class="text-muted">ابدأ بإضافة تنقل جديد لعرض البيانات هنا</p>
            <button class="btn btn-primary" onclick="addTransfer('internal')">
                <i class="fas fa-plus me-2"></i>إضافة تنقل جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal إضافة تنقل -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exchange-alt me-2"></i>إضافة تنقل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="transferForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموظف <span class="required">*</span></label>
                                <select name="employee_id" class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع التنقل <span class="required">*</span></label>
                                <select name="transfer_type" class="form-select" required>
                                    <option value="">اختر النوع</option>
                                    <option value="تنقل داخلي">تنقل داخلي</option>
                                    <option value="تحويل خارجي">تحويل خارجي</option>
                                    <option value="انتداب">انتداب</option>
                                    <option value="إعارة">إعارة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">من المديرية</label>
                                <select name="from_directorate_id" class="form-select">
                                    <option value="">اختر المديرية</option>
                                    <!-- سيتم ملؤها من قاعدة البيانات -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">إلى المديرية <span class="required">*</span></label>
                                <select name="to_directorate_id" class="form-select" required>
                                    <option value="">اختر المديرية</option>
                                    <!-- سيتم ملؤها من قاعدة البيانات -->
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">من المصلحة</label>
                                <select name="from_service_id" class="form-select">
                                    <option value="">اختر المصلحة</option>
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">إلى المصلحة</label>
                                <select name="to_service_id" class="form-select">
                                    <option value="">اختر المصلحة</option>
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">تاريخ التنصيب <span class="required">*</span></label>
                                <input type="date" name="assignment_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">تاريخ انتهاء المهام</label>
                                <input type="date" name="end_date" class="form-control">
                                <div class="form-text">اتركه فارغاً إذا كان مستمراً</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الوظيفة الجديدة</label>
                                <input type="text" name="position" class="form-control" placeholder="الوظيفة في المكان الجديد">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم مقرر التنقل</label>
                                <input type="text" name="decision_number" class="form-control" placeholder="رقم المقرر">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ مقرر التنقل</label>
                                <input type="date" name="decision_date" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="2" placeholder="ملاحظات إضافية حول التنقل"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التنقل</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addTransfer(type) {
    // فتح modal التنقل
    const modal = new bootstrap.Modal(document.getElementById('transferModal'));
    
    // تحديد نوع التنقل حسب النوع المختار
    const transferTypeSelect = document.querySelector('select[name="transfer_type"]');
    switch(type) {
        case 'internal':
            transferTypeSelect.value = 'تنقل داخلي';
            break;
        case 'external':
            transferTypeSelect.value = 'تحويل خارجي';
            break;
        case 'secondment':
            transferTypeSelect.value = 'انتداب';
            break;
    }
    
    modal.show();
}

function exportData() {
    alert('سيتم تصدير بيانات التنقلات إلى Excel');
    // يمكن إضافة وظيفة التصدير الفعلية
}

// معالجة نموذج التنقل
document.getElementById('transferForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const employeeId = document.querySelector('select[name="employee_id"]').value;
    const transferType = document.querySelector('select[name="transfer_type"]').value;
    const assignmentDate = document.querySelector('input[name="assignment_date"]').value;
    
    if (!employeeId || !transferType || !assignmentDate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    alert('تم حفظ التنقل بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
});
</script>
{% endblock %}
