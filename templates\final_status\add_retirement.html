{% extends "base.html" %}

{% block title %}تقاعد موظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-check"></i>
                        تقاعد موظف
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- تحذير مهم -->
                    <div class="alert alert-success">
                        <h6><i class="fas fa-info-circle"></i> معلومات مهمة:</h6>
                        <p class="mb-1">• سيتم نقل الموظف من جدول الموظفين النشطين إلى جدول المتقاعدين</p>
                        <p class="mb-1">• لن يُحسب الموظف في إجمالي عدد الموظفين النشطين</p>
                        <p class="mb-0">• سيتم الاحتفاظ بجميع بياناته في سجل منفصل</p>
                    </div>

                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user"></i> معلومات الموظف:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                                <p class="mb-1"><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الحالة الحالية:</strong> {{ employee.status }}</p>
                                {% if employee.hire_date %}
                                <p class="mb-0"><strong>تاريخ التوظيف:</strong> {{ employee.hire_date }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <form method="POST" onsubmit="return confirmRetirement()">
                        <div class="row">
                            <!-- تاريخ التقاعد -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="retirement_date">
                                        <i class="fas fa-calendar-alt"></i>
                                        تاريخ التقاعد <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="retirement_date" 
                                           name="retirement_date" required>
                                </div>
                            </div>

                            <!-- رقم مقرر التقاعد -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="retirement_decision_number">
                                        <i class="fas fa-file-alt"></i>
                                        رقم مقرر التقاعد <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="retirement_decision_number" 
                                           name="retirement_decision_number" required
                                           placeholder="رقم المقرر الإداري">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ مقرر التقاعد -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="retirement_decision_date">
                                        <i class="fas fa-calendar"></i>
                                        تاريخ مقرر التقاعد <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="retirement_decision_date" 
                                           name="retirement_decision_date" required>
                                </div>
                            </div>

                            <!-- رقم بطاقة التقاعد -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="retirement_card_number">
                                        <i class="fas fa-id-card"></i>
                                        رقم بطاقة التقاعد (اختياري)
                                    </label>
                                    <input type="text" class="form-control" id="retirement_card_number" 
                                           name="retirement_card_number"
                                           placeholder="رقم بطاقة التقاعد">
                                </div>
                            </div>
                        </div>

                        <!-- تاريخ إصدار بطاقة التقاعد -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="retirement_card_issue_date">
                                        <i class="fas fa-calendar-plus"></i>
                                        تاريخ إصدار بطاقة التقاعد (اختياري)
                                    </label>
                                    <input type="date" class="form-control" id="retirement_card_issue_date" 
                                           name="retirement_card_issue_date">
                                </div>
                            </div>
                            
                            <!-- حساب سنوات الخدمة -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="service_years">
                                        <i class="fas fa-clock"></i>
                                        سنوات الخدمة (محسوبة تلقائياً)
                                    </label>
                                    <input type="text" class="form-control" id="service_years" 
                                           readonly placeholder="سيتم حسابها تلقائياً">
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات إضافية -->
                        <div class="form-group">
                            <label for="notes">
                                <i class="fas fa-sticky-note"></i>
                                ملاحظات التقاعد (اختياري)
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="أي ملاحظات حول التقاعد..."></textarea>
                        </div>

                        <!-- تأكيد العملية -->
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_retirement" required>
                                <label class="custom-control-label text-success" for="confirm_retirement">
                                    <strong>أؤكد صحة المعلومات وأن الموظف سيتم تقاعده</strong>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-check"></i>
                                تأكيد التقاعد
                            </button>
                            <a href="{{ url_for('employees') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmRetirement() {
    const employeeName = "{{ employee.first_name }} {{ employee.last_name }}";
    const retirementDate = document.getElementById('retirement_date').value;
    const decisionNumber = document.getElementById('retirement_decision_number').value;
    const serviceYears = document.getElementById('service_years').value;
    
    const confirmMessage = `
هل أنت متأكد من تقاعد الموظف؟

الموظف: ${employeeName}
تاريخ التقاعد: ${retirementDate}
رقم المقرر: ${decisionNumber}
سنوات الخدمة: ${serviceYears}

تحذير: سيتم نقل الموظف لجدول المتقاعدين وحذفه من قائمة الموظفين النشطين.
هذا الإجراء لا يمكن التراجع عنه بسهولة.
    `;
    
    return confirm(confirmMessage);
}

// حساب سنوات الخدمة تلقائياً
function calculateServiceYears() {
    const hireDate = "{{ employee.hire_date }}";
    const retirementDate = document.getElementById('retirement_date').value;
    
    if (hireDate && retirementDate) {
        const hire = new Date(hireDate);
        const retirement = new Date(retirementDate);
        
        if (retirement > hire) {
            const years = retirement.getFullYear() - hire.getFullYear();
            const months = retirement.getMonth() - hire.getMonth();
            const days = retirement.getDate() - hire.getDate();
            
            let totalYears = years;
            if (months < 0 || (months === 0 && days < 0)) {
                totalYears--;
            }
            
            document.getElementById('service_years').value = `${totalYears} سنة`;
        }
    }
}

// ربط الحدث
document.getElementById('retirement_date').addEventListener('change', calculateServiceYears);

// التحقق من صحة التواريخ
document.getElementById('retirement_decision_date').addEventListener('change', function() {
    const decisionDate = new Date(this.value);
    const retirementDate = new Date(document.getElementById('retirement_date').value);
    
    if (retirementDate && decisionDate > retirementDate) {
        alert('تاريخ المقرر لا يمكن أن يكون بعد تاريخ التقاعد');
        this.value = '';
    }
});

document.getElementById('retirement_card_issue_date').addEventListener('change', function() {
    const cardDate = new Date(this.value);
    const retirementDate = new Date(document.getElementById('retirement_date').value);
    
    if (retirementDate && cardDate < retirementDate) {
        alert('تاريخ إصدار البطاقة لا يمكن أن يكون قبل تاريخ التقاعد');
        this.value = '';
    }
});
</script>

<style>
.custom-control-label {
    font-weight: bold;
}

.alert-success {
    border-left: 4px solid #28a745;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}
</style>
{% endblock %}