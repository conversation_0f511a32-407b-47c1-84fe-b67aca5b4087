{% extends "base.html" %}

{% block title %}انتداب الموظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}انتداب الموظف{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-user-tie me-2"></i>انتداب الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الرتبة:</strong> {{ employee.rank or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-success">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الانتداب -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>بيانات الانتداب
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="assignmentForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="assignment_date" class="form-label">تاريخ الانتداب <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="assignment_date" name="assignment_date" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="duration_months" class="form-label">مدة الانتداب (بالأشهر) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="duration_months" name="duration_months" 
                               min="1" max="60" required>
                        <div class="form-text">الحد الأقصى: 60 شهر (5 سنوات)</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الانتداب <span class="text-danger">*</span></label>
                        <select class="form-select" id="reason" name="reason" required>
                            <option value="">اختر سبب الانتداب</option>
                            <option value="تعزيز الخبرات">تعزيز الخبرات</option>
                            <option value="نقل المعرفة">نقل المعرفة</option>
                            <option value="مهمة خاصة">مهمة خاصة</option>
                            <option value="تطوير المهارات">تطوير المهارات</option>
                            <option value="احتياج إداري">احتياج إداري</option>
                            <option value="مشروع مؤقت">مشروع مؤقت</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="location" class="form-label">مكان الانتداب <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="location" name="location" 
                               placeholder="أدخل مكان الانتداب" required>
                    </div>
                </div>
            </div>
            
            <!-- حقل السبب المخصص -->
            <div class="row" id="customReasonRow" style="display: none;">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="custom_reason" class="form-label">تفاصيل سبب الانتداب</label>
                        <textarea class="form-control" id="custom_reason" name="custom_reason" rows="3" 
                                  placeholder="أدخل تفاصيل سبب الانتداب"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- معاينة تاريخ النهاية -->
            <div class="alert alert-info" id="endDatePreview" style="display: none;">
                <i class="fas fa-calendar-alt me-2"></i>
                <strong>تاريخ انتهاء الانتداب المتوقع:</strong> <span id="calculatedEndDate"></span>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="alert alert-success">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> سيحتفظ الموظف بجميع حقوقه الوظيفية أثناء فترة الانتداب، وسيعود إلى منصبه الأصلي عند انتهاء المدة.
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-info" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل الانتداب
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal تأكيد الانتداب -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>تأكيد انتداب الموظف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من انتداب الموظف:</p>
                <div class="text-center p-3 border rounded bg-light">
                    <h6><strong>{{ employee.first_name }} {{ employee.last_name }}</strong></h6>
                    <p class="mb-0">رقم التسجيل: {{ employee.registration_number }}</p>
                </div>
                <div class="mt-3">
                    <p><strong>تاريخ الانتداب:</strong> <span id="confirmDate"></span></p>
                    <p><strong>المدة:</strong> <span id="confirmDuration"></span> شهر</p>
                    <p><strong>المكان:</strong> <span id="confirmLocation"></span></p>
                    <p><strong>السبب:</strong> <span id="confirmReason"></span></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>تأكيد الانتداب
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إظهار/إخفاء حقل السبب المخصص
document.getElementById('reason').addEventListener('change', function() {
    const customReasonRow = document.getElementById('customReasonRow');
    if (this.value === 'أخرى') {
        customReasonRow.style.display = 'block';
        document.getElementById('custom_reason').required = true;
    } else {
        customReasonRow.style.display = 'none';
        document.getElementById('custom_reason').required = false;
    }
});

// حساب تاريخ النهاية عند تغيير تاريخ البداية أو المدة
function calculateEndDate() {
    const startDate = document.getElementById('assignment_date').value;
    const duration = document.getElementById('duration_months').value;
    
    if (startDate && duration) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setMonth(end.getMonth() + parseInt(duration));
        
        const endDateFormatted = end.toISOString().split('T')[0];
        document.getElementById('calculatedEndDate').textContent = endDateFormatted;
        document.getElementById('endDatePreview').style.display = 'block';
    } else {
        document.getElementById('endDatePreview').style.display = 'none';
    }
}

document.getElementById('assignment_date').addEventListener('change', calculateEndDate);
document.getElementById('duration_months').addEventListener('input', calculateEndDate);

document.getElementById('assignmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const assignmentDate = document.getElementById('assignment_date').value;
    const duration = document.getElementById('duration_months').value;
    const reason = document.getElementById('reason').value;
    const location = document.getElementById('location').value;
    
    if (!assignmentDate || !duration || !reason || !location) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من السبب المخصص إذا كان مطلوباً
    if (reason === 'أخرى') {
        const customReason = document.getElementById('custom_reason').value.trim();
        if (!customReason) {
            alert('يرجى إدخال تفاصيل سبب الانتداب');
            return;
        }
    }
    
    // تحديث بيانات التأكيد
    document.getElementById('confirmDate').textContent = assignmentDate;
    document.getElementById('confirmDuration').textContent = duration;
    document.getElementById('confirmLocation').textContent = location;
    document.getElementById('confirmReason').textContent = reason === 'أخرى' ? 
        document.getElementById('custom_reason').value : reason;
    
    // إظهار مودال التأكيد
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
});

document.getElementById('confirmSubmit').addEventListener('click', function() {
    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
    
    // تعطيل الزر وإظهار مؤشر التحميل
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
    
    // تحديث قيمة السبب إذا كان مخصصاً
    const reasonSelect = document.getElementById('reason');
    if (reasonSelect.value === 'أخرى') {
        reasonSelect.value = document.getElementById('custom_reason').value;
    }
    
    // إرسال النموذج
    document.getElementById('assignmentForm').submit();
});

// تحديد تاريخ اليوم كحد أدنى
document.getElementById('assignment_date').min = new Date().toISOString().split('T')[0];
</script>
{% endblock %}