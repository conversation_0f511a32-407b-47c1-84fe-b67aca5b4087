{% extends "base.html" %}

{% block title %}العطل والإجازات - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة العطل والإجازات{% endblock %}

{% block content %}
<!-- إحصائيات العطل -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <i class="fas fa-calendar-check fa-2x mb-2"></i>
            <h3>{{ annual_leaves|length }}</h3>
            <p>العطل السنوية</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <i class="fas fa-user-md fa-2x mb-2"></i>
            <h3>{{ sick_leaves|length }}</h3>
            <p>العطل المرضية</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <i class="fas fa-calendar-plus fa-2x mb-2"></i>
            <h3>{{ other_leaves|length }}</h3>
            <p>العطل الأخرى</p>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>إضافة عطلة جديدة
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ url_for('add_annual_leave') }}">
                    <i class="fas fa-calendar-check me-2"></i>عطلة سنوية
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('add_sick_leave') }}">
                    <i class="fas fa-user-md me-2"></i>عطلة مرضية
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('add_other_leave') }}">
                    <i class="fas fa-calendar-plus me-2"></i>عطلة أخرى
                </a></li>
            </ul>
        </div>
        <button class="btn btn-success" onclick="exportLeaves()">
            <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
        </button>
    </div>
</div>

<!-- العطل السنوية -->
<div class="card mb-4">
    <div class="card-header">
        <h4><i class="fas fa-calendar-check me-2"></i>العطل السنوية</h4>
    </div>
    <div class="card-body">
        {% if annual_leaves %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>السنة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>عدد الأيام</th>
                        <th>المتبقي</th>
                        <th>الوجهة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in annual_leaves %}
                    <tr>
                        <td>
                            <strong>{{ leave.first_name }} {{ leave.last_name }}</strong>
                            <br><small class="text-muted">{{ leave.registration_number }}</small>
                        </td>
                        <td>{{ leave.leave_year }}</td>
                        <td>{{ leave.start_date }}</td>
                        <td>{{ leave.end_date }}</td>
                        <td>
                            <span class="badge bg-primary">{{ leave.days_count }} يوم</span>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ leave.remaining_days or 0 }} يوم</span>
                        </td>
                        <td>{{ leave.destination or 'غير محدد' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عطل سنوية مسجلة</h5>
            <a href="{{ url_for('add_annual_leave') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة عطلة سنوية
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- العطل المرضية -->
<div class="card mb-4">
    <div class="card-header">
        <h4><i class="fas fa-user-md me-2"></i>العطل المرضية</h4>
    </div>
    <div class="card-body">
        {% if sick_leaves %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع العطلة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>عدد الأيام</th>
                        <th>مؤشرة</th>
                        <th>رأي الطبيب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in sick_leaves %}
                    <tr>
                        <td>
                            <strong>{{ leave.first_name }} {{ leave.last_name }}</strong>
                            <br><small class="text-muted">{{ leave.registration_number }}</small>
                        </td>
                        <td>{{ leave.sick_leave_type }}</td>
                        <td>{{ leave.start_date }}</td>
                        <td>{{ leave.end_date }}</td>
                        <td>
                            <span class="badge bg-warning">{{ leave.days_count }} يوم</span>
                        </td>
                        <td>
                            {% if leave.is_indexed %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if leave.doctor_opinion == 'مقبولة' %}
                                <span class="badge bg-success">مقبولة</span>
                            {% elif leave.doctor_opinion == 'غير مقبولة' %}
                                <span class="badge bg-danger">غير مقبولة</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عطل مرضية مسجلة</h5>
            <button class="btn btn-warning" onclick="addLeave('sick')">
                <i class="fas fa-plus me-2"></i>إضافة عطلة مرضية
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- العطل الأخرى -->
<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-calendar-plus me-2"></i>العطل الأخرى</h4>
    </div>
    <div class="card-body">
        {% if other_leaves %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع العطلة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>عدد الأيام</th>
                        <th>السبب</th>
                        <th>خصم الراتب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in other_leaves %}
                    <tr>
                        <td>
                            <strong>{{ leave.first_name }} {{ leave.last_name }}</strong>
                            <br><small class="text-muted">{{ leave.registration_number }}</small>
                        </td>
                        <td>{{ leave.leave_type }}</td>
                        <td>{{ leave.start_date }}</td>
                        <td>{{ leave.end_date }}</td>
                        <td>
                            <span class="badge bg-info">{{ leave.days_count }} يوم</span>
                        </td>
                        <td>{{ leave.reason or 'غير محدد' }}</td>
                        <td>
                            {% if leave.salary_deduction %}
                                <span class="badge bg-danger">نعم</span>
                            {% else %}
                                <span class="badge bg-success">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عطل أخرى مسجلة</h5>
            <button class="btn btn-info" onclick="addLeave('other')">
                <i class="fas fa-plus me-2"></i>إضافة عطلة أخرى
            </button>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addLeave(type) {
    let url = '';
    switch(type) {
        case 'annual':
            url = '{{ url_for("add_annual_leave") }}';
            break;
        case 'sick':
            url = '/leaves/sick/add';
            break;
        case 'other':
            url = '/leaves/other/add';
            break;
    }
    if (url) {
        window.location.href = url;
    }
}

function exportLeaves() {
    alert('سيتم تصدير بيانات العطل إلى Excel');
    // يمكن إضافة وظيفة التصدير الفعلية
}
</script>
{% endblock %}
