{% extends "base.html" %}

{% block title %}العطل والإجازات - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-calendar-alt"></i>
                    العطل والإجازات
                </h1>
                <p class="page-subtitle">إدارة العطل السنوية والمرضية وأنواع الإجازات الأخرى</p>
            </div>
        </div>
    </div>

    <!-- Leave Types Cards -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4">
            <div class="card leave-type-card annual-leave">
                <div class="card-body text-center">
                    <div class="leave-icon mb-3">
                        <i class="fas fa-calendar-check fa-3x"></i>
                    </div>
                    <h5 class="card-title">العطل السنوية</h5>
                    <p class="card-text">إدارة العطل السنوية والرصيد المتاح</p>
                    <div class="leave-stats mb-3">
                        <div class="row">
                            <div class="col-6">
                                <h6 class="mb-0">المجموع</h6>
                                <span class="badge bg-primary">0</span>
                            </div>
                            <div class="col-6">
                                <h6 class="mb-0">النشطة</h6>
                                <span class="badge bg-success">0</span>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLeaveModal" data-type="annual">
                        <i class="fas fa-plus"></i> إضافة عطلة سنوية
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card leave-type-card sick-leave">
                <div class="card-body text-center">
                    <div class="leave-icon mb-3">
                        <i class="fas fa-user-injured fa-3x"></i>
                    </div>
                    <h5 class="card-title">العطل المرضية</h5>
                    <p class="card-text">إدارة العطل المرضية والإشراف الطبي</p>
                    <div class="leave-stats mb-3">
                        <div class="row">
                            <div class="col-6">
                                <h6 class="mb-0">المجموع</h6>
                                <span class="badge bg-warning">0</span>
                            </div>
                            <div class="col-6">
                                <h6 class="mb-0">النشطة</h6>
                                <span class="badge bg-danger">0</span>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addLeaveModal" data-type="sick">
                        <i class="fas fa-plus"></i> إضافة عطلة مرضية
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card leave-type-card other-leave">
                <div class="card-body text-center">
                    <div class="leave-icon mb-3">
                        <i class="fas fa-calendar-times fa-3x"></i>
                    </div>
                    <h5 class="card-title">العطل الأخرى</h5>
                    <p class="card-text">إدارة العطل الاستثنائية والظروف الخاصة</p>
                    <div class="leave-stats mb-3">
                        <div class="row">
                            <div class="col-6">
                                <h6 class="mb-0">المجموع</h6>
                                <span class="badge bg-info">0</span>
                            </div>
                            <div class="col-6">
                                <h6 class="mb-0">النشطة</h6>
                                <span class="badge bg-secondary">0</span>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addLeaveModal" data-type="other">
                        <i class="fas fa-plus"></i> إضافة عطلة أخرى
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaves Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table"></i> سجل العطل والإجازات
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-filter"></i> تصفية
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-file-excel"></i> تصدير
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filter Tabs -->
                    <ul class="nav nav-tabs mb-3" id="leaveTabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#all-leaves">جميع العطل</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#annual-leaves">العطل السنوية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#sick-leaves">العطل المرضية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#other-leaves">العطل الأخرى</a>
                        </li>
                    </ul>
                    
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="all-leaves">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>نوع العطلة</th>
                                            <th>تاريخ البداية</th>
                                            <th>تاريخ النهاية</th>
                                            <th>عدد الأيام</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="empty-state">
                                                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                                                    <h5 class="text-muted">لا توجد عطل مسجلة</h5>
                                                    <p class="text-muted">ابدأ بإضافة عطلة جديدة</p>
                                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLeaveModal">
                                                        <i class="fas fa-plus"></i> إضافة عطلة جديدة
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- باقي التبويبات ستكون مماثلة -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Leave Modal -->
<div class="modal fade" id="addLeaveModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-plus"></i> إضافة عطلة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addLeaveForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الموظف <span class="text-danger">*</span></label>
                            <select class="form-select" name="employee_id" required>
                                <option value="">اختر الموظف</option>
                                <!-- سيتم ملؤها ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع العطلة <span class="text-danger">*</span></label>
                            <select class="form-select" name="leave_type" required>
                                <option value="">اختر نوع العطلة</option>
                                <option value="annual">عطلة سنوية</option>
                                <option value="sick">عطلة مرضية</option>
                                <option value="maternity">عطلة أمومة</option>
                                <option value="emergency">عطلة طارئة</option>
                                <option value="study">عطلة دراسية</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="start_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ النهاية <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="end_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">عدد الأيام</label>
                            <input type="number" class="form-control" name="days_count" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الوجهة</label>
                            <input type="text" class="form-control" name="destination">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم القرار</label>
                            <input type="text" class="form-control" name="decision_number">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ القرار</label>
                            <input type="date" class="form-control" name="decision_date">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">السبب/الملاحظات</label>
                            <textarea class="form-control" name="reason" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.leave-type-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.leave-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.annual-leave .leave-icon {
    color: #3498db;
}

.sick-leave .leave-icon {
    color: #f39c12;
}

.other-leave .leave-icon {
    color: #17a2b8;
}

.leave-stats h6 {
    font-size: 0.8rem;
    color: #6c757d;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.empty-state {
    padding: 40px 20px;
}

.nav-tabs .nav-link {
    color: #6c757d;
    border: none;
    border-bottom: 2px solid transparent;
}

.nav-tabs .nav-link.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: none;
}
</style>

<script>
// حساب عدد الأيام تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    const daysCountInput = document.querySelector('input[name="days_count"]');
    
    function calculateDays() {
        if (startDateInput.value && endDateInput.value) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);
            const timeDiff = endDate.getTime() - startDate.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
            daysCountInput.value = daysDiff > 0 ? daysDiff : 0;
        }
    }
    
    startDateInput.addEventListener('change', calculateDays);
    endDateInput.addEventListener('change', calculateDays);
});
</script>
{% endblock %}
