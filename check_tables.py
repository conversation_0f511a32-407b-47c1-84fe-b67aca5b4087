#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جداول قاعدة البيانات وإنشاء جداول الحالات الخاصة
"""

import sqlite3
from datetime import datetime

def check_database_tables():
    """فحص الجداول الموجودة في قاعدة البيانات"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()

        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        print("الجداول الموجودة في قاعدة البيانات:")
        print("=" * 50)

        for table in tables:
            table_name = table[0]
            print(f"📋 {table_name}")

            # الحصول على هيكل الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()

            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
            print()

        conn.close()

    except Exception as e:
        print(f"خطأ في فحص قاعدة البيانات: {e}")

def create_special_status_tables():
    """إنشاء جداول الحالات الخاصة"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()

        print("🔧 إنشاء جداول الحالات الخاصة...")

        # جدول الاستقالات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_resignations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                resignation_date DATE NOT NULL,
                reason TEXT,
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                notes TEXT,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')

        # جدول الوفيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_deaths (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                death_date DATE NOT NULL,
                death_place TEXT,
                death_cause TEXT,
                certificate_number TEXT,
                burial_place TEXT,
                family_contact TEXT,
                decision_number TEXT,
                decision_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')

        # جدول التقاعد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_retirements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                retirement_date DATE NOT NULL,
                retirement_type TEXT NOT NULL,
                years_of_service INTEGER,
                pension_amount DECIMAL(10,2),
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')

        # جدول التحويل الخارجي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_external_transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                transfer_date DATE NOT NULL,
                destination_organization TEXT NOT NULL,
                destination_position TEXT,
                reason TEXT,
                decision_number TEXT,
                decision_date DATE,
                effective_date DATE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')

        # جدول الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_leave_of_absence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE,
                duration_months INTEGER,
                reason TEXT NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                status TEXT DEFAULT 'active',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')

        # جدول التوقيف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_suspensions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                suspension_date DATE NOT NULL,
                end_date DATE,
                reason TEXT NOT NULL,
                decision_number TEXT,
                decision_date DATE,
                status TEXT DEFAULT 'active',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')

        conn.commit()
        conn.close()

        print("✅ تم إنشاء جداول الحالات الخاصة بنجاح!")

    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")

if __name__ == '__main__':
    check_database_tables()
    print("\n" + "=" * 50)
    create_special_status_tables()
