#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء موظف تجريبي لاختبار صفحة المعاينة
"""

import sqlite3
from datetime import date

def create_test_employee():
    """إنشاء موظف تجريبي"""
    print("👤 إنشاء موظف تجريبي...")
    
    try:
        conn = sqlite3.connect('customs_employees.db')
        
        # بيانات الموظف التجريبي
        employee_data = {
            'registration_number': '123456',
            'first_name': 'أحمد',
            'last_name': 'بن علي',
            'first_name_fr': '<PERSON>',
            'last_name_fr': '<PERSON>',
            'birth_date': '1985-05-15',
            'birth_wilaya_id': 1,
            'birth_commune_id': 1,
            'gender': 'ذكر',
            'social_security_number': '***************',
            'marital_status': 'متزوج',
            'children_count': 2,
            'dependents_count': 1,
            'blood_type': 'O+',
            'sport_practiced': 'كرة القدم',
            'hire_date': '2010-09-01',
            'current_rank_id': 1,
            'corps_id': 1,
            'current_service_id': 1,
            'postal_account': '**********',
            'phone1': '**********',
            'phone2': '**********',
            'email': '<EMAIL>',
            'address': 'حي النصر، الجزائر العاصمة',
            'status': 'نشط',
            'photo': None
        }
        
        # التحقق من وجود الموظف
        existing = conn.execute('SELECT id FROM employees WHERE registration_number = ?', 
                               (employee_data['registration_number'],)).fetchone()
        
        if existing:
            print(f"✅ الموظف موجود بالفعل برقم ID: {existing[0]}")
            conn.close()
            return existing[0]
        
        # إدراج الموظف الجديد
        cursor = conn.execute('''
            INSERT INTO employees (
                registration_number, first_name, last_name, first_name_fr, last_name_fr,
                birth_date, birth_wilaya_id, birth_commune_id, gender, social_security_number,
                marital_status, children_count, dependents_count, blood_type, sport_practiced,
                hire_date, current_rank_id, corps_id, current_service_id, postal_account,
                phone1, phone2, email, address, status, photo
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            employee_data['registration_number'], employee_data['first_name'], 
            employee_data['last_name'], employee_data['first_name_fr'], 
            employee_data['last_name_fr'], employee_data['birth_date'],
            employee_data['birth_wilaya_id'], employee_data['birth_commune_id'],
            employee_data['gender'], employee_data['social_security_number'],
            employee_data['marital_status'], employee_data['children_count'],
            employee_data['dependents_count'], employee_data['blood_type'],
            employee_data['sport_practiced'], employee_data['hire_date'],
            employee_data['current_rank_id'], employee_data['corps_id'],
            employee_data['current_service_id'], employee_data['postal_account'],
            employee_data['phone1'], employee_data['phone2'], employee_data['email'],
            employee_data['address'], employee_data['status'], employee_data['photo']
        ))
        
        employee_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"✅ تم إنشاء الموظف التجريبي برقم ID: {employee_id}")
        print(f"📋 الاسم: {employee_data['first_name']} {employee_data['last_name']}")
        print(f"🆔 رقم التسجيل: {employee_data['registration_number']}")
        
        return employee_id
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الموظف: {e}")
        return None

def test_employee_view(employee_id):
    """اختبار صفحة المعاينة مع الموظف التجريبي"""
    from app import app
    
    print(f"\n🔍 اختبار صفحة المعاينة للموظف رقم {employee_id}...")
    
    with app.test_client() as client:
        try:
            response = client.get(f'/employee/{employee_id}')
            
            if response.status_code == 200:
                print("✅ صفحة المعاينة تعمل بشكل مثالي!")
                content = response.data.decode('utf-8')
                
                # التحقق من وجود البيانات المهمة
                checks = [
                    ('أحمد بن علي', 'الاسم'),
                    ('Ahmed Ben Ali', 'الاسم بالفرنسية'),
                    ('123456', 'رقم التسجيل'),
                    ('متزوج', 'الحالة العائلية'),
                    ('O+', 'زمرة الدم'),
                    ('كرة القدم', 'الرياضة'),
                    ('**********', 'رقم الهاتف 1'),
                    ('**********', 'رقم الهاتف 2'),
                    ('<EMAIL>', 'البريد الإلكتروني')
                ]
                
                for check_text, description in checks:
                    if check_text in content:
                        print(f"   ✅ {description}: موجود")
                    else:
                        print(f"   ❌ {description}: مفقود")
                        
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    employee_id = create_test_employee()
    if employee_id:
        test_employee_view(employee_id)
        print(f"\n🌐 للاختبار اليدوي:")
        print(f"   اذهب إلى: http://localhost:5000/employee/{employee_id}")
        print(f"   أو: http://localhost:5000/employees (ثم انقر على زر العين)")