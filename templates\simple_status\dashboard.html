{% extends "base.html" %}

{% block title %}لوحة تحكم حالات الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-users-cog"></i>
                        لوحة تحكم حالات الموظفين
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.active or 0 }}</h3>
                                    <p class="mb-0">موظف نشط</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.total_active or 0 }}</h3>
                                    <p class="mb-0">إجمالي النشطين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.total_removed or 0 }}</h3>
                                    <p class="mb-0">المحذوفين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>{{ stats.grand_total or 0 }}</h3>
                                    <p class="mb-0">الإجمالي العام</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الحالات المؤقتة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary">
                                <i class="fas fa-clock"></i>
                                الحالات المؤقتة (يُحسبون في العدد)
                            </h5>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h4 class="text-warning">{{ stats.leave_of_absence or 0 }}</h4>
                                    <small>مستودع</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h4 class="text-danger">{{ stats.suspension or 0 }}</h4>
                                    <small>موقوف</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h4 class="text-info">{{ stats.resignation or 0 }}</h4>
                                    <small>مستقيل</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-secondary">
                                <div class="card-body text-center">
                                    <h4 class="text-secondary">0</h4>
                                    <small>أخرى</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الحالات النهائية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-danger">
                                <i class="fas fa-user-times"></i>
                                الحالات النهائية (لا يُحسبون في العدد)
                            </h5>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-dark">
                                <div class="card-body text-center">
                                    <h4 class="text-dark">{{ stats.deceased or 0 }}</h4>
                                    <small>متوفى</small>
                                    <br>
                                    <a href="{{ url_for('simple_status.deceased_list') }}" 
                                       class="btn btn-sm btn-outline-dark mt-2">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h4 class="text-warning">{{ stats.external_transfer or 0 }}</h4>
                                    <small>محول خارجياً</small>
                                    <br>
                                    <a href="{{ url_for('final_status.external_transfers_list') }}" class="btn btn-sm btn-outline-warning mt-2">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <h4 class="text-danger">{{ stats.dismissed or 0 }}</h4>
                                    <small>معزول</small>
                                    <br>
                                    <a href="{{ url_for('final_status.dismissed_list') }}" class="btn btn-sm btn-outline-danger mt-2">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h4 class="text-success">{{ stats.retired or 0 }}</h4>
                                    <small>متقاعد</small>
                                    <br>
                                    <a href="{{ url_for('final_status.retired_list') }}" class="btn btn-sm btn-outline-success mt-2">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- روابط سريعة -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="text-info">
                                <i class="fas fa-link"></i>
                                روابط سريعة
                            </h5>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h6>قائمة الموظفين</h6>
                                    <a href="{{ url_for('employees') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-list"></i> عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-bar fa-2x text-success mb-2"></i>
                                    <h6>الإحصائيات</h6>
                                    <button class="btn btn-success btn-sm" onclick="updateStats()">
                                        <i class="fas fa-sync"></i> تحديث
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-home fa-2x text-info mb-2"></i>
                                    <h6>الصفحة الرئيسية</h6>
                                    <a href="{{ url_for('index') }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-home"></i> الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateStats() {
    fetch('/status/api/statistics')
        .then(response => response.json())
        .then(data => {
            location.reload(); // إعادة تحميل الصفحة لعرض الإحصائيات المحدثة
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
            alert('خطأ في تحديث الإحصائيات');
        });
}

// تحديث تلقائي كل دقيقة
setInterval(updateStats, 60000);
</script>
{% endblock %}