#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الحالات الخاصة البسيط
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class SimpleSpecialStatusManager:
    """مدير الحالات الخاصة البسيط"""
    
    def __init__(self, db_path: str = 'customs_employees.db'):
        self.db_path = db_path
        self.MAX_LEAVE_OF_ABSENCE_YEARS = 5
        self.ensure_tables()
    
    def ensure_tables(self):
        """التأكد من وجود الجداول"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول أسباب الاستيداع
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    reason TEXT NOT NULL UNIQUE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إدراج أسباب افتراضية
            default_reasons = [
                'رعاية الأطفال',
                'الدراسة', 
                'ظروف شخصية',
                'ظروف صحية',
                'مرافقة الزوج',
                'ظروف عائلية',
                'أسباب أخرى'
            ]
            
            for reason in default_reasons:
                cursor.execute('''
                    INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
                    VALUES (?)
                ''', (reason,))
            
            # جدول الاستيداع المحدث
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_leave_of_absence_updated (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    period_number INTEGER NOT NULL,
                    duration_months INTEGER NOT NULL,
                    reason TEXT NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    decision_number TEXT,
                    decision_date DATE,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"تحذير: {e}")
    
    def get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_leave_reasons(self) -> List:
        """الحصول على قائمة أسباب الاستيداع"""
        conn = self.get_db_connection()
        try:
            reasons = conn.execute('''
                SELECT * FROM leave_of_absence_reasons 
                WHERE is_active = 1 
                ORDER BY reason
            ''').fetchall()
            return reasons
        except:
            return []
        finally:
            conn.close()
    
    def add_leave_reason(self, reason: str) -> bool:
        """إضافة سبب استيداع جديد"""
        try:
            conn = self.get_db_connection()
            conn.execute('''
                INSERT INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
            conn.commit()
            conn.close()
            return True
        except:
            return False
    
    def update_leave_reason(self, reason_id: int, new_reason: str) -> bool:
        """تحديث سبب الاستيداع"""
        try:
            conn = self.get_db_connection()
            conn.execute('''
                UPDATE leave_of_absence_reasons 
                SET reason = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_reason, reason_id))
            conn.commit()
            conn.close()
            return True
        except:
            return False
    
    def deactivate_leave_reason(self, reason_id: int) -> bool:
        """إلغاء تفعيل سبب الاستيداع"""
        try:
            conn = self.get_db_connection()
            conn.execute('''
                UPDATE leave_of_absence_reasons 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (reason_id,))
            conn.commit()
            conn.close()
            return True
        except:
            return False
    
    def get_employee_leave_balance(self, employee_id: int) -> Dict:
        """حساب رصيد الاستيداع للموظف"""
        conn = self.get_db_connection()
        
        try:
            total_months = conn.execute('''
                SELECT COALESCE(SUM(duration_months), 0) 
                FROM employee_leave_of_absence_updated 
                WHERE employee_id = ?
            ''', (employee_id,)).fetchone()[0]
        except:
            total_months = 0
        
        max_months = self.MAX_LEAVE_OF_ABSENCE_YEARS * 12
        remaining_months = max_months - total_months
        
        conn.close()
        
        return {
            'total_used_months': total_months,
            'total_used_years': total_months / 12,
            'remaining_months': remaining_months,
            'remaining_years': remaining_months / 12,
            'max_allowed_years': self.MAX_LEAVE_OF_ABSENCE_YEARS
        }
    
    def calculate_end_date(self, start_date: str, duration_months: int) -> str:
        """حساب تاريخ النهاية"""
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = start + timedelta(days=duration_months * 30)
            return end.strftime('%Y-%m-%d')
        except:
            return start_date
    
    def add_leave_of_absence(self, data: Dict) -> tuple:
        """إضافة استيداع جديد"""
        try:
            # التحقق من الرصيد
            balance = self.get_employee_leave_balance(data['employee_id'])
            if balance['remaining_months'] < data['duration_months']:
                return False, f"تجاوز الحد الأقصى. المتاح: {balance['remaining_months']} شهر"
            
            # حساب تاريخ النهاية
            end_date = self.calculate_end_date(data['start_date'], data['duration_months'])
            
            # حساب رقم الفترة
            conn = self.get_db_connection()
            period_count = conn.execute('''
                SELECT COUNT(*) FROM employee_leave_of_absence_updated 
                WHERE employee_id = ?
            ''', (data['employee_id'],)).fetchone()[0]
            
            period_number = period_count + 1
            
            conn.execute('''
                INSERT INTO employee_leave_of_absence_updated 
                (employee_id, period_number, duration_months, reason, start_date, end_date,
                 decision_number, decision_date, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['employee_id'],
                period_number,
                data['duration_months'],
                data['reason'],
                data['start_date'],
                end_date,
                data.get('decision_number', ''),
                data.get('decision_date', ''),
                'active'
            ))
            
            conn.commit()
            conn.close()
            return True, "تم إضافة الاستيداع بنجاح"
            
        except Exception as e:
            return False, f"خطأ: {str(e)}"
    
    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات"""
        conn = self.get_db_connection()
        stats = {}
        
        try:
            stats['active_leave_of_absence'] = conn.execute(
                'SELECT COUNT(*) FROM employee_leave_of_absence_updated WHERE status = "active"'
            ).fetchone()[0]
        except:
            stats['active_leave_of_absence'] = 0
        
        try:
            stats['pending_resignations'] = conn.execute(
                'SELECT COUNT(*) FROM employee_resignations WHERE status = "pending"'
            ).fetchone()[0]
        except:
            stats['pending_resignations'] = 0
        
        # إضافة إحصائيات افتراضية
        stats.update({
            'active_suspensions': 0,
            'total_deaths': 0,
            'total_external_transfers': 0,
            'total_dismissals': 0,
            'total_retirements': 0,
            'resignations': 0,
            'deaths': 0,
            'retirements': 0,
            'external_transfers': 0,
            'leave_of_absence': 0,
            'suspensions': 0
        })
        
        conn.close()
        return stats
