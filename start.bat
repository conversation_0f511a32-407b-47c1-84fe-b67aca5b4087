@echo off
chcp 65001 > nul
title نظام إدارة موظفي الجمارك الجزائرية

echo.
echo ========================================
echo   نظام إدارة موظفي الجمارك الجزائرية
echo   Algerian Customs Employee Management
echo ========================================
echo.

echo 🔄 التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 🔄 تثبيت المتطلبات...
pip install -r requirements.txt > nul 2>&1

echo.
echo 🔄 التحقق من قاعدة البيانات...
if not exist "customs_employees.db" (
    echo 🔧 إنشاء قاعدة البيانات...
    python init_database.py
)

echo.
echo 🚀 تشغيل النظام...
echo.
echo 🌐 الوصول للنظام: http://localhost:5000
echo 👋 اضغط Ctrl+C لإيقاف الخادم
echo.

python app.py

pause