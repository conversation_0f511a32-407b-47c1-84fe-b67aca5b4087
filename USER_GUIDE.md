# 📖 دليل المستخدم - نظام إدارة حالات الموظفين

## 🎯 مرحباً بك في النظام الكامل لإدارة حالات الموظفين!

تم إنشاء هذا النظام خصيصاً لإدارة حالات الموظفين في الجمارك الجزائرية مع التمييز الدقيق بين الحالات المؤقتة والنهائية.

---

## 🚀 **كيفية تشغيل النظام:**

### 1️⃣ تشغيل الخادم:
```bash
python app.py
```

### 2️⃣ فتح النظام:
افتح المتصفح وانتقل إلى: **http://localhost:5000/**

---

## 🌟 **الميزات الرئيسية:**

### ✅ **الحالات النهائية** (تُحذف من جدول الموظفين ولا تُحسب في العدد):
- **الوفاة** 🕊️
- **التحويل الخارجي** 🔄
- **العزل** ❌
- **التقاعد** 🏆

### ✅ **الحالات المؤقتة** (تبقى في جدول الموظفين وتُحسب في العدد):
- **الاستيداع** 📋 (مع حساب الرصيد - 5 سنوات كحد أقصى)
- **التوقيف** ⏸️
- **الاستقالة** 📝
- **الخدمة الوطنية** 🇩🇿
- **العطلة الطويلة الأمد** 🏖️
- **الانتداب** 🎯
- **الدراسة/التكوين** 📚

---

## 📋 **كيفية استخدام النظام:**

### 🏠 **الصفحة الرئيسية:**
- عرض الإحصائيات العامة
- الوصول السريع للوظائف المهمة
- التنبيهات والإشعارات

### 👥 **قائمة الموظفين:**
1. انتقل إلى **"الموظفين"** من القائمة الجانبية
2. ستجد جدول بجميع الموظفين
3. لكل موظف، ستجد زر **"الحالات"** 
4. اضغط على الزر لعرض قائمة الحالات المتاحة

### 🔄 **إدارة الحالات:**

#### **للحالات المؤقتة:**
- اختر الحالة المطلوبة من قائمة "الحالات المؤقتة"
- املأ النموذج المطلوب
- اضغط "حفظ" - سيتم تحديث حالة الموظف فقط

#### **للحالات النهائية:**
- اختر الحالة من قائمة "الحالات النهائية"
- **تحذير**: ستظهر رسالة تأكيد
- املأ النموذج بعناية
- اضغط "تأكيد" - سيتم نقل الموظف لجدول منفصل

### 📊 **لوحة الحالات:**
- انتقل إلى **"حالات الموظفين"** من القائمة الجانبية
- عرض إحصائيات شاملة لجميع الحالات
- روابط سريعة لقوائم الحالات النهائية

---

## 🎯 **الروابط المهمة:**

| الصفحة | الرابط | الوصف |
|--------|---------|-------|
| الصفحة الرئيسية | http://localhost:5000/ | لوحة التحكم الرئيسية |
| قائمة الموظفين | http://localhost:5000/employees | إدارة الموظفين والحالات |
| لوحة الحالات | http://localhost:5000/status/ | إحصائيات الحالات |
| المتوفين | http://localhost:5000/status/deceased | قائمة الموظفين المتوفين |
| المحولين خارجياً | http://localhost:5000/final_status/external_transfers | قائمة المحولين خارجياً |
| المعزولين | http://localhost:5000/final_status/dismissed | قائمة الموظفين المعزولين |
| المتقاعدين | http://localhost:5000/final_status/retired | قائمة الموظفين المتقاعدين |

---

## ⚠️ **تحذيرات مهمة:**

### 🔴 **للحالات النهائية:**
- **لا يمكن التراجع بسهولة** عن هذه العمليات
- الموظف **سيُحذف** من جدول الموظفين النشطين
- الموظف **لن يُحسب** في إجمالي عدد الموظفين
- جميع البيانات **محفوظة** في جدول الحالة الخاص

### 🟡 **للحالات المؤقتة:**
- الموظف **يبقى** في جدول الموظفين
- الموظف **يُحسب** في إجمالي عدد الموظفين
- يمكن **تغيير الحالة** لاحقاً

### 📋 **رصيد الاستيداع:**
- الحد الأقصى: **60 شهر** (5 سنوات) في الحياة الوظيفية
- يتم **حساب الرصيد تلقائياً**
- **لا يمكن** تجاوز الحد الأقصى

---

## 📊 **فهم الإحصائيات:**

### 🟢 **الموظفين النشطين** (يُحسبون في العدد):
```
النشطين + المستودعين + الموقوفين + المستقيلين + ... = إجمالي النشطين
```

### 🔴 **الموظفين المحذوفين** (لا يُحسبون في العدد):
```
المتوفين + المحولين خارجياً + المعزولين + المتقاعدين = إجمالي المحذوفين
```

### 📈 **الإجمالي العام:**
```
إجمالي النشطين + إجمالي المحذوفين = الإجمالي العام
```

---

## 🔍 **الوصول لمعلومات الموظفين المحذوفين:**

حتى بعد حذف الموظف من جدول الموظفين النشطين، يمكنك الوصول لجميع معلوماته:

1. انتقل لقائمة الحالة المناسبة (مثلاً: قائمة المتقاعدين)
2. اضغط على زر **"عرض البيانات"** بجانب اسم الموظف
3. ستظهر جميع المعلومات الأصلية للموظف

---

## 🛠️ **استكشاف الأخطاء:**

### ❌ **إذا لم تظهر أزرار الحالات:**
1. تأكد من تشغيل الخادم بشكل صحيح
2. أعد تحميل الصفحة (F5)
3. تأكد من وجود موظفين في قاعدة البيانات

### ❌ **إذا ظهرت رسالة خطأ 500:**
1. تحقق من سجلات الخادم في terminal
2. تأكد من وجود جميع الملفات المطلوبة
3. أعد تشغيل الخادم

### ❌ **إذا لم تعمل الإحصائيات:**
1. تحقق من اتصال قاعدة البيانات
2. تأكد من وجود الجداول المطلوبة
3. شغل ملف الاختبار: `python simple_test.py`

---

## 📞 **الدعم والمساعدة:**

### 📋 **للمراجعة التقنية:**
- **COMPLETE_SYSTEM_SUMMARY.md** - ملخص شامل للنظام
- **FINAL_SYSTEM_REPORT.md** - تقرير تقني مفصل

### 🧪 **للاختبار:**
```bash
python simple_test.py
```

### 📊 **لعرض الإحصائيات:**
```bash
python final_system_test.py
```

---

## 🎉 **تهانينا!**

لديك الآن نظام كامل ومتطور لإدارة حالات الموظفين يحقق جميع المتطلبات:

✅ **الحالات النهائية تُحذف ولا تُحسب**  
✅ **الحالات المؤقتة تبقى وتُحسب**  
✅ **حفظ جميع البيانات**  
✅ **إمكانية الوصول للمعلومات**  
✅ **واجهات سهلة الاستخدام**  

**النظام جاهز للاستخدام الفوري! 🚀**