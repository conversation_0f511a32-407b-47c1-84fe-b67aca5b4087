#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صفحة المعاينة المحدثة مع جميع الحقول
"""

from app import app

def test_complete_employee_view():
    """اختبار صفحة المعاينة الكاملة"""
    print("🔍 اختبار صفحة المعاينة المحدثة...")
    print("=" * 50)
    
    with app.test_client() as client:
        # اختبار صفحة عرض التفاصيل
        response = client.get('/employee/1')
        
        if response.status_code == 200 or response.status_code == 302:
            print("✅ صفحة المعاينة تعمل بشكل صحيح")
        else:
            print(f"❌ خطأ في صفحة المعاينة: {response.status_code}")
            return
    
    print("\n📋 الحقول المتاحة الآن في صفحة المعاينة:")
    print("\n🖼️ **قسم الصورة والمعلومات السريعة:**")
    print("   • صورة الموظف")
    print("   • الاسم الكامل (عربي وفرنسي)")
    print("   • رقم التسجيل")
    print("   • الحالة الوظيفية")
    
    print("\n📞 **قسم بيانات الاتصال:**")
    print("   • رقم الهاتف 1")
    print("   • رقم الهاتف 2")
    print("   • البريد الإلكتروني")
    print("   • العنوان")
    print("   • رسالة إذا لم توجد بيانات اتصال")
    
    print("\n👤 **قسم البيانات الشخصية:**")
    print("   • اللقب (عربي وفرنسي)")
    print("   • الاسم (عربي وفرنسي)")
    print("   • تاريخ الميلاد + العمر المحسوب")
    print("   • مكان الميلاد (الولاية والبلدية)")
    print("   • الجنس (مع أيقونات)")
    print("   • رقم الضمان الاجتماعي")
    print("   • الحساب الجاري البريدي")
    print("   • الحالة العائلية (مع أيقونات)")
    print("   • عدد الأبناء")
    print("   • عدد المتكفل بهم")
    print("   • زمرة الدم")
    print("   • الرياضة الممارسة")
    
    print("\n💼 **قسم البيانات المهنية:**")
    print("   • تاريخ التوظيف + سنوات الخدمة المحسوبة")
    print("   • الحالة الوظيفية (مع ألوان)")
    print("   • الرتبة الحالية")
    print("   • السلك")
    print("   • مصلحة التعيين")
    
    print("\nℹ️ **قسم معلومات النظام:**")
    print("   • تاريخ الإنشاء")
    print("   • آخر تحديث")
    
    print("\n" + "=" * 50)
    print("🎨 **الميزات البصرية الجديدة:**")
    print("   ✅ أيقونات مميزة لكل نوع بيانات")
    print("   ✅ ألوان مختلفة للحالات المختلفة")
    print("   ✅ badges ملونة للحالة العائلية والجنس")
    print("   ✅ حساب تلقائي للعمر وسنوات الخدمة")
    print("   ✅ إخفاء الحقول الفارغة تلقائياً")
    print("   ✅ رسائل توضيحية عند عدم وجود بيانات")
    
    print("\n🚀 **النتيجة:**")
    print("   ✅ صفحة المعاينة تعرض الآن جميع الحقول المتاحة")
    print("   ✅ التصميم منظم ومرتب بصرياً")
    print("   ✅ جميع البيانات معروضة بشكل واضح ومفهوم")
    
    print(f"\n📱 للاختبار: اذهب إلى http://localhost:5000/employees")
    print("   ثم انقر على زر العين 👁️ لأي موظف")

if __name__ == "__main__":
    test_complete_employee_view()