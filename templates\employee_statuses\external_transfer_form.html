{% extends "base.html" %}

{% block title %}التحويل الخارجي - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}التحويل الخارجي{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-exchange-alt me-2"></i>التحويل الخارجي للموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المديرية الحالية:</strong> {{ employee.current_directorate or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-success">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج التحويل الخارجي -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>بيانات التحويل الخارجي
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" id="transferForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="end_date" class="form-label">تاريخ إنهاء المهام <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="end_date" name="end_date" required>
                        <div class="form-text">آخر يوم عمل في المديرية الحالية</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="target_directorate" class="form-label">المديرية المحول إليها <span class="text-danger">*</span></label>
                        <select class="form-select" id="target_directorate" name="target_directorate" required>
                            <option value="">اختر المديرية</option>
                            <option value="المديرية الجهوية للجمارك - الجزائر">المديرية الجهوية للجمارك - الجزائر</option>
                            <option value="المديرية الجهوية للجمارك - وهران">المديرية الجهوية للجمارك - وهران</option>
                            <option value="المديرية الجهوية للجمارك - قسنطينة">المديرية الجهوية للجمارك - قسنطينة</option>
                            <option value="المديرية الجهوية للجمارك - ورقلة">المديرية الجهوية للجمارك - ورقلة</option>
                            <option value="المديرية الجهوية للجمارك - بشار">المديرية الجهوية للجمارك - بشار</option>
                            <option value="المديرية الجهوية للجمارك - تمنراست">المديرية الجهوية للجمارك - تمنراست</option>
                            <option value="الإدارة المركزية للجمارك">الإدارة المركزية للجمارك</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- حقل المديرية المخصصة -->
            <div class="row" id="customDirectorateRow" style="display: none;">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="custom_directorate" class="form-label">اسم المديرية</label>
                        <input type="text" class="form-control" id="custom_directorate" name="custom_directorate" 
                               placeholder="أدخل اسم المديرية المحول إليها">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_number" class="form-label">رقم المقرر</label>
                        <input type="text" class="form-control" id="decision_number" name="decision_number" 
                               placeholder="أدخل رقم مقرر التحويل">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="decision_date" class="form-label">تاريخ المقرر</label>
                        <input type="date" class="form-control" id="decision_date" name="decision_date">
                    </div>
                </div>
            </div>
            
            <!-- معلومات مهمة -->
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه مهم:</strong> التحويل الخارجي يعني انتقال الموظف نهائياً إلى مديرية أخرى مع الاحتفاظ بجميع حقوقه الوظيفية.
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> سيتم تحديث سجل الموظف ليعكس المديرية الجديدة وتاريخ التحويل.
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('employee_status_change', employee_id=employee.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
                <button type="submit" class="btn btn-info" id="submitBtn">
                    <i class="fas fa-save me-2"></i>تسجيل التحويل
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal تأكيد التحويل -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>تأكيد التحويل الخارجي
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من تحويل الموظف:</p>
                <div class="text-center p-3 border rounded bg-light">
                    <h6><strong>{{ employee.first_name }} {{ employee.last_name }}</strong></h6>
                    <p class="mb-0">رقم التسجيل: {{ employee.registration_number }}</p>
                </div>
                <div class="mt-3">
                    <p><strong>تاريخ إنهاء المهام:</strong> <span id="confirmEndDate"></span></p>
                    <p><strong>المديرية الجديدة:</strong> <span id="confirmDirectorate"></span></p>
                </div>
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    هذا الإجراء سيؤدي إلى تغيير حالة الموظف إلى "تحويل" وتحديث بياناته الإدارية.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>تأكيد التحويل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إظهار/إخفاء حقل المديرية المخصصة
document.getElementById('target_directorate').addEventListener('change', function() {
    const customDirectorateRow = document.getElementById('customDirectorateRow');
    if (this.value === 'أخرى') {
        customDirectorateRow.style.display = 'block';
        document.getElementById('custom_directorate').required = true;
    } else {
        customDirectorateRow.style.display = 'none';
        document.getElementById('custom_directorate').required = false;
    }
});

document.getElementById('transferForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const endDate = document.getElementById('end_date').value;
    const targetDirectorate = document.getElementById('target_directorate').value;
    
    if (!endDate || !targetDirectorate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من المديرية المخصصة إذا كانت مطلوبة
    if (targetDirectorate === 'أخرى') {
        const customDirectorate = document.getElementById('custom_directorate').value.trim();
        if (!customDirectorate) {
            alert('يرجى إدخال اسم المديرية المحول إليها');
            return;
        }
    }
    
    // التحقق من أن تاريخ إنهاء المهام ليس في الماضي البعيد
    const today = new Date();
    const selectedDate = new Date(endDate);
    const diffTime = Math.abs(today - selectedDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (selectedDate < today && diffDays > 30) {
        if (!confirm('تاريخ إنهاء المهام يبدو قديماً. هل أنت متأكد من المتابعة؟')) {
            return;
        }
    }
    
    // تحديث بيانات التأكيد
    document.getElementById('confirmEndDate').textContent = endDate;
    document.getElementById('confirmDirectorate').textContent = targetDirectorate === 'أخرى' ? 
        document.getElementById('custom_directorate').value : targetDirectorate;
    
    // إظهار مودال التأكيد
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
});

document.getElementById('confirmSubmit').addEventListener('click', function() {
    // إخفاء المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    modal.hide();
    
    // تعطيل الزر وإظهار مؤشر التحميل
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التسجيل...';
    
    // تحديث قيمة المديرية إذا كانت مخصصة
    const directorateSelect = document.getElementById('target_directorate');
    if (directorateSelect.value === 'أخرى') {
        directorateSelect.value = document.getElementById('custom_directorate').value;
    }
    
    // إرسال النموذج
    document.getElementById('transferForm').submit();
});

// تحديد تاريخ اليوم كحد أدنى لتاريخ إنهاء المهام
const today = new Date();
const thirtyDaysAgo = new Date(today);
thirtyDaysAgo.setDate(today.getDate() - 30);

document.getElementById('end_date').min = thirtyDaysAgo.toISOString().split('T')[0];
document.getElementById('decision_date').max = today.toISOString().split('T')[0];
</script>
{% endblock %}