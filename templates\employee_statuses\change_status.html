{% extends "base.html" %}

{% block title %}تغيير حالة الموظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block page_title %}تغيير حالة الموظف{% endblock %}

{% block content %}
<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-user me-2"></i>معلومات الموظف
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                {% if employee.photo %}
                <img src="{{ employee.photo }}" alt="صورة الموظف" class="img-fluid rounded border" style="max-height: 200px;">
                {% else %}
                <div class="bg-light rounded border d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                        <p><strong>الرتبة:</strong> {{ employee.rank_name or 'غير محدد' }}</p>
                        <p><strong>السلك:</strong> {{ employee.corps_name or 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المصلحة:</strong> {{ employee.service_name or 'غير محدد' }}</p>
                        <p><strong>الحالة الحالية:</strong> 
                            <span class="badge bg-info">{{ employee.status or 'غير محدد' }}</span>
                        </p>
                        <p><strong>تاريخ التوظيف:</strong> {{ employee.hiring_date or 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- اختيار الحالة الجديدة -->
<div class="card">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
            <i class="fas fa-exchange-alt me-2"></i>اختيار الحالة الجديدة
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for status in statuses %}
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 border-{{ status.color_class }} status-card" 
                     onclick="selectStatus('{{ status.name }}', '{{ status.color_class }}', '{{ status.icon }}')">
                    <div class="card-body text-center">
                        <i class="fas fa-{{ status.icon }} fa-2x text-{{ status.color_class }} mb-3"></i>
                        <h6 class="card-title">{{ status.name }}</h6>
                        {% if status.description %}
                        <p class="card-text small text-muted">{{ status.description }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Modal تأكيد تغيير الحالة -->
<div class="modal fade" id="statusChangeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>تأكيد تغيير الحالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من تغيير حالة الموظف <strong>{{ employee.first_name }} {{ employee.last_name }}</strong> إلى:</p>
                <div class="text-center p-3 border rounded">
                    <i id="selectedStatusIcon" class="fas fa-info-circle fa-2x mb-2"></i>
                    <h5 id="selectedStatusName">الحالة المختارة</h5>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم توجيهك إلى نموذج تفصيلي لإدخال المعلومات المطلوبة لهذه الحالة.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmStatusChange">متابعة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedStatus = null;

function selectStatus(statusName, colorClass, icon) {
    selectedStatus = statusName;
    
    // تحديث المعلومات في المودال
    document.getElementById('selectedStatusIcon').className = `fas fa-${icon} fa-2x text-${colorClass} mb-2`;
    document.getElementById('selectedStatusName').textContent = statusName;
    
    // إظهار المودال
    const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
    modal.show();
}

document.getElementById('confirmStatusChange').addEventListener('click', function() {
    if (selectedStatus) {
        let url = '';
        
        // تحديد الرابط حسب نوع الحالة
        switch(selectedStatus) {
            case 'متوفي':
                url = `/employee_status_change/{{ employee.id }}/death`;
                break;
            case 'استيداع':
                url = `/employee_status_change/{{ employee.id }}/leave_of_absence`;
                break;
            case 'موقف':
                url = `/employee_status_change/{{ employee.id }}/suspension`;
                break;
            case 'منتدب':
                url = `/employee_status_change/{{ employee.id }}/assignment`;
                break;
            case 'مستقيل':
                url = `/employee_status_change/{{ employee.id }}/resignation`;
                break;
            case 'تحويل':
                url = `/employee_status_change/{{ employee.id }}/external_transfer`;
                break;
            case 'معلق':
                url = `/employee_status_change/{{ employee.id }}/long_term_leave`;
                break;
            case 'متقاعد':
                url = `/employee_status_change/{{ employee.id }}/retirement`;
                break;
            default:
                // للحالات البسيطة، تحديث مباشر
                updateEmployeeStatus(selectedStatus);
                return;
        }
        
        // الانتقال إلى النموذج المناسب
        window.location.href = url;
    }
});

function updateEmployeeStatus(status) {
    fetch(`/api/employee/{{ employee.id }}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({status: status})
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            window.location.href = `/employees/view/{{ employee.id }}`;
        } else {
            alert('خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في العملية');
    });
}

// تأثيرات بصرية للبطاقات
document.querySelectorAll('.status-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        this.style.cursor = 'pointer';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '';
    });
});
</script>

<style>
.status-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}