{% extends "base.html" %}

{% block title %}إضافة انتداب - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase"></i>
                        تسجيل انتداب
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user"></i> معلومات الموظف</h5>
                        <strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}<br>
                        <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                        <strong>الحالة الحالية:</strong> <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> الانتداب يعني تكليف الموظف بمهمة في مكان آخر مع الاحتفاظ بمنصبه الأصلي. يمكن إنهاء الانتداب لاحقاً.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="assignment_type">نوع الانتداب <span class="text-danger">*</span></label>
                                    <select class="form-control" id="assignment_type" name="assignment_type" required>
                                        <option value="">اختر نوع الانتداب</option>
                                        <option value="انتداب داخلي">انتداب داخلي (داخل المؤسسة)</option>
                                        <option value="انتداب خارجي">انتداب خارجي (خارج المؤسسة)</option>
                                        <option value="انتداب تدريبي">انتداب تدريبي</option>
                                        <option value="انتداب تقني">انتداب تقني</option>
                                        <option value="انتداب إداري">انتداب إداري</option>
                                        <option value="انتداب استشاري">انتداب استشاري</option>
                                        <option value="انتداب مؤقت">انتداب مؤقت</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار نوع الانتداب
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="assignment_date">تاريخ بداية الانتداب <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="assignment_date" name="assignment_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ بداية الانتداب
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="assignment_location">مكان الانتداب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="assignment_location" name="assignment_location" 
                                           required placeholder="مثال: مديرية الجمارك - ولاية الجزائر">
                                    <div class="invalid-feedback">
                                        يرجى إدخال مكان الانتداب
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="expected_duration_months">المدة المتوقعة (بالأشهر) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="expected_duration_months" name="expected_duration_months" 
                                           min="1" max="36" value="6" required>
                                    <small class="form-text text-muted">عادة من شهر إلى 3 سنوات</small>
                                    <div class="invalid-feedback">
                                        يرجى إدخال المدة المتوقعة
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">رقم القرار</label>
                                    <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                           placeholder="مثال: 2024/ASG/001">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">تاريخ القرار</label>
                                    <input type="date" class="form-control" id="decision_date" name="decision_date">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="assignment_purpose">الغرض من الانتداب</label>
                            <select class="form-control" id="purpose_select" onchange="handlePurposeChange()">
                                <option value="">اختر الغرض من الانتداب</option>
                                <option value="تعزيز الخبرات">تعزيز الخبرات</option>
                                <option value="تدريب متخصص">تدريب متخصص</option>
                                <option value="مشروع خاص">مشروع خاص</option>
                                <option value="نقل المعرفة">نقل المعرفة</option>
                                <option value="دعم تقني">دعم تقني</option>
                                <option value="مهمة إدارية">مهمة إدارية</option>
                                <option value="ضرورة الخدمة">ضرورة الخدمة</option>
                                <option value="أخرى">أخرى (حدد)</option>
                            </select>
                            <textarea class="form-control mt-2" id="assignment_purpose" name="assignment_purpose" rows="2" 
                                      placeholder="اذكر الغرض من الانتداب والمهام المطلوبة"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                      placeholder="أي ملاحظات إضافية حول الانتداب (اختياري)"></textarea>
                        </div>

                        <!-- معلومات محسوبة -->
                        <div class="card bg-light mb-3" id="duration_info" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">معلومات المدة</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>تاريخ الانتهاء المتوقع:</strong>
                                        <span id="expected_end_date">-</span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>المدة الإجمالية:</strong>
                                        <span id="total_duration">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شروط الانتداب -->
                        <div class="card border-success mb-3">
                            <div class="card-header bg-success text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-info-circle"></i>
                                    شروط الانتداب
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>الموظف يحتفظ بمنصبه الأصلي أثناء الانتداب</li>
                                    <li>يتقاضى راتبه كاملاً مع بدلات الانتداب إن وجدت</li>
                                    <li>يمكن إنهاء الانتداب قبل انتهاء المدة المحددة</li>
                                    <li>فترة الانتداب تحسب في سنوات الخدمة</li>
                                    <li>يجب على الموظف العودة لمنصبه الأصلي عند انتهاء الانتداب</li>
                                </ul>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_conditions" required>
                                <label class="custom-control-label" for="confirm_conditions">
                                    أؤكد فهم شروط الانتداب المذكورة أعلاه
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد فهم شروط الانتداب
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_data" required>
                                <label class="custom-control-label" for="confirm_data">
                                    أؤكد صحة البيانات المدخلة
                                </label>
                                <div class="invalid-feedback">
                                    يجب تأكيد صحة البيانات
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-briefcase"></i> تسجيل الانتداب
                            </button>
                            <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// معالجة تغيير الغرض من الانتداب
function handlePurposeChange() {
    var select = document.getElementById('purpose_select');
    var textarea = document.getElementById('assignment_purpose');
    
    if (select.value && select.value !== 'أخرى') {
        textarea.value = select.value;
    } else if (select.value === 'أخرى') {
        textarea.value = '';
        textarea.focus();
    }
}

// حساب تاريخ الانتهاء المتوقع
function calculateEndDate() {
    const assignmentDate = document.getElementById('assignment_date').value;
    const duration = document.getElementById('expected_duration_months').value;
    
    if (assignmentDate && duration) {
        const start = new Date(assignmentDate);
        const end = new Date(start);
        end.setMonth(end.getMonth() + parseInt(duration));
        
        document.getElementById('expected_end_date').textContent = end.toLocaleDateString('ar-DZ');
        
        // حساب المدة بالسنوات والأشهر
        const years = Math.floor(duration / 12);
        const months = duration % 12;
        let durationText = '';
        
        if (years > 0) {
            durationText += years + ' سنة';
            if (months > 0) {
                durationText += ' و ' + months + ' شهر';
            }
        } else {
            durationText = months + ' شهر';
        }
        
        document.getElementById('total_duration').textContent = durationText;
        document.getElementById('duration_info').style.display = 'block';
    } else {
        document.getElementById('duration_info').style.display = 'none';
    }
}

// ربط الأحداث
document.getElementById('assignment_date').addEventListener('change', calculateEndDate);
document.getElementById('expected_duration_months').addEventListener('input', calculateEndDate);

// التحقق من المدة
document.getElementById('expected_duration_months').addEventListener('change', function() {
    const duration = parseInt(this.value);
    
    if (duration > 36) {
        alert('مدة الانتداب تبدو طويلة جداً (أكثر من 3 سنوات). تأكد من صحة الرقم.');
    } else if (duration < 1) {
        alert('مدة الانتداب يجب أن تكون شهر واحد على الأقل.');
        this.value = 1;
    }
});

// اقتراح مكان الانتداب حسب النوع
document.getElementById('assignment_type').addEventListener('change', function() {
    const assignmentType = this.value;
    const locationInput = document.getElementById('assignment_location');
    
    if (assignmentType && !locationInput.value) {
        let suggestedLocation = '';
        
        switch(assignmentType) {
            case 'انتداب داخلي':
                suggestedLocation = 'مديرية فرعية - نفس المؤسسة';
                break;
            case 'انتداب خارجي':
                suggestedLocation = 'مؤسسة حكومية أخرى';
                break;
            case 'انتداب تدريبي':
                suggestedLocation = 'معهد التكوين أو الجامعة';
                break;
            case 'انتداب تقني':
                suggestedLocation = 'مشروع تقني متخصص';
                break;
            case 'انتداب إداري':
                suggestedLocation = 'إدارة مركزية أو ولائية';
                break;
        }
        
        if (suggestedLocation) {
            locationInput.placeholder = 'مثال: ' + suggestedLocation;
        }
    }
});

// التحقق من التواريخ
document.getElementById('decision_date').addEventListener('change', function() {
    const decisionDate = new Date(this.value);
    const assignmentDate = document.getElementById('assignment_date').value;
    
    if (assignmentDate && decisionDate > new Date(assignmentDate)) {
        alert('تاريخ القرار لا يمكن أن يكون بعد تاريخ بداية الانتداب');
        this.value = '';
    }
});

// تعيين تاريخ اليوم كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('assignment_date').value = today;
    document.getElementById('decision_date').value = today;
    
    // حساب تاريخ الانتهاء مع المدة الافتراضية
    calculateEndDate();
});
</script>
{% endblock %}