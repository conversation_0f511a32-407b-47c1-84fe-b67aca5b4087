{% extends "base.html" %}

{% block page_title %}تسجيل وفاة موظف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-heart me-2"></i>
                            تسجيل وفاة موظف
                        </h3>
                        <a href="{{ url_for('special_status.deaths') }}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" id="deathForm">
                        <div class="row">
                            <!-- اختيار الموظف -->
                            <div class="col-md-6 mb-3">
                                <label for="employee_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                                <select class="form-select" id="employee_id" name="employee_id" required onchange="loadEmployeeInfo()">
                                    <option value="">اختر الموظف</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">
                                        {{ employee.registration_number }} - {{ employee.first_name }} {{ employee.last_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- تاريخ الوفاة -->
                            <div class="col-md-6 mb-3">
                                <label for="death_date" class="form-label">تاريخ الوفاة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="death_date" name="death_date" required>
                            </div>
                        </div>

                        <!-- معلومات الموظف -->
                        <div id="employeeInfo" class="row mb-3" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6>معلومات الموظف:</h6>
                                    <div id="employeeDetails"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- مكان الوفاة -->
                            <div class="col-md-6 mb-3">
                                <label for="death_place" class="form-label">مكان الوفاة</label>
                                <input type="text" class="form-control" id="death_place" name="death_place" 
                                       placeholder="مكان الوفاة">
                            </div>

                            <!-- سبب الوفاة -->
                            <div class="col-md-6 mb-3">
                                <label for="death_cause" class="form-label">سبب الوفاة</label>
                                <input type="text" class="form-control" id="death_cause" name="death_cause" 
                                       placeholder="سبب الوفاة">
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم شهادة الوفاة -->
                            <div class="col-md-6 mb-3">
                                <label for="certificate_number" class="form-label">رقم شهادة الوفاة</label>
                                <input type="text" class="form-control" id="certificate_number" name="certificate_number" 
                                       placeholder="رقم شهادة الوفاة">
                            </div>

                            <!-- مكان الدفن -->
                            <div class="col-md-6 mb-3">
                                <label for="burial_place" class="form-label">مكان الدفن</label>
                                <input type="text" class="form-control" id="burial_place" name="burial_place" 
                                       placeholder="مكان الدفن">
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات الاتصال بالعائلة -->
                            <div class="col-12 mb-3">
                                <label for="family_contact" class="form-label">معلومات الاتصال بالعائلة</label>
                                <textarea class="form-control" id="family_contact" name="family_contact" rows="2" 
                                         placeholder="رقم الهاتف، العنوان، أو أي معلومات اتصال أخرى..."></textarea>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم القرار -->
                            <div class="col-md-6 mb-3">
                                <label for="decision_number" class="form-label">رقم القرار الإداري</label>
                                <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                       placeholder="رقم القرار الإداري">
                            </div>

                            <!-- تاريخ القرار -->
                            <div class="col-md-6 mb-3">
                                <label for="decision_date" class="form-label">تاريخ القرار</label>
                                <input type="date" class="form-control" id="decision_date" name="decision_date">
                            </div>
                        </div>

                        <div class="row">
                            <!-- ملاحظات -->
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات إضافية</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                         placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-dark">
                                        <i class="fas fa-save me-2"></i>تسجيل الوفاة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadEmployeeInfo() {
    const employeeId = document.getElementById('employee_id').value;
    
    if (!employeeId) {
        document.getElementById('employeeInfo').style.display = 'none';
        return;
    }
    
    fetch(`/special_status/api/employee/${employeeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('خطأ في تحميل بيانات الموظف: ' + data.error);
                return;
            }
            
            const details = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>رقم التسجيل:</strong> ${data.registration_number}<br>
                        <strong>الاسم الكامل:</strong> ${data.first_name} ${data.last_name}
                    </div>
                    <div class="col-md-6">
                        <strong>الرتبة:</strong> ${data.rank_name || 'غير محدد'}<br>
                        <strong>المصلحة:</strong> ${data.service_name || 'غير محدد'}
                    </div>
                </div>
            `;
            
            document.getElementById('employeeDetails').innerHTML = details;
            document.getElementById('employeeInfo').style.display = 'block';
        })
        .catch(error => {
            alert('خطأ في تحميل البيانات: ' + error);
        });
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
        document.getElementById('deathForm').reset();
        document.getElementById('employeeInfo').style.display = 'none';
    }
}

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('deathForm').addEventListener('submit', function(e) {
    const employeeId = document.getElementById('employee_id').value;
    const deathDate = document.getElementById('death_date').value;
    
    if (!employeeId) {
        e.preventDefault();
        alert('يرجى اختيار الموظف');
        return;
    }
    
    if (!deathDate) {
        e.preventDefault();
        alert('يرجى تحديد تاريخ الوفاة');
        return;
    }
    
    // التحقق من أن تاريخ الوفاة ليس في المستقبل
    const selectedDate = new Date(deathDate);
    const today = new Date();
    
    if (selectedDate > today) {
        e.preventDefault();
        alert('تاريخ الوفاة لا يمكن أن يكون في المستقبل');
        return;
    }
    
    // تأكيد العملية
    if (!confirm('هل أنت متأكد من تسجيل هذه الوفاة؟ هذا الإجراء سيؤثر على حالة الموظف.')) {
        e.preventDefault();
        return;
    }
});
</script>
{% endblock %}
