# نظام الحالات الخاصة المحدث - ملخص شامل

## 🎯 نظرة عامة
تم تطوير وتحديث نظام الحالات الخاصة للموظفين ليتوافق مع المواصفات التفصيلية المطلوبة. النظام الآن يدعم جميع أنواع الحالات الخاصة مع الحقول المحددة والقواعد التجارية المطلوبة.

## 📋 الحالات الخاصة المحدثة

### 1. ملف الاستيداع (محدث بالكامل)
- **الفترة**: الأولى، الثانية، الثالثة... (محسوبة تلقائياً)
- **المدة**: بالأشهر مع حد أقصى 5 سنوات (60 شهر) في الحياة الوظيفية
- **السبب**: قائمة قابلة للتعديل في الإعدادات
- **تاريخ بداية الاستيداع**: تاريخ البداية
- **تاريخ نهاية الاستيداع**: محسوب تلقائياً بإضافة المدة إلى تاريخ البداية
- **رقم المقرر/الوثيقة**: رقم القرار
- **تاريخ المقرر/الوثيقة**: تاريخ القرار

### 2. ملف التوقيف
- **تاريخ التوقيف**: تاريخ بداية التوقيف
- **سبب التوقيف**: سبب التوقيف
- **رقم مقرر التوقيف**: رقم القرار
- **تاريخ مقرر التوقيف**: تاريخ القرار

### 3. ملف العزل (جديد)
- **تاريخ العزل**: تاريخ العزل
- **سبب العزل**: سبب العزل
- **رقم مقرر العزل**: رقم القرار
- **تاريخ مقرر العزل**: تاريخ القرار

### 4. ملف الوفيات (محدث)
- **تاريخ الوفاة**: تاريخ الوفاة
- **السبب**: عادية أو حادث عمل
- **رقم شهادة الوفاة**: رقم الشهادة

### 5. ملف الانتداب
- **تاريخ الانتداب**: تاريخ بداية الانتداب
- **مدة الانتداب**: المدة بالأشهر
- **سبب الانتداب**: سبب الانتداب
- **مكان الانتداب**: الجهة المنتدب إليها

### 6. ملف الاستقالة (محدث)
- **تاريخ الطلب**: تاريخ تقديم طلب الاستقالة
- **سبب الاستقالة**: سبب الاستقالة
- **مقبولة**: نعم/لا (نظام الموافقة)
- **رقم المقرر**: رقم قرار الموافقة (إذا كانت مقبولة)
- **تاريخ المقرر**: تاريخ قرار الموافقة

### 7. ملف التحويل الخارجي (محدث)
- **تاريخ إنهاء المهام**: تاريخ انتهاء العمل
- **المديرية المحول لها**: الجهة المستقبلة
- **رقم المقرر**: رقم قرار التحويل
- **تاريخ المقرر**: تاريخ القرار

### 8. ملف عطلة طويلة الأمد (جديد)
- **تاريخ البداية**: تاريخ بداية العطلة
- **تاريخ المراجعة**: تاريخ المراجعة الطبية
- **رقم الوثيقة**: رقم الوثيقة الطبية
- **تاريخ الوثيقة**: تاريخ إصدار الوثيقة
- **الهيئة المانحة**: الجهة التي أصدرت الوثيقة

### 9. ملف التقاعد (محدث)
- **تاريخ التقاعد**: تاريخ التقاعد
- **رقم مقرر التقاعد**: رقم قرار التقاعد
- **تاريخ مقرر التقاعد**: تاريخ القرار
- **رقم بطاقة التقاعد**: رقم البطاقة
- **تاريخ صدورها**: تاريخ إصدار البطاقة

### 10. ملف الخدمة الوطنية (جديد)
- **تاريخ بداية الخدمة**: تاريخ البداية
- **تاريخ نهاية الخدمة**: تاريخ الانتهاء
- **رقم مقرر الخدمة الوطنية**: رقم القرار
- **تاريخ مقرر الخدمة الوطنية**: تاريخ القرار

### 11. ملف الدراسة أو التكوين (جديد)
- **تاريخ بداية الدراسة أو التكوين**: تاريخ البداية
- **نوع التكوين أو الدراسة**: نوع البرنامج
- **رقم مقرر الدراسة أو التكوين**: رقم القرار
- **تاريخ مقرر الدراسة أو التكوين**: تاريخ القرار

## 🗂️ هيكل قاعدة البيانات المحدث

### الجداول الجديدة/المحدثة:
1. `employee_leave_of_absence_updated` - الاستيداع المحدث
2. `employee_suspensions_updated` - التوقيف المحدث
3. `employee_dismissals` - العزل (جديد)
4. `employee_deaths_updated` - الوفيات المحدث
5. `employee_assignments_updated` - الانتداب المحدث
6. `employee_resignations_updated` - الاستقالات المحدث
7. `employee_external_transfers_updated` - التحويل الخارجي المحدث
8. `employee_long_term_leave` - عطلة طويلة الأمد (جديد)
9. `employee_retirements_updated` - التقاعد المحدث
10. `employee_national_service` - الخدمة الوطنية (جديد)
11. `employee_study_training` - الدراسة والتكوين (جديد)
12. `leave_of_absence_reasons` - أسباب الاستيداع (للإعدادات)

## 🔄 نظام نقل الموظفين

### الحالات النهائية (نقل من جدول الموظفين):
- **الوفاة**: نقل إلى `employee_deaths_updated`
- **التحويل الخارجي**: نقل إلى `employee_external_transfers_updated`
- **العزل**: نقل إلى `employee_dismissals`
- **التقاعد**: نقل إلى `employee_retirements_updated`

### الحالات المؤقتة (تبقى في جدول الموظفين):
- **الاستيداع**: تحديث الحالة مع الاحتفاظ بالموظف
- **التوقيف**: تحديث الحالة مع الاحتفاظ بالموظف
- **الاستقالة المعلقة**: تحديث الحالة حتى الموافقة
- **باقي الحالات**: تحديث الحالة مع الاحتفاظ بالموظف

## ✨ الميزات الجديدة

### 1. نظام إدارة أسباب الاستيداع
- قائمة قابلة للتعديل في الإعدادات
- إضافة وتعديل وإلغاء تفعيل الأسباب
- أسباب افتراضية: رعاية الأطفال، الدراسة، ظروف شخصية، إلخ

### 2. تتبع الحد الأقصى للاستيداع
- حد أقصى 5 سنوات (60 شهر) في الحياة الوظيفية
- حساب الرصيد المتبقي تلقائياً
- منع تجاوز الحد الأقصى
- عرض تفصيلي للاستخدام والرصيد

### 3. حساب التواريخ التلقائي
- حساب تاريخ نهاية الاستيداع تلقائياً
- حساب رقم الفترة تلقائياً (الأولى، الثانية، إلخ)
- التحقق من صحة التواريخ

### 4. نظام الموافقة للاستقالات
- حقل الموافقة (نعم/لا)
- رقم وتاريخ مقرر الموافقة
- تتبع حالة الطلب (معلق، مؤكد، مرفوض)

### 5. واجهات محدثة
- صفحات مخصصة لكل نوع من الحالات
- نماذج تفاعلية مع التحقق من البيانات
- عرض معلومات الموظف ورصيد الاستيداع
- تصميم متجاوب ومتقدم

## 🌐 الصفحات والمسارات الجديدة

### صفحات الإعدادات:
- `/special_status/leave_reasons_settings` - إعدادات أسباب الاستيداع

### صفحات الاستيداع المحدثة:
- `/special_status/leave_of_absence` - قائمة الاستيداع
- `/special_status/leave_of_absence/add` - إضافة استيداع جديد

### API الجديدة:
- `/special_status/api/employee/{id}/leave_balance` - رصيد الاستيداع
- `/special_status/api/leave_reason/{id}/deactivate` - إلغاء تفعيل السبب
- `/special_status/api/leave_reason/{id}/activate` - تفعيل السبب

## 📊 الإحصائيات المحدثة

### الحالات النشطة:
- الاستيداع النشط
- التوقيف النشط
- الاستقالات المعلقة

### الحالات النهائية:
- إجمالي الوفيات
- إجمالي التحويلات الخارجية
- إجمالي حالات العزل
- إجمالي المتقاعدين

## 🔧 الملفات التقنية الجديدة

### ملفات Python:
1. `create_updated_special_status_tables.py` - إنشاء الجداول المحدثة
2. `updated_special_status_manager.py` - مدير الحالات المحدث
3. `update_special_status_system.py` - تشغيل التحديث

### ملفات HTML الجديدة:
1. `templates/special_status/leave_reasons_settings.html` - إعدادات الأسباب
2. `templates/special_status/add_leave_of_absence.html` - إضافة استيداع محدث
3. `templates/special_status/index_updated.html` - الصفحة الرئيسية المحدثة

## 🚀 كيفية الوصول للنظام المحدث

### الروابط الرئيسية:
- الصفحة الرئيسية: `http://localhost:5000/special_status/`
- إعدادات أسباب الاستيداع: `http://localhost:5000/special_status/leave_reasons_settings`
- إضافة استيداع: `http://localhost:5000/special_status/leave_of_absence/add`

### من الشريط الجانبي:
- انقر على "الحالات الخاصة" للوصول للنظام المحدث

## ✅ ما تم إنجازه

- ✅ تحديث جميع جداول قاعدة البيانات حسب المواصفات
- ✅ إنشاء نظام إدارة أسباب الاستيداع
- ✅ تطوير نظام حساب المدد التلقائي
- ✅ إضافة نظام الموافقات للاستقالات
- ✅ تطوير نظام نقل الموظفين للحالات النهائية
- ✅ إنشاء صفحات مفصلة لجميع الحالات
- ✅ تحديث المسارات والواجهات
- ✅ إضافة التحقق من البيانات والقواعد التجارية

---

**النظام المحدث جاهز للاستخدام ويتوافق مع جميع المواصفات المطلوبة! 🎉**
