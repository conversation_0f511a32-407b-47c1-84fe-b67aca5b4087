#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص حالة نظام إدارة موظفي الجمارك الجزائرية
System Health Check for Algerian Customs Employee Management System
"""

import os
import sys
import sqlite3
from datetime import datetime

def check_python_version():
    """التحقق من إصدار Python"""
    version = sys.version_info
    print(f"🐍 Python Version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✅ إصدار Python مناسب")
        return True
    else:
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False

def check_required_packages():
    """التحقق من المكتبات المطلوبة"""
    required_packages = {
        'flask': 'Flask',
        'PIL': 'Pillow',
        'werkzeug': 'Werkzeug'
    }
    
    missing_packages = []
    
    for package, name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {name} متوفر")
        except ImportError:
            print(f"❌ {name} مفقود")
            missing_packages.append(name)
    
    return len(missing_packages) == 0, missing_packages

def check_database():
    """التحقق من قاعدة البيانات"""
    db_path = 'customs_employees.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من الجداول الأساسية
        required_tables = [
            'employees', 'wilayas', 'communes', 'ranks', 
            'corps', 'services', 'annual_leaves'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"✅ جدول {table} موجود")
            else:
                print(f"❌ جدول {table} مفقود")
                missing_tables.append(table)
        
        # التحقق من البيانات
        cursor.execute("SELECT COUNT(*) FROM employees")
        employee_count = cursor.fetchone()[0]
        print(f"📊 عدد الموظفين: {employee_count}")
        
        cursor.execute("SELECT COUNT(*) FROM wilayas")
        wilaya_count = cursor.fetchone()[0]
        print(f"📊 عدد الولايات: {wilaya_count}")
        
        conn.close()
        
        return len(missing_tables) == 0
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def check_files_structure():
    """التحقق من هيكل الملفات"""
    required_files = [
        'app.py',
        'init_database.py',
        'requirements.txt',
        'templates/base.html',
        'templates/index.html',
        'templates/employees/list.html',
        'templates/employees/add.html',
        'static/css'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} موجود")
        else:
            print(f"❌ {file_path} مفقود")
            missing_files.append(file_path)
    
    return len(missing_files) == 0, missing_files

def check_status_modules():
    """التحقق من وحدات إدارة الحالات"""
    status_modules = [
        'employee_status_manager.py',
        'employee_status_api.py',
        'employee_status_helpers.py',
        'status_integration.py'
    ]
    
    available_modules = []
    
    for module in status_modules:
        if os.path.exists(module):
            print(f"✅ وحدة الحالات {module} متوفرة")
            available_modules.append(module)
        else:
            print(f"⚠️  وحدة الحالات {module} مفقودة")
    
    return len(available_modules) > 0

def main():
    """الدالة الرئيسية للفحص"""
    print("🔍 فحص حالة نظام إدارة موظفي الجمارك الجزائرية")
    print("=" * 60)
    print(f"📅 تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    all_checks_passed = True
    
    # فحص Python
    print("\n1️⃣ فحص Python:")
    if not check_python_version():
        all_checks_passed = False
    
    # فحص المكتبات
    print("\n2️⃣ فحص المكتبات المطلوبة:")
    packages_ok, missing_packages = check_required_packages()
    if not packages_ok:
        all_checks_passed = False
        print(f"💡 لتثبيت المكتبات المفقودة: pip install {' '.join(missing_packages)}")
    
    # فحص هيكل الملفات
    print("\n3️⃣ فحص هيكل الملفات:")
    files_ok, missing_files = check_files_structure()
    if not files_ok:
        all_checks_passed = False
        print(f"⚠️  ملفات مفقودة: {', '.join(missing_files)}")
    
    # فحص قاعدة البيانات
    print("\n4️⃣ فحص قاعدة البيانات:")
    if not check_database():
        all_checks_passed = False
        print("💡 لإنشاء قاعدة البيانات: python init_database.py")
    
    # فحص وحدات الحالات
    print("\n5️⃣ فحص وحدات إدارة الحالات:")
    status_modules_available = check_status_modules()
    if status_modules_available:
        print("✅ وحدات إدارة الحالات متوفرة")
    else:
        print("⚠️  وحدات إدارة الحالات غير متوفرة (اختيارية)")
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("🎉 جميع الفحوصات نجحت! النظام جاهز للتشغيل")
        print("🚀 لتشغيل النظام: python app.py")
        print("🌐 الوصول للنظام: http://localhost:5000")
    else:
        print("⚠️  بعض الفحوصات فشلت. يرجى إصلاح المشاكل أولاً")
    
    print("=" * 60)

if __name__ == '__main__':
    main()