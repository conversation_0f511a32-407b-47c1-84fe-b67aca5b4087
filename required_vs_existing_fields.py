#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مقارنة الحقول المطلوبة مع الحقول الموجودة حالياً
"""

import sqlite3

def compare_required_vs_existing():
    """مقارنة الحقول المطلوبة مع الموجودة"""
    print("📋 مقارنة الحقول المطلوبة مع الحقول الموجودة حالياً")
    print("=" * 80)
    
    # الحقول المطلوبة حسب المواصفات
    required_fields = {
        # البيانات الأساسية
        'registration_number': 'رقم التسجيل (06 أرقام غير مكرر)',
        'last_name': 'اللقب',
        'first_name': 'الاسم',
        'last_name_fr': 'Nom باللغة الفرنسية',
        'first_name_fr': 'Prenom باللغة الفرنسية',
        'gender': 'الجنس',
        'birth_date': 'تاريخ الميلاد (من 19 سنة الى 65 سنة)',
        'birth_wilaya_id': 'ولاية الميلاد (مرتبط بملف التقسيم الاداري)',
        'birth_commune_id': 'بلدية الميلاد (مرتبط بملف التقسيم الاداري)',
        
        # البيانات الشخصية
        'marital_status': 'الحالة العائلية',
        'children_count': 'عدد الابناء (لا يظهر في حالة إختيار اعزب)',
        'dependents_count': 'عدد الاشخاص المتكفل بهم',
        'blood_type': 'زمرة الدم',
        'photo': 'صور الموظف (تعديل الصور لتكون مناسبة)',
        'sport_practiced': 'الرياضة الممارسة',
        
        # بيانات الاتصال
        'phone1': 'رقم الهاتف1',
        'phone2': 'رقم الهاتف2',
        'email': 'البريد الالكتروني',
        'address': 'العنوان الرئيسي',
        'secondary_address': 'العنوان الثانوي',
        'emergency_contact_name': 'اسم الشخص المتصل به في حالة الضرورة',
        'emergency_contact_address': 'عنوان الشخص المتصل به في حالة الضرورة',
        
        # البيانات المهنية
        'status': 'حالة الموظف (تحويل-موقف-استيداع-منتدب-متوفي-مفصول-مستقيل)',
        'corps_id': 'السلك (اسلاك خاصة – اسلاك مشتركة – عمال مهنيين)',
        'current_rank_id': 'الرتبة الحالية (حسب السلك)',
        'rank_promotion_date': 'تاريخ الترقية في الرتبة الحالية',
        'current_position_id': 'الوظيفة الحالية (قائمة مربوطة بجدول)',
        'position_assignment_date': 'تاريخ التعين في الوظيفة الحالية',
        'directorate_id': 'المديرية (قائمة بجدول التعيين)',
        'current_service_id': 'المصلحة (قائمة بجدول التعيين)',
        'assignment_location_id': 'مكان التعيين (قائمة بجدول التعيين)',
        'hire_date': 'تاريخ التوظيف/الدخول في ادارة الجمارك',
        'initial_rank_id': 'رتبة التوظيف',
        
        # الوثائق والأرقام الرسمية
        'social_security_number': 'رقم الضمان الاجتماعي (جزائري مع دالة التاكد)',
        'postal_account': 'رقم الحساب الجاري البريدي (جزائري مع دالة التاكيد)',
        'professional_card_number': 'رقم بطاقة المهنية',
        'professional_card_issue_date': 'تاريخ صدور بطاقة المهنية',
        'national_id_number': 'رقم بطاقة التعريف الوطنية',
        'national_id_issue_date': 'تاريخ صدور بطاقة التعريف الوطنية',
        'national_id_issue_place_id': 'مكان صدور بطاقة التعريف الوطنية',
        'driving_license_number': 'رقم رخصة السياقة',
        'driving_license_category': 'صنف رخصة السياقة',
        'driving_license_issue_date': 'تاريخ صدور رخصة السياقة',
        'driving_license_issue_place_id': 'مكان صدور رخصة السياقة',
        'mutual_card_number': 'رقم بطاقة التعاضدية',
        'mutual_card_issue_date': 'تاريخ صدور بطاقة التعاضدية'
    }
    
    # الحصول على الحقول الموجودة حالياً
    conn = sqlite3.connect('customs_employees.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(employees)")
    existing_columns = cursor.fetchall()
    conn.close()
    
    existing_fields = [col[1] for col in existing_columns]
    
    print(f"📊 إجمالي الحقول المطلوبة: {len(required_fields)}")
    print(f"📊 إجمالي الحقول الموجودة: {len(existing_fields)}")
    
    # تصنيف الحقول
    existing_required = []
    missing_fields = []
    extra_fields = []
    
    for field_name, description in required_fields.items():
        if field_name in existing_fields:
            existing_required.append((field_name, description))
        else:
            missing_fields.append((field_name, description))
    
    for field_name in existing_fields:
        if field_name not in required_fields:
            extra_fields.append(field_name)
    
    print(f"\n✅ الحقول الموجودة والمطلوبة ({len(existing_required)}):")
    print("-" * 80)
    for field_name, description in existing_required:
        print(f"✅ {field_name:<30} - {description}")
    
    print(f"\n❌ الحقول المفقودة ({len(missing_fields)}):")
    print("-" * 80)
    for field_name, description in missing_fields:
        print(f"❌ {field_name:<30} - {description}")
    
    print(f"\n⚠️  الحقول الإضافية الموجودة ({len(extra_fields)}):")
    print("-" * 80)
    for field_name in extra_fields:
        print(f"⚠️  {field_name}")
    
    # إحصائيات
    completion_percentage = (len(existing_required) / len(required_fields)) * 100
    print(f"\n📈 نسبة الاكتمال: {completion_percentage:.1f}%")
    
    if completion_percentage >= 80:
        print("🎉 ممتاز! معظم الحقول المطلوبة موجودة")
    elif completion_percentage >= 60:
        print("👍 جيد! أغلب الحقول المطلوبة موجودة")
    else:
        print("⚠️  يحتاج عمل كبير - العديد من الحقول مفقودة")
    
    # اقتراحات SQL لإضافة الحقول المفقودة
    if missing_fields:
        print(f"\n🔧 SQL لإضافة الحقول المفقودة:")
        print("-" * 80)
        for field_name, description in missing_fields:
            # تحديد نوع البيانات حسب اسم الحقل
            if 'date' in field_name:
                field_type = 'DATE'
            elif 'id' in field_name:
                field_type = 'INTEGER'
            elif 'count' in field_name:
                field_type = 'INTEGER DEFAULT 0'
            elif 'number' in field_name:
                field_type = 'TEXT'
            else:
                field_type = 'TEXT'
            
            print(f"ALTER TABLE employees ADD COLUMN {field_name} {field_type};")
            print(f"-- {description}")
            print()

if __name__ == "__main__":
    compare_required_vs_existing()