#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الكامل لإدارة موظفي الجمارك الجزائرية
"""

import subprocess
import sys
import os
import time
import webbrowser

def print_header():
    """طباعة رأس النظام"""
    print("\n" + "="*70)
    print("🏛️  نظام إدارة موظفي الجمارك الجزائرية - النظام الكامل")
    print("="*70)
    print("📋 النظام يحتوي على:")
    print("   ✅ إدارة الموظفين الشاملة")
    print("   ✅ العطل والإجازات")
    print("   ✅ الشهادات والتكوين")
    print("   ✅ العقوبات والمكافآت")
    print("   ✅ التنقلات والحركات")
    print("   ✅ مختلف الترقيات")
    print("   ✅ حالات الموظفين")
    print("   ✅ الإعدادات والتقارير")
    print("="*70)

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    
    # التحقق من Flask
    try:
        import flask
        print("✅ Flask متوفر")
    except ImportError:
        print("❌ Flask غير مثبت. جاري التثبيت...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "flask"])
            print("✅ تم تثبيت Flask بنجاح")
        except:
            print("❌ فشل في تثبيت Flask")
            return False
    
    # التحقق من الملفات الأساسية
    required_files = ['main.py', 'templates/base.html', 'templates/index.html']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ الملف المطلوب غير موجود: {file}")
            return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📊 معلومات النظام:")
    print("-" * 50)
    
    # التحقق من قاعدة البيانات
    if os.path.exists('customs_employees.db'):
        print("✅ قاعدة البيانات: موجودة")
        
        # عدد الجداول
        try:
            import sqlite3
            conn = sqlite3.connect('customs_employees.db')
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            print(f"📋 عدد الجداول: {table_count}")
            
            # عدد الموظفين
            try:
                cursor.execute("SELECT COUNT(*) FROM employees")
                employee_count = cursor.fetchone()[0]
                print(f"👥 عدد الموظفين: {employee_count}")
            except:
                print("👥 عدد الموظفين: غير محدد")
            
            conn.close()
        except:
            print("❌ خطأ في قراءة قاعدة البيانات")
    else:
        print("⚠️  قاعدة البيانات: ستُنشأ عند التشغيل")
    
    # التحقق من المجلدات
    folders = ['templates', 'static']
    for folder in folders:
        if os.path.exists(folder):
            print(f"✅ مجلد {folder}: موجود")
        else:
            print(f"⚠️  مجلد {folder}: غير موجود")

def start_server():
    """تشغيل الخادم"""
    print("\n🚀 بدء تشغيل الخادم...")
    print("-" * 50)
    
    try:
        # تشغيل الخادم
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # انتظار قصير للتأكد من بدء الخادم
        time.sleep(3)
        
        # التحقق من حالة العملية
        if process.poll() is None:
            print("✅ تم تشغيل الخادم بنجاح!")
            print("🌐 الخادم يعمل على: http://localhost:5000")
            
            # فتح المتصفح
            print("🔗 فتح المتصفح...")
            time.sleep(2)
            webbrowser.open('http://localhost:5000')
            
            print("\n" + "="*70)
            print("🎯 صفحات النظام المتاحة:")
            print("="*70)
            print("🏠 الصفحة الرئيسية: http://localhost:5000")
            print("👥 إدارة الموظفين: http://localhost:5000/employees")
            print("📊 حالات الموظفين: http://localhost:5000/employee_statuses")
            print("📅 العطل والإجازات: http://localhost:5000/leaves")
            print("🎓 الشهادات والتكوين: http://localhost:5000/certificates")
            print("⚖️  العقوبات والمكافآت: http://localhost:5000/sanctions")
            print("🔄 التنقلات والحركات: http://localhost:5000/transfers")
            print("📈 مختلف الترقيات: http://localhost:5000/promotions")
            print("⚙️  الإعدادات: http://localhost:5000/settings")
            print("📋 التقارير: http://localhost:5000/reports")
            print("="*70)
            
            print("\n💡 تعليمات:")
            print("   - اضغط Ctrl+C لإيقاف الخادم")
            print("   - النظام يعمل على المنفذ 5000")
            print("   - يمكنك الوصول للنظام من أي متصفح")
            print("="*70)
            
            # انتظار إيقاف الخادم
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n⏹️  تم إيقاف الخادم بواسطة المستخدم")
                process.terminate()
                
        else:
            # قراءة رسائل الخطأ
            stdout, stderr = process.communicate()
            print("❌ فشل في تشغيل الخادم")
            if stderr:
                print(f"خطأ: {stderr}")
                
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    if not check_requirements():
        print("\n❌ لا يمكن تشغيل النظام بسبب متطلبات مفقودة")
        input("اضغط Enter للخروج...")
        return
    
    show_system_info()
    
    print("\n" + "="*70)
    response = input("هل تريد تشغيل النظام؟ (y/n): ").strip().lower()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        start_server()
    else:
        print("👋 تم إلغاء التشغيل")

if __name__ == "__main__":
    main()
