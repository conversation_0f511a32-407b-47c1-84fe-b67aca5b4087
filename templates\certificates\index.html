{% extends "base.html" %}

{% block title %}الشهادات والتكوين - نظام إدارة موظفي الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة الشهادات والتكوين{% endblock %}

{% block content %}
<!-- إحصائيات -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <i class="fas fa-graduation-cap fa-2x mb-2"></i>
            <h3>{{ certificates|length }}</h3>
            <p>الشهادات</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
            <h3>{{ training|length }}</h3>
            <p>دورات التكوين</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <i class="fas fa-language fa-2x mb-2"></i>
            <h3>{{ languages|length }}</h3>
            <p>اللغات</p>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="card mb-4">
    <div class="card-body">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>إضافة جديد
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="addCertificate()">
                    <i class="fas fa-graduation-cap me-2"></i>إضافة شهادة
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="addTraining()">
                    <i class="fas fa-chalkboard-teacher me-2"></i>إضافة تكوين
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="addLanguage()">
                    <i class="fas fa-language me-2"></i>إضافة لغة
                </a></li>
            </ul>
        </div>
        <button class="btn btn-success" onclick="exportData()">
            <i class="fas fa-file-excel me-2"></i>تصدير البيانات
        </button>
    </div>
</div>

<!-- الشهادات -->
<div class="card mb-4">
    <div class="card-header">
        <h4><i class="fas fa-graduation-cap me-2"></i>الشهادات</h4>
    </div>
    <div class="card-body">
        {% if certificates %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع الشهادة</th>
                        <th>التخصص</th>
                        <th>سنة المنح</th>
                        <th>المؤسسة المانحة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for cert in certificates %}
                    <tr>
                        <td>
                            <strong>{{ cert.first_name }} {{ cert.last_name }}</strong>
                            <br><small class="text-muted">{{ cert.registration_number }}</small>
                        </td>
                        <td>
                            {% if cert.certificate_type == 'دكتوراه' %}
                                <span class="badge bg-danger">{{ cert.certificate_type }}</span>
                            {% elif cert.certificate_type == 'ماستر' %}
                                <span class="badge bg-warning">{{ cert.certificate_type }}</span>
                            {% elif cert.certificate_type == 'ليسانس' %}
                                <span class="badge bg-success">{{ cert.certificate_type }}</span>
                            {% elif cert.certificate_type == 'تقني سامي' %}
                                <span class="badge bg-info">{{ cert.certificate_type }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ cert.certificate_type }}</span>
                            {% endif %}
                        </td>
                        <td>{{ cert.specialization or 'غير محدد' }}</td>
                        <td>{{ cert.year_obtained or 'غير محدد' }}</td>
                        <td>{{ cert.institution or 'غير محدد' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد شهادات مسجلة</h5>
            <button class="btn btn-primary" onclick="addCertificate()">
                <i class="fas fa-plus me-2"></i>إضافة شهادة جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- دورات التكوين -->
<div class="card mb-4">
    <div class="card-header">
        <h4><i class="fas fa-chalkboard-teacher me-2"></i>دورات التكوين</h4>
    </div>
    <div class="card-body">
        {% if training %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>موضوع التكوين</th>
                        <th>المدة الزمنية</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>المؤسسة</th>
                        <th>شهادة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for train in training %}
                    <tr>
                        <td>
                            <strong>{{ train.first_name }} {{ train.last_name }}</strong>
                            <br><small class="text-muted">{{ train.registration_number }}</small>
                        </td>
                        <td>{{ train.training_name }}</td>
                        <td>
                            {% if train.duration_days %}
                                <span class="badge bg-info">{{ train.duration_days }} يوم</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>{{ train.start_date or 'غير محدد' }}</td>
                        <td>{{ train.end_date or 'غير محدد' }}</td>
                        <td>{{ train.institution or 'غير محدد' }}</td>
                        <td>
                            {% if train.certificate_obtained %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-secondary">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد دورات تكوين مسجلة</h5>
            <button class="btn btn-success" onclick="addTraining()">
                <i class="fas fa-plus me-2"></i>إضافة دورة تكوين جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- اللغات -->
<div class="card">
    <div class="card-header">
        <h4><i class="fas fa-language me-2"></i>اللغات</h4>
    </div>
    <div class="card-body">
        {% if languages %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع اللغة</th>
                        <th>المستوى العام</th>
                        <th>الكتابة</th>
                        <th>القراءة</th>
                        <th>التحدث</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for lang in languages %}
                    <tr>
                        <td>
                            <strong>{{ lang.first_name }} {{ lang.last_name }}</strong>
                            <br><small class="text-muted">{{ lang.registration_number }}</small>
                        </td>
                        <td>{{ lang.language_name }}</td>
                        <td>
                            {% if lang.proficiency_level == 'ممتاز' %}
                                <span class="badge bg-success">{{ lang.proficiency_level }}</span>
                            {% elif lang.proficiency_level == 'جيد' %}
                                <span class="badge bg-info">{{ lang.proficiency_level }}</span>
                            {% elif lang.proficiency_level == 'متوسط' %}
                                <span class="badge bg-warning">{{ lang.proficiency_level }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ lang.proficiency_level or 'غير محدد' }}</span>
                            {% endif %}
                        </td>
                        <td>{{ lang.writing_level or 'غير محدد' }}</td>
                        <td>{{ lang.reading_level or 'غير محدد' }}</td>
                        <td>{{ lang.speaking_level or 'غير محدد' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-language fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد لغات مسجلة</h5>
            <button class="btn btn-warning" onclick="addLanguage()">
                <i class="fas fa-plus me-2"></i>إضافة لغة جديدة
            </button>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addCertificate() {
    alert('إضافة شهادة جديدة');
    // يمكن إضافة modal أو توجيه لصفحة الإضافة
}

function addTraining() {
    alert('إضافة دورة تكوين جديدة');
    // يمكن إضافة modal أو توجيه لصفحة الإضافة
}

function addLanguage() {
    alert('إضافة لغة جديدة');
    // يمكن إضافة modal أو توجيه لصفحة الإضافة
}

function exportData() {
    alert('سيتم تصدير بيانات الشهادات والتكوين إلى Excel');
    // يمكن إضافة وظيفة التصدير الفعلية
}
</script>
{% endblock %}
