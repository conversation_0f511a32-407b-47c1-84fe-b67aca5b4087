#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة موظفي الجمارك الجزائرية الجديد
Run New Algerian Customs Employee Management System

نظام حديث بتصميم مستوحى من موقع بريد الجزائر
Modern system inspired by Algeria Post website design
"""

import os
import sys
import webbrowser
from threading import Timer

def open_browser():
    """فتح المتصفح تلقائياً"""
    webbrowser.open('http://localhost:5000')

def main():
    """تشغيل النظام الجديد"""
    print("=" * 60)
    print("🇩🇿 نظام إدارة موظفي الجمارك الجزائرية الجديد")
    print("🇩🇿 New Algerian Customs Employee Management System")
    print("=" * 60)
    print()
    print("✨ تصميم جديد مستوحى من موقع بريد الجزائر")
    print("✨ New design inspired by Algeria Post website")
    print()
    print("🚀 جاري تشغيل النظام...")
    print("🚀 Starting the system...")
    print()
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        'app.py',
        'templates/base.html',
        'templates/index.html',
        'static/css/style.css',
        'static/js/main.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة / Missing files:")
        for file in missing_files:
            print(f"   - {file}")
        print()
        print("يرجى التأكد من وجود جميع الملفات المطلوبة")
        print("Please ensure all required files exist")
        return
    
    print("✅ جميع الملفات موجودة")
    print("✅ All files exist")
    print()
    
    # فتح المتصفح بعد 3 ثوان
    Timer(3.0, open_browser).start()
    
    print("🌐 سيتم فتح المتصفح تلقائياً على:")
    print("🌐 Browser will open automatically at:")
    print("   http://localhost:5000")
    print()
    print("📋 الميزات الجديدة:")
    print("📋 New Features:")
    print("   ✨ تصميم احترافي مستوحى من موقع بريد الجزائر")
    print("   ✨ Professional design inspired by Algeria Post")
    print("   🎨 أيقونات سداسية تفاعلية")
    print("   🎨 Interactive hexagonal icons")
    print("   📱 تصميم متجاوب لجميع الأجهزة")
    print("   📱 Responsive design for all devices")
    print("   🎯 واجهة سهلة الاستخدام")
    print("   🎯 User-friendly interface")
    print("   🔒 نظام آمن ومحمي")
    print("   🔒 Secure and protected system")
    print()
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("🔧 To stop: Press Ctrl+C")
    print("=" * 60)
    
    try:
        # تشغيل التطبيق
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print(f"❌ Error importing app: {e}")
    except KeyboardInterrupt:
        print("\n")
        print("🛑 تم إيقاف النظام بواسطة المستخدم")
        print("🛑 System stopped by user")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print(f"❌ Error running system: {e}")

if __name__ == '__main__':
    main()
