{% extends "base.html" %}

{% block title %}تسجيل وفاة - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-dark">
                <div class="card-header bg-dark text-white">
                    <h3 class="card-title">
                        <i class="fas fa-cross"></i>
                        تسجيل وفاة
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user"></i> معلومات الموظف</h5>
                        <strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}<br>
                        <strong>رقم التسجيل:</strong> {{ employee.registration_number }}<br>
                        <strong>تاريخ الميلاد:</strong> {{ employee.birth_date|format_arabic_date if employee.birth_date }}<br>
                        <strong>الحالة الحالية:</strong> <span class="badge {{ employee.status|status_badge_class }}">{{ employee.status }}</span>
                    </div>

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تسجيل الوفاة سيغير حالة الموظف إلى "متوفى" نهائياً ولا يمكن التراجع عن هذا الإجراء.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="death_date">تاريخ الوفاة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="death_date" name="death_date" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال تاريخ الوفاة
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="death_place">مكان الوفاة</label>
                                    <input type="text" class="form-control" id="death_place" name="death_place" 
                                           placeholder="مثال: مستشفى الجامعي - الجزائر">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="death_cause">سبب الوفاة</label>
                            <select class="form-control" id="cause_select" onchange="handleCauseChange()">
                                <option value="">اختر سبب الوفاة (اختياري)</option>
                                <option value="مرض">مرض</option>
                                <option value="حادث">حادث</option>
                                <option value="وفاة طبيعية">وفاة طبيعية</option>
                                <option value="أسباب صحية">أسباب صحية</option>
                                <option value="غير محدد">غير محدد</option>
                                <option value="أخرى">أخرى (حدد)</option>
                            </select>
                            <textarea class="form-control mt-2" id="death_cause" name="death_cause" rows="2" 
                                      placeholder="سبب الوفاة (اختياري - حسب المعلومات المتاحة)"></textarea>
                            <small class="form-text text-muted">هذه المعلومة اختيارية ويمكن تركها فارغة إذا لم تكن متاحة</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="certificate_number">رقم شهادة الوفاة</label>
                                    <input type="text" class="form-control" id="certificate_number" name="certificate_number" 
                                           placeholder="مثال: 2024/DC/001">
                                    <small class="form-text text-muted">رقم شهادة الوفاة الرسمية</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="certificate_date">تاريخ إصدار الشهادة</label>
                                    <input type="date" class="form-control" id="certificate_date" name="certificate_date">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="أي ملاحظات إضافية (اختياري)"></textarea>
                        </div>

                        <!-- معلومات محسوبة -->
                        <div class="card bg-light mb-3" id="calculated_info" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">معلومات محسوبة</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>العمر عند الوفاة:</strong>
                                        <span id="age_at_death">-</span> سنة
                                    </div>
                                    <div class="col-md-4">
                                        <strong>سنوات الخدمة:</strong>
                                        <span id="service_years">-</span> سنة
                                    </div>
                                    <div class="col-md-4">
                                        <strong>حالة الخدمة:</strong>
                                        <span id="service_status">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تأكيدات مهمة -->
                        <div class="card border-danger mb-3">
                            <div class="card-header bg-danger text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    تأكيدات مهمة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="confirm_death" required>
                                        <label class="custom-control-label" for="confirm_death">
                                            <strong>أؤكد وفاة الموظف وصحة المعلومات المدخلة</strong>
                                        </label>
                                        <div class="invalid-feedback">
                                            يجب تأكيد وفاة الموظف
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="confirm_irreversible" required>
                                        <label class="custom-control-label" for="confirm_irreversible">
                                            <strong>أفهم أن هذا الإجراء نهائي ولا يمكن التراجع عنه</strong>
                                        </label>
                                        <div class="invalid-feedback">
                                            يجب تأكيد فهم طبيعة الإجراء النهائية
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-0">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="confirm_authority" required>
                                        <label class="custom-control-label" for="confirm_authority">
                                            <strong>أؤكد أن لدي الصلاحية لتسجيل هذه المعلومة</strong>
                                        </label>
                                        <div class="invalid-feedback">
                                            يجب تأكيد الصلاحية
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-dark">
                                <i class="fas fa-cross"></i> تسجيل الوفاة
                            </button>
                            <a href="{{ url_for('employee_status_history', employee_id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات الموظف من الخادم
const employeeData = {
    birthDate: '{{ employee.birth_date }}',
    hireDate: '{{ employee.hire_date }}'
};

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// معالجة تغيير سبب الوفاة
function handleCauseChange() {
    var select = document.getElementById('cause_select');
    var textarea = document.getElementById('death_cause');
    
    if (select.value && select.value !== 'أخرى') {
        textarea.value = select.value;
    } else if (select.value === 'أخرى') {
        textarea.value = '';
        textarea.focus();
    }
}

// حساب المعلومات عند تغيير تاريخ الوفاة
document.getElementById('death_date').addEventListener('change', calculateDeathInfo);

function calculateDeathInfo() {
    const deathDate = document.getElementById('death_date').value;
    
    if (!deathDate) {
        document.getElementById('calculated_info').style.display = 'none';
        return;
    }
    
    const death = new Date(deathDate);
    
    // حساب العمر عند الوفاة
    if (employeeData.birthDate) {
        const birth = new Date(employeeData.birthDate);
        const ageAtDeath = Math.floor((death - birth) / (365.25 * 24 * 60 * 60 * 1000));
        document.getElementById('age_at_death').textContent = ageAtDeath;
    } else {
        document.getElementById('age_at_death').textContent = 'غير محدد';
    }
    
    // حساب سنوات الخدمة
    if (employeeData.hireDate) {
        const hire = new Date(employeeData.hireDate);
        const serviceYears = Math.floor((death - hire) / (365.25 * 24 * 60 * 60 * 1000));
        document.getElementById('service_years').textContent = serviceYears;
        
        // تحديد حالة الخدمة
        let serviceStatus = '';
        if (serviceYears >= 32) {
            serviceStatus = 'مستحق للمعاش الكامل';
        } else if (serviceYears >= 15) {
            serviceStatus = 'مستحق لمعاش نسبي';
        } else {
            serviceStatus = 'أقل من الحد الأدنى للمعاش';
        }
        document.getElementById('service_status').textContent = serviceStatus;
    } else {
        document.getElementById('service_years').textContent = 'غير محدد';
        document.getElementById('service_status').textContent = 'غير محدد';
    }
    
    document.getElementById('calculated_info').style.display = 'block';
}

// التحقق من صحة التواريخ
document.getElementById('death_date').addEventListener('change', function() {
    const deathDate = new Date(this.value);
    const today = new Date();
    
    if (deathDate > today) {
        alert('تاريخ الوفاة لا يمكن أن يكون في المستقبل');
        this.value = '';
        return;
    }
    
    // التحقق من تاريخ الميلاد
    if (employeeData.birthDate) {
        const birth = new Date(employeeData.birthDate);
        if (deathDate <= birth) {
            alert('تاريخ الوفاة لا يمكن أن يكون قبل أو في نفس تاريخ الميلاد');
            this.value = '';
            return;
        }
    }
    
    // التحقق من تاريخ التوظيف
    if (employeeData.hireDate) {
        const hire = new Date(employeeData.hireDate);
        if (deathDate < hire) {
            alert('تاريخ الوفاة لا يمكن أن يكون قبل تاريخ التوظيف');
            this.value = '';
            return;
        }
    }
});

// التحقق من تاريخ إصدار الشهادة
document.getElementById('certificate_date').addEventListener('change', function() {
    const certificateDate = new Date(this.value);
    const deathDate = document.getElementById('death_date').value;
    
    if (deathDate && certificateDate < new Date(deathDate)) {
        alert('تاريخ إصدار الشهادة لا يمكن أن يكون قبل تاريخ الوفاة');
        this.value = '';
    }
});

// تأكيد إضافي قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const employeeName = '{{ employee.first_name }} {{ employee.last_name }}';
    if (!confirm(`هل أنت متأكد من تسجيل وفاة الموظف ${employeeName}؟\n\nهذا الإجراء نهائي ولا يمكن التراجع عنه.`)) {
        e.preventDefault();
    }
});

// تعيين تاريخ اليوم كافتراضي لتاريخ الشهادة
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('certificate_date').value = today;
});
</script>
{% endblock %}