#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import sqlite3
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'customs-algeria-2025'

def init_database():
    """تهيئة قاعدة البيانات"""
    try:
        conn = sqlite3.connect('customs_employees.db')
        cursor = conn.cursor()
        
        # جدول أسباب الاستيداع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_of_absence_reasons (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                reason TEXT NOT NULL UNIQUE,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج أسباب افتراضية
        reasons = [
            'رعاية الأطفال',
            'الدراسة', 
            'ظروف شخصية',
            'ظروف صحية',
            'مرافقة الزوج',
            'ظروف عائلية',
            'أسباب أخرى'
        ]
        
        for reason in reasons:
            cursor.execute('''
                INSERT OR IGNORE INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
        
        conn.commit()
        conn.close()
        print("✅ تم تهيئة قاعدة البيانات")
        return True
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    try:
        return render_template('index.html')
    except:
        return '''
        <html dir="rtl">
        <head>
            <title>نظام إدارة موظفي الجمارك</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial; text-align: center; padding: 50px; }
                .btn { padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <h1>🎉 نظام إدارة موظفي الجمارك الجزائرية</h1>
            <p>مرحباً بك في النظام</p>
            <a href="/special_status/" class="btn">الحالات الخاصة</a>
        </body>
        </html>
        '''

@app.route('/special_status/')
def special_status_index():
    """الصفحة الرئيسية للحالات الخاصة"""
    return '''
    <html dir="rtl">
    <head>
        <title>الحالات الخاصة</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial; padding: 20px; }
            .card { border: 1px solid #ddd; padding: 20px; margin: 10px; border-radius: 5px; }
            .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; }
            .btn-secondary { background: #6c757d; }
            .btn-success { background: #28a745; }
            h1 { color: #333; }
            h3 { color: #666; }
        </style>
    </head>
    <body>
        <h1>🏛️ نظام إدارة الحالات الخاصة للموظفين</h1>
        
        <div class="card">
            <h3>📊 إحصائيات سريعة</h3>
            <p>عدد حالات الاستيداع النشطة: 0</p>
            <p>عدد الاستقالات المعلقة: 0</p>
        </div>
        
        <div class="card">
            <h3>🔧 ملف الاستيداع</h3>
            <p>إدارة حالات الاستيداع مع تتبع الحد الأقصى 5 سنوات</p>
            <a href="/special_status/leave_reasons_settings" class="btn btn-secondary">⚙️ إعدادات الأسباب</a>
            <a href="/special_status/leave_of_absence/add" class="btn btn-success">➕ إضافة استيداع</a>
        </div>
        
        <div class="card">
            <h3>📋 الحالات الأخرى</h3>
            <p>الاستقالة، التوقيف، العزل، الوفيات، التقاعد، التحويل الخارجي</p>
            <p><em>قيد التطوير...</em></p>
        </div>
        
        <a href="/" class="btn">🏠 العودة للرئيسية</a>
    </body>
    </html>
    '''

@app.route('/special_status/leave_reasons_settings')
def leave_reasons_settings():
    """إعدادات أسباب الاستيداع"""
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    
    try:
        reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        reasons = []
    finally:
        conn.close()
    
    reasons_html = ""
    for i, reason in enumerate(reasons, 1):
        reasons_html += f'''
        <tr>
            <td>{i}</td>
            <td>{reason['reason']}</td>
            <td><span style="color: green;">نشط</span></td>
            <td>
                <button onclick="deactivateReason({reason['id']})" style="background: red; color: white; border: none; padding: 5px 10px; border-radius: 3px;">إلغاء تفعيل</button>
            </td>
        </tr>
        '''
    
    return f'''
    <html dir="rtl">
    <head>
        <title>إعدادات أسباب الاستيداع</title>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial; padding: 20px; }}
            table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
            th, td {{ border: 1px solid #ddd; padding: 10px; text-align: right; }}
            th {{ background: #f5f5f5; }}
            .btn {{ padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; border: none; }}
            .form-group {{ margin: 10px 0; }}
            input[type="text"] {{ padding: 8px; width: 300px; }}
        </style>
    </head>
    <body>
        <h1>⚙️ إعدادات أسباب الاستيداع</h1>
        
        <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <strong>ملاحظة:</strong> الحد الأقصى للاستيداع هو 5 سنوات (60 شهر) في الحياة الوظيفية للموظف.
        </div>
        
        <h3>إضافة سبب جديد</h3>
        <form method="POST" action="/special_status/add_leave_reason">
            <div class="form-group">
                <input type="text" name="reason" placeholder="اكتب سبب الاستيداع..." required>
                <button type="submit" class="btn">إضافة</button>
            </div>
        </form>
        
        <h3>الأسباب الحالية</h3>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>سبب الاستيداع</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {reasons_html}
            </tbody>
        </table>
        
        <a href="/special_status/" class="btn">🔙 العودة</a>
        
        <script>
        function deactivateReason(id) {{
            if (confirm('هل أنت متأكد من إلغاء تفعيل هذا السبب؟')) {{
                fetch('/special_status/api/leave_reason/' + id + '/deactivate', {{
                    method: 'POST'
                }})
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        alert('تم إلغاء تفعيل السبب بنجاح');
                        location.reload();
                    }} else {{
                        alert('خطأ في العملية');
                    }}
                }});
            }}
        }}
        </script>
    </body>
    </html>
    '''

@app.route('/special_status/add_leave_reason', methods=['POST'])
def add_leave_reason():
    """إضافة سبب استيداع جديد"""
    reason = request.form.get('reason')
    if reason:
        conn = sqlite3.connect('customs_employees.db')
        try:
            conn.execute('''
                INSERT INTO leave_of_absence_reasons (reason)
                VALUES (?)
            ''', (reason,))
            conn.commit()
        except:
            pass
        finally:
            conn.close()
    
    return redirect('/special_status/leave_reasons_settings')

@app.route('/special_status/leave_of_absence/add')
def add_leave_of_absence():
    """إضافة استيداع جديد"""
    # الحصول على قائمة الموظفين
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    
    try:
        employees = conn.execute('''
            SELECT id, registration_number, first_name, last_name
            FROM employees 
            ORDER BY registration_number
        ''').fetchall()
    except:
        employees = []
    
    try:
        leave_reasons = conn.execute('''
            SELECT * FROM leave_of_absence_reasons 
            WHERE is_active = 1 
            ORDER BY reason
        ''').fetchall()
    except:
        leave_reasons = []
    finally:
        conn.close()
    
    employees_options = ""
    for emp in employees:
        employees_options += f'<option value="{emp["id"]}">{emp["registration_number"]} - {emp["first_name"]} {emp["last_name"]}</option>'
    
    reasons_options = ""
    for reason in leave_reasons:
        reasons_options += f'<option value="{reason["reason"]}">{reason["reason"]}</option>'
    
    return f'''
    <html dir="rtl">
    <head>
        <title>إضافة استيداع جديد</title>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial; padding: 20px; }}
            .form-group {{ margin: 15px 0; }}
            label {{ display: block; margin-bottom: 5px; font-weight: bold; }}
            input, select, textarea {{ padding: 8px; width: 300px; }}
            .btn {{ padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; border: none; }}
            .btn-success {{ background: #28a745; }}
            .alert {{ background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        </style>
    </head>
    <body>
        <h1>➕ إضافة استيداع جديد</h1>
        
        <form method="POST">
            <div class="form-group">
                <label>الموظف *</label>
                <select name="employee_id" required onchange="loadEmployeeInfo()">
                    <option value="">اختر الموظف</option>
                    {employees_options}
                </select>
            </div>
            
            <div id="employeeInfo" style="display: none;" class="alert">
                <h4>معلومات الموظف ورصيد الاستيداع:</h4>
                <div id="employeeDetails"></div>
            </div>
            
            <div class="form-group">
                <label>المدة (بالأشهر) *</label>
                <input type="number" name="duration_months" min="1" max="60" required onchange="calculateEndDate()">
                <small>الحد الأقصى: 60 شهر (5 سنوات) في الحياة الوظيفية</small>
            </div>
            
            <div class="form-group">
                <label>سبب الاستيداع *</label>
                <select name="reason" required>
                    <option value="">اختر السبب</option>
                    {reasons_options}
                </select>
                <br><small><a href="/special_status/leave_reasons_settings" target="_blank">⚙️ إدارة أسباب الاستيداع</a></small>
            </div>
            
            <div class="form-group">
                <label>تاريخ بداية الاستيداع *</label>
                <input type="date" name="start_date" required onchange="calculateEndDate()">
            </div>
            
            <div class="form-group">
                <label>تاريخ نهاية الاستيداع</label>
                <input type="date" name="end_date" readonly>
                <small>يتم حساب هذا التاريخ تلقائياً بإضافة المدة إلى تاريخ البداية</small>
            </div>
            
            <div class="form-group">
                <label>رقم المقرر/الوثيقة</label>
                <input type="text" name="decision_number" placeholder="رقم المقرر أو الوثيقة">
            </div>
            
            <div class="form-group">
                <label>تاريخ المقرر/الوثيقة</label>
                <input type="date" name="decision_date">
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-success">💾 حفظ الاستيداع</button>
                <a href="/special_status/" class="btn">🔙 العودة</a>
            </div>
        </form>
        
        <script>
        function loadEmployeeInfo() {{
            const employeeId = document.querySelector('[name="employee_id"]').value;
            if (!employeeId) {{
                document.getElementById('employeeInfo').style.display = 'none';
                return;
            }}
            
            // عرض معلومات افتراضية
            const details = `
                <div style="display: flex; gap: 50px;">
                    <div>
                        <strong>رقم التسجيل:</strong> 123456<br>
                        <strong>الاسم الكامل:</strong> موظف تجريبي<br>
                        <strong>الرتبة:</strong> موظف<br>
                        <strong>المصلحة:</strong> الإدارة العامة
                    </div>
                    <div>
                        <strong>إجمالي الاستيداع المستخدم:</strong> 0 شهر (0 سنة)<br>
                        <strong>الرصيد المتبقي:</strong> 60 شهر (5 سنوات)<br>
                        <strong>الحد الأقصى المسموح:</strong> 5 سنوات<br>
                    </div>
                </div>
            `;
            
            document.getElementById('employeeDetails').innerHTML = details;
            document.getElementById('employeeInfo').style.display = 'block';
        }}
        
        function calculateEndDate() {{
            const startDate = document.querySelector('[name="start_date"]').value;
            const durationMonths = parseInt(document.querySelector('[name="duration_months"]').value);
            
            if (startDate && durationMonths) {{
                const start = new Date(startDate);
                const end = new Date(start);
                end.setMonth(end.getMonth() + durationMonths);
                
                document.querySelector('[name="end_date"]').value = end.toISOString().split('T')[0];
            }}
        }}
        
        // تعيين التاريخ الحالي كافتراضي
        document.addEventListener('DOMContentLoaded', function() {{
            const today = new Date().toISOString().split('T')[0];
            document.querySelector('[name="start_date"]').value = today;
        }});
        </script>
    </body>
    </html>
    '''

@app.route('/special_status/api/leave_reason/<int:reason_id>/deactivate', methods=['POST'])
def api_deactivate_leave_reason(reason_id):
    """API لإلغاء تفعيل سبب الاستيداع"""
    conn = sqlite3.connect('customs_employees.db')
    try:
        conn.execute('''
            UPDATE leave_of_absence_reasons 
            SET is_active = 0
            WHERE id = ?
        ''', (reason_id,))
        conn.commit()
        return jsonify({'success': True})
    except:
        return jsonify({'success': False, 'error': 'خطأ في العملية'}), 500
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 تشغيل الخادم...")
    print("🌐 الخادم سيعمل على: http://localhost:5000")
    print("📋 الحالات الخاصة: http://localhost:5000/special_status/")
    
    # تهيئة قاعدة البيانات
    init_database()
    
    try:
        app.run(debug=False, host='127.0.0.1', port=5000, threaded=True)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print("🔧 جرب تشغيل الأمر مرة أخرى")
