{% extends "base.html" %}

{% block title %}قائمة الموظفين المحولين خارجياً{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exchange-alt"></i>
                        قائمة الموظفين المحولين خارجياً
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> معلومات:</h6>
                                <p class="mb-0">إجمالي الموظفين المحولين خارجياً: <strong>{{ transfers|length }}</strong></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> ملاحظة:</h6>
                                <p class="mb-0">هؤلاء الموظفون لا يُحسبون في إجمالي عدد الموظفين النشطين</p>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ url_for('simple_status.dashboard') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button class="btn btn-success" onclick="window.print()">
                                <i class="fas fa-print"></i>
                                طباعة القائمة
                            </button>
                        </div>
                    </div>

                    {% if transfers %}
                    <!-- جدول المحولين خارجياً -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-warning">
                                <tr>
                                    <th>#</th>
                                    <th>رقم التسجيل</th>
                                    <th>الاسم الكامل</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>تاريخ التحويل</th>
                                    <th>الجهة المحول إليها</th>
                                    <th>رقم المقرر</th>
                                    <th>تاريخ المقرر</th>
                                    <th>تاريخ النقل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in transfers %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <span class="badge badge-secondary">
                                            {{ transfer.registration_number }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ transfer.first_name }} {{ transfer.last_name }}</strong>
                                    </td>
                                    <td>
                                        {% if transfer.hire_date %}
                                            {{ transfer.hire_date }}
                                            <br>
                                            <small class="text-muted">
                                                ({{ (transfer.transfer_date|strptime('%Y-%m-%d')).year - (transfer.hire_date|strptime('%Y-%m-%d')).year }} سنة خدمة)
                                            </small>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-warning">
                                            {{ transfer.transfer_date }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ transfer.destination_directorate }}</strong>
                                    </td>
                                    <td>
                                        <code>{{ transfer.decision_number }}</code>
                                    </td>
                                    <td>
                                        {{ transfer.decision_date }}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ transfer.transferred_at[:10] }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    onclick="viewEmployeeData({{ transfer.id }}, '{{ transfer.first_name }} {{ transfer.last_name }}')">
                                                <i class="fas fa-eye"></i>
                                                عرض البيانات
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- إحصائيات الجهات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-warning">
                                <i class="fas fa-chart-pie"></i>
                                إحصائيات التحويل حسب الجهة
                            </h5>
                            <div class="row">
                                {% set destinations = {} %}
                                {% for transfer in transfers %}
                                    {% if destinations.update({transfer.destination_directorate: destinations.get(transfer.destination_directorate, 0) + 1}) %}{% endif %}
                                {% endfor %}
                                
                                {% for destination, count in destinations.items() %}
                                <div class="col-md-4 mb-2">
                                    <div class="card border-warning">
                                        <div class="card-body text-center">
                                            <h6 class="text-warning">{{ count }}</h6>
                                            <small>{{ destination }}</small>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <!-- رسالة عدم وجود بيانات -->
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد موظفون محولون خارجياً</h5>
                        <p class="text-muted">لم يتم تسجيل أي تحويل خارجي حتى الآن</p>
                        <a href="{{ url_for('employees') }}" class="btn btn-primary">
                            <i class="fas fa-users"></i>
                            عرض قائمة الموظفين
                        </a>
                    </div>
                    {% endif %}

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض بيانات الموظف -->
<div class="modal fade" id="employeeDataModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user"></i>
                    بيانات الموظف المحول خارجياً
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="employeeDataContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>جاري تحميل البيانات...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function viewEmployeeData(transferId, employeeName) {
    // فتح النافذة المنبثقة
    $('#employeeDataModal').modal('show');
    
    // تحديث عنوان النافذة
    $('#employeeDataModal .modal-title').html(`
        <i class="fas fa-user"></i>
        بيانات الموظف المحول: ${employeeName}
    `);
    
    // جلب البيانات من API
    fetch(`/final_status/api/employee_data/external_transfers/${transferId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                $('#employeeDataContent').html(`
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> خطأ:</h6>
                        <p class="mb-0">${data.error}</p>
                    </div>
                `);
            } else {
                // عرض البيانات الأصلية للموظف
                let content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">المعلومات الشخصية:</h6>
                            <p><strong>الاسم:</strong> ${data.first_name} ${data.last_name}</p>
                            <p><strong>رقم التسجيل:</strong> ${data.registration_number}</p>
                            <p><strong>تاريخ الميلاد:</strong> ${data.birth_date || 'غير محدد'}</p>
                            <p><strong>الجنس:</strong> ${data.gender || 'غير محدد'}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">المعلومات الوظيفية:</h6>
                            <p><strong>تاريخ التوظيف:</strong> ${data.hire_date || 'غير محدد'}</p>
                            <p><strong>الرتبة:</strong> ${data.rank_id || 'غير محدد'}</p>
                            <p><strong>السلك:</strong> ${data.corps_id || 'غير محدد'}</p>
                            <p><strong>المصلحة:</strong> ${data.service_id || 'غير محدد'}</p>
                        </div>
                    </div>
                `;
                
                $('#employeeDataContent').html(content);
            }
        })
        .catch(error => {
            $('#employeeDataContent').html(`
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> خطأ في الشبكة:</h6>
                    <p class="mb-0">لا يمكن تحميل البيانات حالياً</p>
                </div>
            `);
        });
}

// تحسين الطباعة
window.addEventListener('beforeprint', function() {
    document.querySelectorAll('.btn, .modal').forEach(el => {
        el.style.display = 'none';
    });
});

window.addEventListener('afterprint', function() {
    document.querySelectorAll('.btn').forEach(el => {
        el.style.display = '';
    });
});
</script>

<style>
@media print {
    .btn, .modal {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}
</style>
{% endblock %}