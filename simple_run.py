#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط للتطبيق
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from simple_special_status_manager import SimpleSpecialStatusManager
import sqlite3

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'customs-algeria-2025'

# إنشاء مدير الحالات الخاصة
simple_manager = SimpleSpecialStatusManager()

def get_db_connection():
    """اتصال بقاعدة البيانات"""
    conn = sqlite3.connect('customs_employees.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/special_status/')
def special_status_index():
    """الصفحة الرئيسية للحالات الخاصة"""
    stats = simple_manager.get_statistics()
    return render_template('special_status/index.html', stats=stats)

@app.route('/special_status/leave_reasons_settings')
def leave_reasons_settings():
    """إعدادات أسباب الاستيداع"""
    reasons = simple_manager.get_leave_reasons()
    return render_template('special_status/leave_reasons_settings.html', reasons=reasons)

@app.route('/special_status/add_leave_reason', methods=['POST'])
def add_leave_reason():
    """إضافة سبب استيداع جديد"""
    reason = request.form.get('reason')
    if reason and simple_manager.add_leave_reason(reason):
        flash('تم إضافة السبب بنجاح', 'success')
    else:
        flash('خطأ في إضافة السبب', 'error')
    return redirect('/special_status/leave_reasons_settings')

@app.route('/special_status/leave_of_absence/add', methods=['GET', 'POST'])
def add_leave_of_absence():
    """إضافة استيداع جديد"""
    if request.method == 'POST':
        data = {
            'employee_id': request.form.get('employee_id'),
            'duration_months': int(request.form.get('duration_months')),
            'reason': request.form.get('reason'),
            'start_date': request.form.get('start_date'),
            'decision_number': request.form.get('decision_number'),
            'decision_date': request.form.get('decision_date')
        }
        
        success, message = simple_manager.add_leave_of_absence(data)
        if success:
            flash('تم إضافة الاستيداع بنجاح', 'success')
            return redirect('/special_status/')
        else:
            flash(f'خطأ: {message}', 'error')
    
    # الحصول على قائمة الموظفين النشطين وأسباب الاستيداع
    conn = get_db_connection()
    try:
        employees = conn.execute('''
            SELECT id, registration_number, first_name, last_name
            FROM employees 
            WHERE status = 'نشط'
            ORDER BY registration_number
        ''').fetchall()
    except:
        employees = []
    conn.close()
    
    leave_reasons = simple_manager.get_leave_reasons()
    
    return render_template('special_status/add_leave_of_absence.html', 
                         employees=employees, leave_reasons=leave_reasons)

@app.route('/special_status/api/employee/<int:employee_id>/leave_balance')
def api_employee_leave_balance(employee_id):
    """API للحصول على رصيد الاستيداع للموظف"""
    balance = simple_manager.get_employee_leave_balance(employee_id)
    return jsonify(balance)

@app.route('/special_status/api/employee/<int:employee_id>')
def api_employee_info(employee_id):
    """API للحصول على معلومات الموظف"""
    conn = get_db_connection()
    try:
        employee = conn.execute('''
            SELECT e.*, r.name as rank_name, s.name as service_name
            FROM employees e
            LEFT JOIN ranks r ON e.current_rank_id = r.id
            LEFT JOIN services s ON e.current_service_id = s.id
            WHERE e.id = ?
        ''', (employee_id,)).fetchone()
        
        if employee:
            return jsonify({
                'id': employee['id'],
                'registration_number': employee['registration_number'],
                'first_name': employee['first_name'],
                'last_name': employee['last_name'],
                'rank_name': employee['rank_name'],
                'service_name': employee['service_name'],
                'status': employee['status']
            })
        else:
            return jsonify({'error': 'الموظف غير موجود'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/special_status/api/leave_reason/<int:reason_id>/deactivate', methods=['POST'])
def api_deactivate_leave_reason(reason_id):
    """API لإلغاء تفعيل سبب الاستيداع"""
    if simple_manager.deactivate_leave_reason(reason_id):
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': 'خطأ في العملية'}), 500

if __name__ == '__main__':
    print("🚀 تشغيل التطبيق المبسط...")
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("📋 الحالات الخاصة: http://localhost:5000/special_status/")
    app.run(debug=True, host='0.0.0.0', port=5000)
