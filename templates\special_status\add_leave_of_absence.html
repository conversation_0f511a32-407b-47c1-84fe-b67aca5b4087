{% extends "base.html" %}

{% block page_title %}إضافة استيداع جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-user-clock me-2"></i>
                            إضافة استيداع جديد
                        </h3>
                        <a href="{{ url_for('special_status.leave_of_absence') }}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" id="leaveForm">
                        <div class="row">
                            <!-- اختيار الموظف -->
                            <div class="col-md-6 mb-3">
                                <label for="employee_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                                <select class="form-select" id="employee_id" name="employee_id" required onchange="loadEmployeeInfo()">
                                    <option value="">اختر الموظف</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">
                                        {{ employee.registration_number }} - {{ employee.first_name }} {{ employee.last_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- المدة بالأشهر -->
                            <div class="col-md-6 mb-3">
                                <label for="duration_months" class="form-label">المدة (بالأشهر) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="duration_months" name="duration_months" 
                                       min="1" max="60" required onchange="calculateEndDate()">
                                <div class="form-text">الحد الأقصى: 60 شهر (5 سنوات) في الحياة الوظيفية</div>
                            </div>
                        </div>

                        <!-- معلومات الموظف ورصيد الاستيداع -->
                        <div id="employeeInfo" class="row mb-3" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6>معلومات الموظف ورصيد الاستيداع:</h6>
                                    <div id="employeeDetails"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- سبب الاستيداع -->
                            <div class="col-md-6 mb-3">
                                <label for="reason" class="form-label">سبب الاستيداع <span class="text-danger">*</span></label>
                                <select class="form-select" id="reason" name="reason" required>
                                    <option value="">اختر السبب</option>
                                    {% for reason in leave_reasons %}
                                    <option value="{{ reason.reason }}">{{ reason.reason }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <a href="{{ url_for('special_status.leave_reasons_settings') }}" target="_blank">
                                        <i class="fas fa-cog me-1"></i>إدارة أسباب الاستيداع
                                    </a>
                                </div>
                            </div>

                            <!-- تاريخ بداية الاستيداع -->
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">تاريخ بداية الاستيداع <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required onchange="calculateEndDate()">
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ نهاية الاستيداع (محسوب تلقائياً) -->
                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">تاريخ نهاية الاستيداع</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" readonly>
                                <div class="form-text">يتم حساب هذا التاريخ تلقائياً بإضافة المدة إلى تاريخ البداية</div>
                            </div>

                            <!-- رقم الفترة (محسوب تلقائياً) -->
                            <div class="col-md-6 mb-3">
                                <label for="period_display" class="form-label">رقم الفترة</label>
                                <input type="text" class="form-control" id="period_display" readonly placeholder="سيتم حسابه تلقائياً">
                                <div class="form-text">يتم تحديد رقم الفترة تلقائياً (الأولى، الثانية، إلخ)</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم المقرر/الوثيقة -->
                            <div class="col-md-6 mb-3">
                                <label for="decision_number" class="form-label">رقم المقرر/الوثيقة</label>
                                <input type="text" class="form-control" id="decision_number" name="decision_number" 
                                       placeholder="رقم المقرر أو الوثيقة">
                            </div>

                            <!-- تاريخ المقرر/الوثيقة -->
                            <div class="col-md-6 mb-3">
                                <label for="decision_date" class="form-label">تاريخ المقرر/الوثيقة</label>
                                <input type="date" class="form-control" id="decision_date" name="decision_date">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>حفظ الاستيداع
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadEmployeeInfo() {
    const employeeId = document.getElementById('employee_id').value;
    
    if (!employeeId) {
        document.getElementById('employeeInfo').style.display = 'none';
        return;
    }
    
    // تحميل معلومات الموظف ورصيد الاستيداع
    Promise.all([
        fetch(`/special_status/api/employee/${employeeId}`),
        fetch(`/special_status/api/employee/${employeeId}/leave_balance`)
    ])
    .then(responses => Promise.all(responses.map(r => r.json())))
    .then(([employeeData, balanceData]) => {
        if (employeeData.error || balanceData.error) {
            alert('خطأ في تحميل البيانات');
            return;
        }
        
        const details = `
            <div class="row">
                <div class="col-md-6">
                    <strong>رقم التسجيل:</strong> ${employeeData.registration_number}<br>
                    <strong>الاسم الكامل:</strong> ${employeeData.first_name} ${employeeData.last_name}<br>
                    <strong>الرتبة:</strong> ${employeeData.rank_name || 'غير محدد'}<br>
                    <strong>المصلحة:</strong> ${employeeData.service_name || 'غير محدد'}
                </div>
                <div class="col-md-6">
                    <strong>إجمالي الاستيداع المستخدم:</strong> ${balanceData.total_used_months} شهر (${balanceData.total_used_years.toFixed(1)} سنة)<br>
                    <strong>الرصيد المتبقي:</strong> ${balanceData.remaining_months} شهر (${balanceData.remaining_years.toFixed(1)} سنة)<br>
                    <strong>الحد الأقصى المسموح:</strong> ${balanceData.max_allowed_years} سنوات<br>
                    ${balanceData.remaining_months <= 0 ? '<span class="text-danger"><strong>تحذير: تم استنفاد رصيد الاستيداع!</strong></span>' : ''}
                </div>
            </div>
        `;
        
        document.getElementById('employeeDetails').innerHTML = details;
        document.getElementById('employeeInfo').style.display = 'block';
        
        // تحديث الحد الأقصى للمدة
        document.getElementById('duration_months').max = balanceData.remaining_months;
        
        // تحديث رقم الفترة
        const periodNumber = Math.floor(balanceData.total_used_months / 12) + 1;
        const periodNames = ['', 'الأولى', 'الثانية', 'الثالثة', 'الرابعة', 'الخامسة'];
        document.getElementById('period_display').value = periodNames[periodNumber] || `الفترة ${periodNumber}`;
    })
    .catch(error => {
        alert('خطأ في تحميل البيانات: ' + error);
    });
}

function calculateEndDate() {
    const startDate = document.getElementById('start_date').value;
    const durationMonths = parseInt(document.getElementById('duration_months').value);
    
    if (startDate && durationMonths) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setMonth(end.getMonth() + durationMonths);
        
        document.getElementById('end_date').value = end.toISOString().split('T')[0];
    }
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
        document.getElementById('leaveForm').reset();
        document.getElementById('employeeInfo').style.display = 'none';
    }
}

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('leaveForm').addEventListener('submit', function(e) {
    const employeeId = document.getElementById('employee_id').value;
    const durationMonths = parseInt(document.getElementById('duration_months').value);
    const reason = document.getElementById('reason').value;
    const startDate = document.getElementById('start_date').value;
    
    if (!employeeId || !durationMonths || !reason || !startDate) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    if (durationMonths <= 0 || durationMonths > 60) {
        e.preventDefault();
        alert('المدة يجب أن تكون بين 1 و 60 شهر');
        return;
    }
    
    // التحقق من أن تاريخ البداية ليس في الماضي البعيد
    const selectedDate = new Date(startDate);
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    
    if (selectedDate < oneYearAgo) {
        e.preventDefault();
        alert('تاريخ بداية الاستيداع لا يمكن أن يكون أكثر من سنة في الماضي');
        return;
    }
});

// تعيين التاريخ الحالي كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').value = today;
});
</script>
{% endblock %}
