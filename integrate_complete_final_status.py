#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دمج النظام الكامل للحالات النهائية
Integrate Complete Final Status System
"""

def integrate_complete_final_status():
    """دمج النظام الكامل للحالات النهائية"""
    print("🔗 دمج النظام الكامل للحالات النهائية")
    print("=" * 70)
    
    # 1. تحديث app.py
    print("1️⃣  تحديث app.py...")
    update_main_app()
    
    # 2. تحديث قالب قائمة الموظفين
    print("2️⃣  تحديث قالب قائمة الموظفين...")
    update_employees_template()
    
    # 3. تحديث لوحة التحكم
    print("3️⃣  تحديث لوحة التحكم...")
    update_dashboard_template()
    
    # 4. إنشاء قوالب إضافية
    print("4️⃣  إنشاء القوالب المتبقية...")
    create_remaining_templates()
    
    print("✅ تم الدمج بنجاح!")

def update_main_app():
    """تحديث ملف app.py الرئيسي"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة الاستيراد
        import_line = "from complete_final_status_routes import register_final_status_routes"
        
        if import_line not in content:
            # البحث عن مكان الاستيراد
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "from simple_status_routes import register_simple_status_routes" in line:
                    lines.insert(i + 1, import_line)
                    break
            content = '\n'.join(lines)
        
        # إضافة تسجيل المسارات
        register_line = "register_final_status_routes(app)"
        
        if register_line not in content:
            # البحث عن مكان التسجيل
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "register_simple_status_routes(app)" in line:
                    lines.insert(i + 1, register_line)
                    break
            content = '\n'.join(lines)
        
        # حفظ الملف المحدث
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث app.py")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث app.py: {e}")

def update_employees_template():
    """تحديث قالب قائمة الموظفين لإضافة أزرار الحالات النهائية"""
    template_path = 'templates/employees/list.html'
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة أزرار الحالات النهائية
        final_status_buttons = '''
                                            <div class="dropdown-divider"></div>
                                            <h6 class="dropdown-header">الحالات النهائية</h6>
                                            <a class="dropdown-item" href="{{ url_for('simple_status.add_death', employee_id=employee.id) }}"
                                               onclick="return confirm('هل أنت متأكد من تسجيل وفاة هذا الموظف؟ سيتم نقله لجدول منفصل.')">
                                                <i class="fas fa-cross text-dark"></i> وفاة
                                            </a>
                                            <a class="dropdown-item" href="{{ url_for('final_status.add_external_transfer', employee_id=employee.id) }}"
                                               onclick="return confirm('هل أنت متأكد من تحويل هذا الموظف خارجياً؟ سيتم نقله لجدول منفصل.')">
                                                <i class="fas fa-exchange-alt text-warning"></i> تحويل خارجي
                                            </a>
                                            <a class="dropdown-item" href="{{ url_for('final_status.add_dismissal', employee_id=employee.id) }}"
                                               onclick="return confirm('هل أنت متأكد من عزل هذا الموظف؟ سيتم نقله لجدول منفصل.')">
                                                <i class="fas fa-user-times text-danger"></i> عزل
                                            </a>
                                            <a class="dropdown-item" href="{{ url_for('final_status.add_retirement', employee_id=employee.id) }}"
                                               onclick="return confirm('هل أنت متأكد من تقاعد هذا الموظف؟ سيتم نقله لجدول منفصل.')">
                                                <i class="fas fa-user-check text-success"></i> تقاعد
                                            </a>
        '''
        
        # البحث عن مكان إضافة الأزرار
        if 'الحالات النهائية' not in content:
            # البحث عن نهاية قائمة الحالات المؤقتة
            temp_status_pattern = '<i class="fas fa-user-clock text-warning"></i> استيداع'
            if temp_status_pattern in content:
                # إضافة الأزرار بعد الحالات المؤقتة
                content = content.replace(
                    '</a>\n                                        </div>',
                    '</a>' + final_status_buttons + '\n                                        </div>'
                )
        
        # حفظ الملف المحدث
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث قالب قائمة الموظفين")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث القالب: {e}")

def update_dashboard_template():
    """تحديث لوحة التحكم لإضافة روابط الحالات النهائية"""
    template_path = 'templates/simple_status/dashboard.html'
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة روابط للحالات النهائية
        final_status_links = '''
                                    <a href="{{ url_for('final_status.external_transfers_list') }}" 
                                       class="btn btn-sm btn-outline-warning mt-2">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
        '''
        
        # البحث عن مكان التحديث
        if 'final_status.external_transfers_list' not in content:
            # تحديث رابط المحولين خارجياً
            content = content.replace(
                '<a href="#" class="btn btn-sm btn-outline-warning mt-2">',
                '<a href="{{ url_for(\'final_status.external_transfers_list\') }}" class="btn btn-sm btn-outline-warning mt-2">'
            )
            
            # تحديث رابط المعزولين
            content = content.replace(
                '<a href="#" class="btn btn-sm btn-outline-danger mt-2">',
                '<a href="{{ url_for(\'final_status.dismissed_list\') }}" class="btn btn-sm btn-outline-danger mt-2">'
            )
            
            # تحديث رابط المتقاعدين
            content = content.replace(
                '<a href="#" class="btn btn-sm btn-outline-success mt-2">',
                '<a href="{{ url_for(\'final_status.retired_list\') }}" class="btn btn-sm btn-outline-success mt-2">'
            )
        
        # حفظ الملف المحدث
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث لوحة التحكم")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث لوحة التحكم: {e}")

def create_remaining_templates():
    """إنشاء القوالب المتبقية"""
    
    # قالب إضافة العزل
    dismissal_template = '''{% extends "base.html" %}

{% block title %}عزل موظف - {{ employee.first_name }} {{ employee.last_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-times"></i>
                        عزل موظف
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- تحذير مهم -->
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم:</h6>
                        <p class="mb-1">• سيتم نقل الموظف من جدول الموظفين النشطين إلى جدول المعزولين</p>
                        <p class="mb-1">• لن يُحسب الموظف في إجمالي عدد الموظفين النشطين</p>
                        <p class="mb-0">• سيتم الاحتفاظ بجميع بياناته في سجل منفصل</p>
                    </div>

                    <!-- معلومات الموظف -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user"></i> معلومات الموظف:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الاسم:</strong> {{ employee.first_name }} {{ employee.last_name }}</p>
                                <p class="mb-1"><strong>رقم التسجيل:</strong> {{ employee.registration_number }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>الحالة الحالية:</strong> {{ employee.status }}</p>
                                {% if employee.hire_date %}
                                <p class="mb-0"><strong>تاريخ التوظيف:</strong> {{ employee.hire_date }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <form method="POST" onsubmit="return confirmDismissal()">
                        <div class="row">
                            <!-- تاريخ العزل -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dismissal_date">
                                        <i class="fas fa-calendar-alt"></i>
                                        تاريخ العزل <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="dismissal_date" 
                                           name="dismissal_date" required>
                                </div>
                            </div>

                            <!-- رقم المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_number">
                                        <i class="fas fa-file-alt"></i>
                                        رقم مقرر العزل <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="decision_number" 
                                           name="decision_number" required
                                           placeholder="رقم المقرر الإداري">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ المقرر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="decision_date">
                                        <i class="fas fa-calendar"></i>
                                        تاريخ المقرر <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="decision_date" 
                                           name="decision_date" required>
                                </div>
                            </div>

                            <!-- سبب العزل -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dismissal_reason">
                                        <i class="fas fa-question-circle"></i>
                                        سبب العزل <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="dismissal_reason" 
                                            name="dismissal_reason" required>
                                        <option value="">اختر السبب...</option>
                                        <option value="مخالفة تأديبية جسيمة">مخالفة تأديبية جسيمة</option>
                                        <option value="إهمال في العمل">إهمال في العمل</option>
                                        <option value="عدم الكفاءة المهنية">عدم الكفاءة المهنية</option>
                                        <option value="مخالفة قانونية">مخالفة قانونية</option>
                                        <option value="سوء السلوك">سوء السلوك</option>
                                        <option value="أسباب أخرى">أسباب أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل إضافية -->
                        <div class="form-group">
                            <label for="details">
                                <i class="fas fa-sticky-note"></i>
                                تفاصيل العزل (اختياري)
                            </label>
                            <textarea class="form-control" id="details" name="details" rows="3"
                                      placeholder="تفاصيل إضافية حول أسباب العزل..."></textarea>
                        </div>

                        <!-- تأكيد العملية -->
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="confirm_dismissal" required>
                                <label class="custom-control-label text-danger" for="confirm_dismissal">
                                    <strong>أؤكد صحة المعلومات وأن الموظف سيتم عزله نهائياً</strong>
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-user-times"></i>
                                تأكيد العزل
                            </button>
                            <a href="{{ url_for('employees') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDismissal() {
    const employeeName = "{{ employee.first_name }} {{ employee.last_name }}";
    const dismissalDate = document.getElementById('dismissal_date').value;
    const dismissalReason = document.getElementById('dismissal_reason').value;
    const decisionNumber = document.getElementById('decision_number').value;
    
    const confirmMessage = `
هل أنت متأكد من عزل الموظف؟

الموظف: ${employeeName}
تاريخ العزل: ${dismissalDate}
السبب: ${dismissalReason}
رقم المقرر: ${decisionNumber}

تحذير: سيتم نقل الموظف لجدول المعزولين وحذفه من قائمة الموظفين النشطين.
هذا الإجراء لا يمكن التراجع عنه بسهولة.
    `;
    
    return confirm(confirmMessage);
}
</script>
{% endblock %}'''
    
    with open('templates/final_status/add_dismissal.html', 'w', encoding='utf-8') as f:
        f.write(dismissal_template)
    
    print("✅ تم إنشاء قالب إضافة العزل")

def test_complete_integration():
    """اختبار التكامل الكامل"""
    print(f"\n🧪 اختبار التكامل الكامل...")
    
    try:
        from complete_status_transfers import CompleteStatusTransfers
        
        transfer_manager = CompleteStatusTransfers()
        stats = transfer_manager.manager.get_statistics()
        
        print("📊 الإحصائيات النهائية:")
        print(f"   النشطين: {stats.get('active', 0)}")
        print(f"   المستودعين: {stats.get('leave_of_absence', 0)}")
        print(f"   المتوفين: {stats.get('deceased', 0)}")
        print(f"   المحولين خارجياً: {stats.get('external_transfer', 0)}")
        print(f"   المعزولين: {stats.get('dismissed', 0)}")
        print(f"   المتقاعدين: {stats.get('retired', 0)}")
        print(f"   الإجمالي النشط: {stats.get('total_active', 0)}")
        print(f"   الإجمالي المحذوف: {stats.get('total_removed', 0)}")
        print(f"   الإجمالي العام: {stats.get('grand_total', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🌟 دمج النظام الكامل للحالات النهائية")
    print("=" * 80)
    
    try:
        # تنفيذ التكامل
        integrate_complete_final_status()
        
        # اختبار التكامل
        if test_complete_integration():
            print(f"\n🎉 تم الدمج الكامل بنجاح!")
            
            print(f"\n📋 الميزات المكتملة:")
            print(f"   ✅ الوفاة (نقل لجدول منفصل)")
            print(f"   ✅ التحويل الخارجي (نقل لجدول منفصل)")
            print(f"   ✅ العزل (نقل لجدول منفصل)")
            print(f"   ✅ التقاعد (نقل لجدول منفصل)")
            print(f"   ✅ الاستيداع (يبقى في جدول الموظفين)")
            print(f"   ✅ الاحتفاظ بجميع البيانات الأصلية في JSON")
            print(f"   ✅ عدم احتساب المحذوفين في العدد الكلي")
            print(f"   ✅ إمكانية الوصول لمعلومات الموظفين من جداول الحالات")
            
            print(f"\n🚀 لتشغيل النظام:")
            print(f"   python app.py")
            
            print(f"\n🌐 الروابط المهمة:")
            print(f"   - لوحة الحالات: http://localhost:5000/status/")
            print(f"   - المتوفين: http://localhost:5000/status/deceased")
            print(f"   - المحولين خارجياً: http://localhost:5000/final_status/external_transfers")
            print(f"   - المعزولين: http://localhost:5000/final_status/dismissed")
            print(f"   - المتقاعدين: http://localhost:5000/final_status/retired")
            
        else:
            print("❌ فشل في اختبار التكامل")
            
    except Exception as e:
        print(f"❌ خطأ في التكامل: {e}")

if __name__ == "__main__":
    main()